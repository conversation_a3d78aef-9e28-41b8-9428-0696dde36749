@baseUrl = http://localhost:3000
@contentType = application/json
@projectRef = dxodiiizyfzpueyvoaqr

@authToken = base64-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

###
# 파트너 수업 취소 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/partner/classes/550e8400-e29b-41d4-a716-446655440000/schedules/75/cancel
Content-Type: {{contentType}}

{
  "cancelReason": "강사 개인 사정으로 인한 취소"
}

###
# 파트너 수업 취소 (인증 포함)
POST {{baseUrl}}/api/partner/classes/912241a9-a7f1-4fdd-97bb-9955ee6efb0c/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": "강사 개인 사정으로 인한 취소"
}

###
# 유효하지 않은 스케줄 그룹 ID (400 에러)
POST {{baseUrl}}/api/partner/classes/550e8400-e29b-41d4-a716-446655440000/schedules/invalid/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": "테스트 취소"
}

###
# 필수 필드 누락 (400 에러)
POST {{baseUrl}}/api/partner/classes/550e8400-e29b-41d4-a716-446655440000/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{}

###
# 빈 취소 사유 (400 에러)
POST {{baseUrl}}/api/partner/classes/550e8400-e29b-41d4-a716-446655440000/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": ""
}

###
# 존재하지 않는 클래스 ID (404 에러)
POST {{baseUrl}}/api/partner/classes/00000000-0000-0000-0000-000000000000/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": "존재하지 않는 클래스"
}

###
# 다른 파트너 클래스 취소 시도 (403 에러)
POST {{baseUrl}}/api/partner/classes/other-partner-class-id/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": "다른 파트너 클래스 접근"
}

###
# 장기간 취소 사유 테스트
POST {{baseUrl}}/api/partner/classes/550e8400-e29b-41d4-a716-446655440000/schedules/75/cancel
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "cancelReason": "스튜디오 시설 점검으로 인한 불가피한 취소입니다. 수강생분들께 죄송하며, 빠른 시일 내에 보강 수업을 안내드리겠습니다."
}