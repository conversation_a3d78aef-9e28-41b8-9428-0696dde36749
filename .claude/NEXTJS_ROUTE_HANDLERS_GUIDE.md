# Next.js Route Handlers Guide for ShallWe

## 📋 문서 개요

**작성 기준**: Next.js 15 공식 문서 + ShallWe 프로젝트 실제 구현 패턴  
**마지막 업데이트**: 2025년 7월 19일  
**대상 독자**: 백엔드 개발자, API 개발자, 풀스택 개발자

## 🎯 Route Handlers란?

Route Handlers는 Next.js App Router에서 사용자 정의 요청 핸들러를 생성할 수 있게 해주는 기능입니다. Web [Request](https://developer.mozilla.org/docs/Web/API/Request)와 [Response](https://developer.mozilla.org/docs/Web/API/Response) API를 사용하여 RESTful API를 구축할 수 있습니다.

### 주요 특징
- **표준 Web API**: Request/Response 객체 사용
- **HTTP 메서드 지원**: GET, POST, PUT, PATCH, DELETE, HEAD, OPTIONS
- **파일 기반 라우팅**: 폴더 구조로 API 엔드포인트 정의
- **TypeScript 완전 지원**: 타입 안전성 보장
- **미들웨어 통합**: 인증, 로깅 등 쉽게 구현

## 🏗️ 기본 구조

### 파일 명명 규칙
Route Handler는 `route.ts` (또는 `route.js`) 파일로 작성합니다.

```typescript
// app/api/hello/route.ts
export async function GET() {
  return Response.json({ message: 'Hello World' });
}
```

### 지원되는 HTTP 메서드
```typescript
// app/api/example/route.ts
export async function GET(request: Request) {}
export async function POST(request: Request) {}
export async function PUT(request: Request) {}
export async function PATCH(request: Request) {}
export async function DELETE(request: Request) {}
export async function HEAD(request: Request) {}
export async function OPTIONS(request: Request) {}
```

## 📂 ShallWe 프로젝트 API 구조

### 현재 구현된 API 엔드포인트
```
src/app/api/
├── instructor/
│   ├── onboarding/route.ts          # GET, POST
│   ├── profile/route.ts             # GET, PUT
│   ├── dashboard/route.ts           # GET
│   ├── classes/
│   │   ├── route.ts                 # GET, POST
│   │   └── [id]/route.ts           # GET, PATCH, DELETE
│   ├── schedule-groups/
│   │   └── [id]/route.ts           # GET, PUT
│   └── studios/
│       ├── route.ts                 # GET
│       └── [id]/route.ts           # GET
├── class-enrollments/
│   └── route.ts                     # GET, POST
├── classes/
│   └── [id]/route.ts               # GET
├── payment/
│   └── route.ts                     # POST
├── profile/
│   └── route.ts                     # GET, PUT
└── stations/
    └── route.ts                     # GET
```

## 💡 ShallWe 프로젝트 패턴 분석

### 1. 인증 패턴
모든 API에서 일관된 인증 처리를 사용합니다:

```typescript
// 표준 인증 패턴
export async function GET(request: NextRequest) {
  try {
    const { supabase } = createClient(request);

    // 사용자 인증 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { message: '인증이 필요합니다.' },
        { status: 401 }
      );
    }

    // API 로직 구현...
  } catch (error) {
    console.error('API 오류:', error);
    return NextResponse.json(
      { message: '서버 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
```

### 2. 강사 권한 확인 패턴
강사 전용 API에서 사용하는 권한 확인:

```typescript
// 강사 정보 확인
const instructor = await db
  .select()
  .from(instructors)
  .where(eq(instructors.member_id, user.id))
  .limit(1);

if (!instructor.length) {
  return NextResponse.json(
    { message: '강사 정보를 찾을 수 없습니다.' },
    { status: 404 }
  );
}

const instructorData = instructor[0];
```

### 3. 동적 라우트 파라미터 처리
```typescript
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Next.js 15에서는 params가 Promise입니다
  const groupId = (await params).id;
  
  // API 로직...
}
```

### 4. 데이터베이스 트랜잭션 패턴
```typescript
const result = await db.transaction(async tx => {
  // 1. 데이터 업데이트
  const updatedGroup = await tx
    .update(class_schedule_groups)
    .set({
      group_start_date,
      group_end_date,
      updated_at: new Date(),
    })
    .where(eq(class_schedule_groups.id, groupId))
    .returning();

  // 2. 관련 데이터 자동 계산 및 업데이트
  const allGroups = await tx.select(/* ... */);
  const calculatedDates = calculateTemplateClassDates(allGroups);

  // 3. 계산된 값으로 템플릿 업데이트
  if (calculatedDates.class_start_date && calculatedDates.class_end_date) {
    await tx.update(class_templates).set(/* ... */);
  }

  return updatedGroup[0];
});
```

## 🔧 Request 처리

### 1. 요청 본문 읽기
```typescript
// JSON 데이터
export async function POST(request: Request) {
  const body = await request.json();
  const { name, email } = body;
  
  return Response.json({ success: true });
}

// FormData
export async function POST(request: Request) {
  const formData = await request.formData();
  const name = formData.get('name');
  const email = formData.get('email');
  
  return Response.json({ name, email });
}
```

### 2. 쿼리 파라미터 처리
```typescript
import { type NextRequest } from 'next/server';

export function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = searchParams.get('page') || '1';
  
  // /api/search?query=hello&page=2
  return Response.json({ query, page });
}
```

### 3. 헤더 처리
```typescript
import { headers } from 'next/headers';

export async function GET(request: NextRequest) {
  const headersList = await headers();
  const referer = headersList.get('referer');
  
  // 또는 직접 Request 객체에서
  const authorization = request.headers.get('authorization');
  
  return Response.json({ referer, authorization });
}
```

### 4. 쿠키 처리
```typescript
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const cookieStore = await cookies();
  const token = cookieStore.get('token');
  
  // 쿠키 설정
  cookieStore.set('session', 'value', {
    httpOnly: true,
    secure: true,
    maxAge: 60 * 60 * 24 * 7, // 7일
  });
  
  return Response.json({ token: token?.value });
}
```

## 📤 Response 처리

### 1. JSON 응답
```typescript
export async function GET() {
  return Response.json({
    message: '성공',
    data: { id: 1, name: 'John' },
  });
}

// 상태 코드와 함께
export async function POST() {
  return Response.json(
    { message: '생성됨' },
    { status: 201 }
  );
}
```

### 2. 에러 응답 패턴
```typescript
// ShallWe 프로젝트 에러 응답 패턴
export async function GET() {
  try {
    // API 로직...
  } catch (error) {
    console.error('API 오류:', error);
    return NextResponse.json(
      { message: '서버 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

// 유효성 검사 실패
if (!requiredField) {
  return NextResponse.json(
    { message: '필수 필드가 누락되었습니다.' },
    { status: 400 }
  );
}

// 권한 없음
if (user.id !== resourceOwnerId) {
  return NextResponse.json(
    { message: '권한이 없습니다.' },
    { status: 403 }
  );
}

// 리소스 없음
if (!resource) {
  return NextResponse.json(
    { message: '리소스를 찾을 수 없습니다.' },
    { status: 404 }
  );
}
```

### 3. 리다이렉트
```typescript
import { redirect } from 'next/navigation';

export async function GET() {
  redirect('https://nextjs.org/');
}

// 또는 NextResponse 사용
import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.redirect(new URL('/dashboard', request.url));
}
```

### 4. 스트리밍 응답
```typescript
export async function GET() {
  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    start(controller) {
      controller.enqueue(encoder.encode('데이터 시작\n'));
    },
    pull(controller) {
      // 추가 데이터 전송
      controller.enqueue(encoder.encode('더 많은 데이터\n'));
      controller.close();
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/plain',
      'Transfer-Encoding': 'chunked',
    },
  });
}
```

## 🔒 보안 및 CORS

### 1. CORS 설정
```typescript
export async function GET(request: Request) {
  return new Response('Hello, Next.js!', {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

export async function OPTIONS(request: Request) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
```

### 2. 웹훅 처리
```typescript
export async function POST(request: Request) {
  try {
    const payload = await request.text();
    const signature = request.headers.get('signature');
    
    // 서명 검증
    if (!verifySignature(payload, signature)) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    // 웹훅 처리
    await processWebhook(JSON.parse(payload));
    
    return new Response('Success!', { status: 200 });
  } catch (error) {
    return new Response(`Webhook error: ${error.message}`, {
      status: 400,
    });
  }
}
```

## ⚡ 성능 최적화

### 1. 캐싱 설정
```typescript
// Route Segment Config를 사용한 캐싱
export const dynamic = 'auto';
export const revalidate = 60; // 60초마다 재검증

export async function GET() {
  const data = await fetch('https://api.example.com/data', {
    next: { revalidate: 3600 } // 1시간 캐시
  });
  
  return Response.json(await data.json());
}
```

### 2. Edge Runtime 사용
```typescript
export const runtime = 'edge';

export async function GET() {
  // Edge Runtime에서 실행
  return Response.json({ message: 'Fast response from edge!' });
}
```

## 🧪 테스팅

### 1. 단위 테스트 예시
```typescript
// __tests__/api/instructor/profile.test.ts
import { GET } from '@/app/api/instructor/profile/route';
import { NextRequest } from 'next/server';

describe('/api/instructor/profile', () => {
  it('should return instructor profile', async () => {
    const request = new NextRequest('http://localhost:3000/api/instructor/profile');
    
    // 인증된 요청 시뮬레이션
    const response = await GET(request);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data).toHaveProperty('instructor');
  });

  it('should return 401 for unauthenticated request', async () => {
    const request = new NextRequest('http://localhost:3000/api/instructor/profile');
    
    // 미인증 요청 시뮬레이션
    const response = await GET(request);
    
    expect(response.status).toBe(401);
  });
});
```

## 📝 베스트 프랙티스

### 1. 코드 구조화
```typescript
// utils/api-helpers.ts
export async function authenticateUser(request: NextRequest) {
  const { supabase } = createClient(request);
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error || !user) {
    throw new Error('Unauthorized');
  }
  
  return user;
}

export function createErrorResponse(message: string, status: number) {
  return NextResponse.json({ message }, { status });
}

// route.ts에서 사용
export async function GET(request: NextRequest) {
  try {
    const user = await authenticateUser(request);
    // API 로직...
  } catch (error) {
    if (error.message === 'Unauthorized') {
      return createErrorResponse('인증이 필요합니다.', 401);
    }
    return createErrorResponse('서버 오류가 발생했습니다.', 500);
  }
}
```

### 2. 타입 안전성
```typescript
// types/api.ts
export interface CreateClassRequest {
  title: string;
  description: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  price_per_session: number;
  max_capacity: number;
  studio_id: string;
}

export interface ApiResponse<T> {
  message: string;
  data?: T;
  error?: string;
}

// route.ts
export async function POST(request: NextRequest): Promise<Response> {
  const body: CreateClassRequest = await request.json();
  
  // 타입 안전한 API 구현
  const response: ApiResponse<ClassTemplate> = {
    message: '클래스가 생성되었습니다.',
    data: createdClass,
  };
  
  return NextResponse.json(response);
}
```

### 3. 유효성 검사
```typescript
import { z } from 'zod';

const CreateClassSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(10).max(1000),
  price_per_session: z.number().positive(),
  max_capacity: z.number().min(1).max(50),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = CreateClassSchema.parse(body);
    
    // 검증된 데이터로 API 로직 실행
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: '입력 데이터가 유효하지 않습니다.', errors: error.errors },
        { status: 400 }
      );
    }
  }
}
```

## 🚨 주의사항

### 1. Next.js 15 변경사항
- `params`가 이제 Promise입니다: `const { id } = await params`
- 기본 캐싱이 static에서 dynamic으로 변경됨

### 2. 파일 명명 규칙
- Route Handler 파일은 반드시 `route.ts` 또는 `route.js`여야 함
- `page.tsx`와 같은 폴더에 `route.ts`를 둘 수 없음

### 3. 메모리 및 성능
- 대용량 파일 업로드 시 스트리밍 사용 권장
- 데이터베이스 연결 풀링 고려
- 적절한 캐싱 전략 수립

## 🔗 관련 문서

- [Next.js Route Handlers 공식 문서](https://nextjs.org/docs/app/api-reference/file-conventions/route)
- [ShallWe API 문서](./API_DOCUMENTATION.md)
- [ShallWe 기술 아키텍처](./TECHNICAL_ARCHITECTURE.md)
- [Supabase 인증 가이드](./SUPABASE_AUTH_GUIDE.md)

---

**이 문서는 Next.js 15와 ShallWe 프로젝트의 실제 구현을 기반으로 작성되었습니다.**  
**마지막 업데이트**: 2025년 7월 19일
