# Next.js Project Structure Guide

This guide provides a comprehensive overview of Next.js project structure conventions and best practices for organizing your ShallWe app.

## Table of Contents

1. [Folder and File Conventions](#folder-and-file-conventions)
2. [Layouts and Pages](#layouts-and-pages)
3. [Project Organization Strategies](#project-organization-strategies)
4. [Best Practices](#best-practices)
5. [ShallWe Specific Recommendations](#shallwe-specific-recommendations)

## Folder and File Conventions

### Top-Level Folders

| Folder   | Purpose                                         |
| -------- | ----------------------------------------------- |
| `app`    | App Router (current project structure)          |
| `pages`  | Pages Router (legacy, not used in this project) |
| `public` | Static assets to be served                      |
| `src`    | Optional application source folder              |

### Top-Level Files

| File                 | Purpose                                 |
| -------------------- | --------------------------------------- |
| `next.config.js`     | Configuration file for Next.js          |
| `package.json`       | Project dependencies and scripts        |
| `instrumentation.ts` | OpenTelemetry and Instrumentation file  |
| `middleware.ts`      | Next.js request middleware              |
| `.env`               | Environment variables                   |
| `.env.local`         | Local environment variables             |
| `.env.production`    | Production environment variables        |
| `.env.development`   | Development environment variables       |
| `.eslintrc.json`     | Configuration file for ESLint           |
| `.gitignore`         | Git files and folders to ignore         |
| `next-env.d.ts`      | TypeScript declaration file for Next.js |
| `tsconfig.json`      | Configuration file for TypeScript       |
| `postcss.config.mjs` | PostCSS configuration                   |

### App Router Special Files

| File           | Extensions          | Purpose                      |
| -------------- | ------------------- | ---------------------------- |
| `layout`       | `.js` `.jsx` `.tsx` | Layout component             |
| `page`         | `.js` `.jsx` `.tsx` | Page component               |
| `loading`      | `.js` `.jsx` `.tsx` | Loading UI                   |
| `not-found`    | `.js` `.jsx` `.tsx` | Not found UI                 |
| `error`        | `.js` `.jsx` `.tsx` | Error UI                     |
| `global-error` | `.js` `.jsx` `.tsx` | Global error UI              |
| `route`        | `.js` `.ts`         | API endpoint                 |
| `template`     | `.js` `.jsx` `.tsx` | Re-rendered layout           |
| `default`      | `.js` `.jsx` `.tsx` | Parallel route fallback page |

### Routing Conventions

#### Basic Routes

- `folder/` - Route segment
- `folder/folder/` - Nested route segment

#### Dynamic Routes

- `[folder]/` - Dynamic route segment
- `[...folder]/` - Catch-all route segment
- `[[...folder]]/` - Optional catch-all route segment

#### Special Folders

- `(folder)/` - Route group (doesn't affect URL path)
- `_folder/` - Private folder (excluded from routing)

#### Parallel and Intercepted Routes

- `@folder/` - Named slot for parallel routes
- `(.)folder/` - Intercept same level
- `(..)folder/` - Intercept one level above
- `(..)(..)folder/` - Intercept two levels above
- `(...)folder/` - Intercept from root

### Metadata File Conventions

#### App Icons

- `favicon.ico` - Favicon file
- `icon.ico/.jpg/.jpeg/.png/.svg` - App icon file
- `apple-icon.jpg/.jpeg/.png` - Apple app icon file

#### SEO Files

- `sitemap.xml` - Sitemap file
- `robots.txt` - Robots file

#### Open Graph and Twitter Images

- `opengraph-image.jpg/.jpeg/.png/.gif` - Open Graph image
- `twitter-image.jpg/.jpeg/.png/.gif` - Twitter image

## Layouts and Pages

Next.js uses file-system based routing where folders define route segments and files create the UI for those routes.

### Creating Pages

A **page** is UI that is rendered on a specific route. Create a page by adding a `page.tsx` file inside a folder in the `app` directory.

```tsx
// app/page.tsx - Creates the home page (/)
export default function HomePage() {
  return <h1>Welcome to ShallWe!</h1>;
}

// app/classes/page.tsx - Creates /classes route
export default function ClassesPage() {
  return <h1>Browse Classes</h1>;
}
```

### Creating Layouts

A **layout** is UI that is shared between multiple pages. Layouts preserve state, remain interactive, and do not re-render on navigation.

```tsx
// app/layout.tsx - Root layout (required)
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en'>
      <body>
        <header>ShallWe Navigation</header>
        <main>{children}</main>
        <footer>© 2025 ShallWe</footer>
      </body>
    </html>
  );
}

// app/classes/layout.tsx - Nested layout for classes
export default function ClassesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='classes-container'>
      <aside>Class Filters</aside>
      <section>{children}</section>
    </div>
  );
}
```

### Nested Routes

Create nested routes by nesting folders:

```
app/
├── page.tsx              # / (home)
├── classes/
│   ├── page.tsx          # /classes
│   ├── layout.tsx        # Layout for all /classes/* routes
│   └── [id]/
│       ├── page.tsx      # /classes/[id]
│       └── booking/
│           └── page.tsx  # /classes/[id]/booking
```

### Dynamic Routes

Use square brackets for dynamic route segments:

```tsx
// app/classes/[id]/page.tsx
export default async function ClassDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const classData = await getClass(id);

  return (
    <div>
      <h1>{classData.title}</h1>
      <p>{classData.description}</p>
    </div>
  );
}
```

### Search Parameters

Access search parameters in Server Components:

```tsx
// app/classes/page.tsx
export default async function ClassesPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const { category, location } = await searchParams;
  const classes = await getClasses({ category, location });

  return (
    <div>
      <h1>Classes</h1>
      {/* Render filtered classes */}
    </div>
  );
}
```

### Layout Hierarchy

Layouts nest automatically:

1. `app/layout.tsx` (root layout)
2. `app/classes/layout.tsx` (classes layout)
3. `app/classes/[id]/page.tsx` (class detail page)

The component hierarchy becomes:

```
RootLayout
└── ClassesLayout
    └── ClassDetailPage
```

### Navigation Between Pages

Use the `Link` component for client-side navigation:

```tsx
import Link from 'next/link';

export default function ClassCard({ classItem }) {
  return (
    <div className='class-card'>
      <h3>{classItem.title}</h3>
      <Link href={`/classes/${classItem.id}`}>View Details</Link>
      <Link href={`/classes/${classItem.id}/booking`}>Book Now</Link>
    </div>
  );
}
```

### ShallWe App Layout Examples

For your mobile app, consider this layout structure:

```
app/
├── layout.tsx                    # Root layout with mobile viewport
├── page.tsx                      # Home page with class recommendations
├── (main)/                       # Route group for main app
│   ├── layout.tsx               # Layout with navigation
│   ├── classes/
│   │   ├── page.tsx             # Class listing
│   │   ├── [id]/
│   │   │   ├── page.tsx         # Class details with tabs
│   │   │   └── booking/
│   │   │       └── page.tsx     # Booking page
│   └── profile/
│       ├── page.tsx             # Profile with tabs
│       └── bookings/
│           └── page.tsx         # Booking history
├── (auth)/                       # Route group for auth
│   ├── login/
│   │   └── page.tsx
│   └── register/
│       └── page.tsx
└── (modals)/                     # Route group for modals
    ├── payment/
    │   └── page.tsx             # Payment modal
    └── confirmation/
        └── page.tsx             # Confirmation modal
```

## Project Organization Strategies

### 1. Store Project Files Outside of App (Recommended for ShallWe)

Keep the `app` directory purely for routing and store shared code in the root:

```
project/
├── app/                    # Routing only
│   ├── layout.tsx
│   ├── page.tsx
│   └── (routes)/
├── components/             # Shared components
│   ├── ui/
│   ├── auth/
│   └── stories/
├── lib/                    # Utilities and configurations
│   ├── utils.ts
│   ├── db/
│   └── supabase/
├── public/                 # Static assets
└── docs/                   # Documentation
```

### 2. Store Project Files in Top-Level App Folders

Store shared code in the root of the `app` directory:

```
app/
├── _components/           # Private components folder
├── _lib/                  # Private utilities folder
├── (dashboard)/
│   ├── layout.tsx
│   └── page.tsx
└── globals.css
```

### 3. Split Project Files by Feature or Route

Organize code by feature with route-specific components:

```
app/
├── _components/           # Globally shared components
├── (dashboard)/
│   ├── _components/       # Dashboard-specific components
│   ├── layout.tsx
│   └── page.tsx
└── (auth)/
    ├── _components/       # Auth-specific components
    ├── login/
    └── register/
```

## Best Practices

### Component Hierarchy

Special files are rendered in this order:

1. `layout.js`
2. `template.js`
3. `error.js` (React error boundary)
4. `loading.js` (React suspense boundary)
5. `not-found.js` (React error boundary)
6. `page.js` or nested `layout.js`

### Private Folders

Use underscore prefix for private implementation details:

- `_components/` - Internal components
- `_utils/` - Internal utilities
- `_hooks/` - Internal custom hooks

### Route Groups

Use parentheses to organize routes without affecting URLs:

- `(marketing)/` - Marketing pages group
- `(dashboard)/` - Dashboard pages group
- `(auth)/` - Authentication pages group

### File Naming Conventions

- Use **kebab-case** for file and folder names
- Use **PascalCase** for React components
- Use **camelCase** for utility functions
- Prefix private folders with underscore `_`

## ShallWe Specific Recommendations

Based on your current project structure, here are specific recommendations:

### Current Structure Analysis

Your project follows the **"Store Project Files Outside of App"** pattern, which is excellent for maintainability.

### Recommended Improvements

1. **Organize components by feature:**

```
src/components/
├── ui/                    # Reusable UI components (current)
├── auth/                  # Authentication components (current)
├── class/                 # Class-related components
│   ├── ClassCard.tsx
│   ├── ClassDetail.tsx
│   └── ClassSchedule.tsx
├── booking/               # Booking-related components
│   ├── BookingCard.tsx
│   ├── PaymentModal.tsx
│   └── BookingStatus.tsx
├── profile/               # User profile components
│   ├── UserStats.tsx
│   ├── BookingHistory.tsx
│   └── ProfileSettings.tsx
└── stories/               # Storybook stories (current)
```

2. **Add route groups for better organization:**

```
src/app/
├── (main)/                # Main app pages
│   ├── page.tsx           # Home/recommendations
│   ├── classes/
│   ├── booking/
│   └── profile/
├── (auth)/                # Authentication pages
│   ├── login/
│   └── register/
└── (admin)/               # Admin pages (if needed)
```

3. **Enhance lib organization:**

```
src/lib/
├── utils.ts               # General utilities (current)
├── constants/             # App constants
│   ├── routes.ts
│   ├── api.ts
│   └── ui.ts
├── hooks/                 # Custom React hooks
│   ├── useAuth.ts
│   ├── useBooking.ts
│   └── useClasses.ts
├── types/                 # TypeScript type definitions
│   ├── auth.ts
│   ├── class.ts
│   └── booking.ts
├── db/                    # Database utilities (current)
└── supabase/              # Supabase client (current)
```

4. **Add API organization:**

```
src/app/api/
├── auth/
│   └── route.ts
├── classes/
│   ├── route.ts
│   └── [id]/
│       └── route.ts
├── bookings/
│   └── route.ts
└── users/
    └── route.ts
```

### Mobile App Specific Considerations

For your mobile-focused ShallWe app:

1. **Use route groups for app sections:**
   - `(tabs)/` - Bottom tab navigation pages
   - `(modals)/` - Modal presentations
   - `(auth)/` - Authentication flow

2. **Organize components by screen:**
   - `components/screens/` - Full-screen components
   - `components/cards/` - Card components
   - `components/modals/` - Modal components

3. **Consider creating a design system:**
   - `components/ui/` - Base UI components
   - `lib/design-tokens/` - Colors, spacing, typography
   - `components/icons/` - Icon components

### Documentation Structure

Enhance your docs folder:

```
docs/
├── NEXTJS_PROJECT_STRUCTURE_GUIDE.md  # This guide
├── STORYBOOK_GUIDE.md                 # Current
├── API_DOCUMENTATION.md               # API endpoints
├── COMPONENT_GUIDELINES.md            # Component standards
├── DEPLOYMENT_GUIDE.md                # Deployment instructions
└── DEVELOPMENT_SETUP.md               # Local setup guide
```

## Conclusion

Your current project structure is well-organized and follows Next.js best practices. The recommendations above will help maintain clean, scalable code as your ShallWe app grows. Remember to:

- Keep routing logic in the `app` directory
- Store shared code outside of `app`
- Use private folders for internal implementations
- Organize components by feature/domain
- Maintain consistent naming conventions
- Document your architectural decisions

For more detailed information, refer to the [official Next.js documentation](https://nextjs.org/docs/app/getting-started/project-structure) and [Layouts and Pages guide](https://nextjs.org/docs/app/getting-started/layouts-and-pages).
