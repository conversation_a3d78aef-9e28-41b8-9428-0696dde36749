---
inclusion: always
---

# Technology Stack & Development Guidelines

## Core Technologies

- **Next.js 15.3.5** with App Router - Use server components by default, client components only when needed
- **React 19** - Leverage concurrent features and new hooks
- **TypeScript 5** - Strict mode enabled, use proper typing for all functions and components
- **Tailwind CSS 4** - Utility-first styling with custom design system variables
- **shadcn/ui** - Component library built on Radix UI primitives

## Database & Backend

- **PostgreSQL** with **Drizzle ORM** - Type-safe database operations
- **Supabase** - Authentication, real-time subscriptions, and database hosting
- **Kakao Login** - Primary social authentication method for Korean users

## Code Architecture Patterns

### Component Structure

- Use server components for data fetching and static content
- Client components only for interactivity (`'use client'` directive)
- Co-locate related components in feature directories
- Export types and interfaces alongside components

### API Design

- RESTful endpoints in `src/app/api/` following resource-based naming
- Use TypeScript for request/response types
- Implement proper error handling with consistent error responses
- Validate inputs using Zod schemas

### State Management

- **TanStack Query (React Query)** - Server state management, caching, and synchronization
- **Server State**: TanStack Query for API data fetching, caching, and background updates
- **Client State**: Zustand for global client state
- **Form State**: React Hook Form with Zod validation
- Minimize client state, prefer server state when possible

## Development Workflow

### Code Quality Standards

```bash
npm run dev          # Development with Turbopack
npm run lint         # ESLint with Next.js rules
npm run format       # Prettier with Tailwind plugin
npm run check-types  # TypeScript strict checking
```

### Database Operations

```bash
npx drizzle-kit generate # Create migrations from schema changes
npx drizzle-kit migrate  # Apply migrations to database
npx drizzle-kit studio   # Visual database browser
```

## Coding Conventions

### File Organization

- **Pages**: `page.tsx` in App Router directories
- **Components**: PascalCase files (e.g., `BookingCard.tsx`)
- **Utilities**: camelCase files (e.g., `dateUtils.ts`)
- **Types**: Export from component files or dedicated `types.ts`

### Import Standards

- Use `@/` alias for all internal imports
- Group imports: external → internal → relative
- Prefer named exports over default exports for utilities

### TypeScript Guidelines

- Use interfaces for object shapes, types for unions/primitives
- Implement proper error boundaries and loading states
- Type all API responses and database queries
- Use generic types for reusable components

### Styling Approach

- Use Tailwind utility classes with design system variables
- Implement responsive design with mobile-first approach
- Support both light and dark themes using CSS custom properties
- Use `cn()` utility for conditional class composition

## Performance Considerations

- Implement proper loading states and error boundaries
- Use React.Suspense for async components
- Optimize images with Next.js Image component
- Implement proper caching strategies for API calls
