import { z } from 'zod';

/**
 * Member 인증 관련 유효성 검증 스키마
 * 임시 기능: 심사자용 ID/PW 로그인
 */

// 이메일 스키마
const EmailSchema = z
  .string()
  .min(1, '이메일을 입력해주세요.')
  .email('올바른 이메일 형식을 입력해주세요.')
  .max(100, '이메일은 100자 이하로 입력해주세요.');

// 비밀번호 스키마 (로그인용)
const LoginPasswordSchema = z
  .string()
  .min(1, '비밀번호를 입력해주세요.');

// 비밀번호 스키마 (회원가입용)
const RegisterPasswordSchema = z
  .string()
  .min(8, '비밀번호는 8자 이상이어야 합니다.')
  .max(100, '비밀번호는 100자 이하로 입력해주세요.')
  .refine(
    (password) => {
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
      return hasLetter && hasNumber && hasSpecialChar;
    },
    '비밀번호는 영문, 숫자, 특수문자를 포함해야 합니다.'
  );

// 닉네임 스키마
const NicknameSchema = z
  .string()
  .min(2, '닉네임은 2자 이상 입력해주세요.')
  .max(20, '닉네임은 20자 이하로 입력해주세요.')
  .refine(
    (nickname) => /^[가-힣a-zA-Z0-9\s]+$/.test(nickname),
    '닉네임은 한글, 영문, 숫자만 입력 가능합니다.'
  )
  .transform((val) => val.trim());

// Member 로그인 요청 스키마
export const MemberLoginSchema = z.object({
  email: EmailSchema,
  password: LoginPasswordSchema,
});

// Member 회원가입 요청 스키마
export const MemberRegisterSchema = z.object({
  email: EmailSchema,
  password: RegisterPasswordSchema,
  passwordConfirm: z.string().min(1, '비밀번호 확인을 입력해주세요.'),
  nickname: NicknameSchema,
}).refine(
  (data) => data.password === data.passwordConfirm,
  {
    message: '비밀번호가 일치하지 않습니다.',
    path: ['passwordConfirm'],
  }
);

// Member 로그인 응답 스키마
export const MemberLoginResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  member: z.object({
    id: z.string(),
    email: z.string(),
    nickname: z.string(),
  }).optional(),
  error: z.string().optional(),
});

// Member 회원가입 응답 스키마
export const MemberRegisterResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  member: z.object({
    id: z.string(),
    email: z.string(),
    nickname: z.string(),
  }).optional(),
  error: z.string().optional(),
  errors: z.array(z.object({
    field: z.string(),
    message: z.string(),
  })).optional(),
});

// TypeScript 타입 추출
export type MemberLoginRequest = z.infer<typeof MemberLoginSchema>;
export type MemberRegisterRequest = z.infer<typeof MemberRegisterSchema>;
export type MemberLoginResponse = z.infer<typeof MemberLoginResponseSchema>;
export type MemberRegisterResponse = z.infer<typeof MemberRegisterResponseSchema>;

// 에러 메시지 헬퍼
export function formatZodErrors(error: z.ZodError): Array<{ field: string; message: string }> {
  return error.issues.map((issue) => ({
    field: issue.path.join('.'),
    message: issue.message,
  }));
}

// 스키마 검증 헬퍼
export function validateMemberLogin(data: unknown) {
  return MemberLoginSchema.safeParse(data);
}

export function validateMemberRegister(data: unknown) {
  return MemberRegisterSchema.safeParse(data);
}