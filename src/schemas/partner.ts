import { z } from 'zod';
import { PARTNER_PHONE_REGEX, PARTNER_PHONE_DISPLAY_REGEX, PARTNER_NAME_REGEX } from '@/types/partner';

/**
 * 파트너 회원가입 유효성 검증 스키마
 */

// 이메일 스키마
const EmailSchema = z
  .string()
  .min(1, '이메일을 입력해주세요.')
  .email('올바른 이메일 형식을 입력해주세요.')
  .max(100, '이메일은 100자 이하로 입력해주세요.');

// 비밀번호 스키마
const PasswordSchema = z
  .string()
  .min(8, '비밀번호는 8자 이상이어야 합니다.')
  .max(100, '비밀번호는 100자 이하로 입력해주세요.')
  .refine(
    (password) => {
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
      return hasLetter && hasNumber && hasSpecialChar;
    },
    '비밀번호는 영문, 숫자, 특수문자를 포함해야 합니다.'
  );

// 담당자명 스키마
const ContactNameSchema = z
  .string()
  .min(1, '담당자명을 입력해주세요.')
  .max(50, '담당자명은 50자 이하로 입력해주세요.')
  .refine(
    (name) => PARTNER_NAME_REGEX.test(name),
    '담당자명은 한글 또는 영문만 입력 가능합니다.'
  )
  .transform((val) => val.trim());

// 연락처 스키마 (서버용 - 숫자만)
const ContactPhoneSchema = z
  .string()
  .min(1, '연락처를 입력해주세요.')
  .refine(
    (phone) => PARTNER_PHONE_REGEX.test(phone),
    '연락처는 010으로 시작하는 11자리 숫자로 입력해주세요.'
  );

// 연락처 스키마 (프론트엔드용 - 하이픈 포함)
const ContactPhoneDisplaySchema = z
  .string()
  .min(1, '연락처를 입력해주세요.')
  .refine(
    (phone) => PARTNER_PHONE_DISPLAY_REGEX.test(phone),
    '연락처는 010-0000-0000 형식으로 입력해주세요.'
  );

// 이용약관 동의 항목 스키마
const TermsAgreementSchema = z.object({
  type: z.string().min(1, '약관 유형을 입력해주세요.'),
  agreed: z.boolean(),
});

// 담당자 정보 스키마 (서버용)
const ContactInfoSchema = z.object({
  name: ContactNameSchema,
  phone: ContactPhoneSchema,
});

// 담당자 정보 스키마 (프론트엔드용 - 하이픈 포함)
const ContactInfoDisplaySchema = z.object({
  name: ContactNameSchema,
  phone: ContactPhoneDisplaySchema,
});

// 파트너 회원가입 요청 스키마
export const PartnerRegisterSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
  contactInfo: ContactInfoSchema,
  terms_agreements: z.array(TermsAgreementSchema).min(1, '이용약관 동의가 필요합니다.'),
});

// 파트너 로그인 요청 스키마
export const PartnerLoginSchema = z.object({
  email: EmailSchema,
  password: z.string().min(1, '비밀번호를 입력해주세요.'),
  rememberMe: z.boolean().optional().default(false),
});

// 파트너 프로필 업데이트 스키마
export const PartnerProfileUpdateSchema = z.object({
  contact_name: ContactNameSchema.optional(),
  contact_phone: ContactPhoneSchema.optional(),
}).refine(
  (data) => data.contact_name !== undefined || data.contact_phone !== undefined,
  '수정할 정보를 하나 이상 입력해주세요.'
);

// 파트너 회원가입 폼 스키마 (비밀번호 확인 포함, 프론트엔드용 - 하이픈 포함)
export const PartnerSignupFormSchema = z.object({
  email: EmailSchema,
  password: PasswordSchema,
  passwordConfirm: z.string().min(1, '비밀번호 확인을 입력해주세요.'),
  contactInfo: ContactInfoDisplaySchema,
  terms_agreements: z.array(TermsAgreementSchema).min(1, '이용약관 동의가 필요합니다.'),
}).refine(
  (data) => data.password === data.passwordConfirm,
  {
    message: '비밀번호가 일치하지 않습니다.',
    path: ['passwordConfirm'],
  }
);

// 파트너 로그인 폼 스키마
export const PartnerLoginFormSchema = z.object({
  email: EmailSchema,
  password: z.string().min(1, '비밀번호를 입력해주세요.'),
  rememberMe: z.boolean().optional().default(false),
});

// 파트너 상품 상태 스키마
export const PartnerStatusSchema = z.enum(['PENDING', 'ACTIVE', 'SUSPENDED', 'REJECTED']);

// 파트너 회원가입 응답 스키마
export const PartnerRegisterResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    partnerId: z.uuid(),
    status: PartnerStatusSchema,
  }).optional(),
  error: z.string().optional(),
  errors: z.array(z.object({
    field: z.string(),
    message: z.string(),
  })).optional(),
});

// TypeScript 타입 추출
export type PartnerRegisterRequest = z.infer<typeof PartnerRegisterSchema>;
export type PartnerLoginRequest = z.infer<typeof PartnerLoginSchema>;
export type PartnerProfileUpdateRequest = z.infer<typeof PartnerProfileUpdateSchema>;
export type PartnerSignupFormData = z.infer<typeof PartnerSignupFormSchema>;
export type PartnerLoginFormData = z.infer<typeof PartnerLoginFormSchema>;
export type PartnerRegisterResponse = z.infer<typeof PartnerRegisterResponseSchema>;
export type PartnerStatusType = z.infer<typeof PartnerStatusSchema>;

// 에러 메시지 헬퍼
export function formatZodErrors(error: z.ZodError): Array<{ field: string; message: string }> {
  return error.issues.map((issue) => ({
    field: issue.path.join('.'),
    message: issue.message,
  }));
}

// 스키마 검증 헬퍼
export function validatePartnerRegister(data: unknown) {
  return PartnerRegisterSchema.safeParse(data);
}

export function validatePartnerLogin(data: unknown) {
  return PartnerLoginSchema.safeParse(data);
}

export function validatePartnerSignupForm(data: unknown) {
  return PartnerSignupFormSchema.safeParse(data);
}

export function validatePartnerLoginForm(data: unknown) {
  return PartnerLoginFormSchema.safeParse(data);
}

export function validatePartnerProfileUpdate(data: unknown) {
  return PartnerProfileUpdateSchema.safeParse(data);
}