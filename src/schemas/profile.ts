import { z } from 'zod';
import { PaymentStatusSchema } from './payment';

export const NextClassSchema = z.object({
  classId: z.string(),
  title: z.string(),
  date: z.string(),
  time: z.string(),
  location: z.string(),
});

export const UserStatsSchema = z.object({
  nickname: z.string(),
  totalClassDays: z.number(), // 지금까지 총 수업일수
  attendedDays: z.number(), // 출석일수
  nextClasses: z.array(NextClassSchema), // 다음 수업 일정 (최대 3개)
});

export const BookingStatusSchema = z.enum([
  'payment_pending', // 결제 대기
  'recruiting',      // 모집중
  'reserved',        // 예약 완료 (신청 완료)
  'in_progress',     // 진행중
  'completed',       // 완료
  'cancelled',       // 취소
]);

export const BookingSchema = z.object({
  // 식별자
  id: z.string(), // enrollmentId as id for components
  enrollmentId: z.string(),
  enrolledAt: z.string().datetime(),
  
  // 클래스 정보
  classId: z.string(),
  classTitle: z.string(),
  category: z.string(),
  level: z.string(),
  
  // 강사 정보
  coachName: z.string(), // instructorName as coachName for components
  instructorName: z.string(),
  
  // 스튜디오 정보
  studioName: z.string(),
  location: z.string().nullable(), // nearestStation
  
  // 스케줄 정보
  scheduleGroupId: z.number(),
  date: z.string().nullable(), // formatted date for display
  time: z.string().nullable(), // formatted time range for display
  startDate: z.string().nullable(),
  endDate: z.string().nullable(),
  sessionsPerWeek: z.number(),
  durationWeeks: z.number(),
  schedule: z.string().optional(), // e.g., "주 2회, 총 8회"
  maxParticipants: z.number(),
  
  // 금액 정보
  price: z.number(), // pricePerSession as price for components
  pricePerSession: z.number(),
  totalAmount: z.number(),
  depositAmount: z.number(),
  
  // 결제 정보
  payment: z.object({
    status: z.enum(['pending', 'processing', 'completed', 'failed']),
    paymentKey: z.string().optional(),
  }).optional(),
  paymentDueDate: z.string().optional(),
  
  // 상태
  status: BookingStatusSchema,
});

export const UserProfileResponseSchema = UserStatsSchema;

export const UserProfileErrorResponseSchema = z.object({
  success: z.literal(false),
  data: z.null(),
});

export const BookingHistoryRequestSchema = z.object({
  status: BookingStatusSchema.optional(),
});

export const BookingHistoryResponseSchema = z.object({
  applied: z.array(BookingSchema),    // 신청(시작 전)
  inProgress: z.array(BookingSchema), // 진행중
  completed: z.array(BookingSchema),  // 완료
});

export const BookingActionRequestSchema = z.object({
  action: z.enum(['pay_deposit', 'cancel_booking']),
  bookingId: z.string(),
});

export const BookingActionResponseSchema = z.object({
  message: z.string(),
});

export type UserStats = z.infer<typeof UserStatsSchema>;
export type BookingStatus = z.infer<typeof BookingStatusSchema>;
export type Booking = z.infer<typeof BookingSchema>;
export type UserProfileResponse = z.infer<typeof UserProfileResponseSchema>;
export type UserProfileErrorResponse = z.infer<typeof UserProfileErrorResponseSchema>;
export type BookingHistoryRequest = z.infer<typeof BookingHistoryRequestSchema>;
export type BookingHistoryResponse = z.infer<typeof BookingHistoryResponseSchema>;
export type BookingActionRequest = z.infer<typeof BookingActionRequestSchema>;
export type BookingActionResponse = z.infer<typeof BookingActionResponseSchema>;