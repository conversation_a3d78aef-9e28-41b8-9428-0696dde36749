export const COMMON_ERRORS = {
  INVALID_PAGE: {
    json: {
      code: 'INVALID_PARAMETER',
      message: 'Page must be a positive integer',
    },
    status: 400,
  },
  INVALID_LIMIT: {
    json: {
      code: 'INVALID_PARAMETER',
      message: 'Limit must be between 1 and 100',
    },
    status: 400,
  },
  SERVER_ERROR: {
    json: {
      code: 'SERVER_ERROR',
      message: 'Internal server error',
    },
    status: 500,
  },
} as const;

export const POSTGREST_ERROR_CODES = {
  PGRST000: {
    status: 503,
    message:
      'Could not connect with the database due to an incorrect db-uri or due to the PostgreSQL service not running.',
  },
  PGRST001: {
    status: 503,
    message: 'Could not connect with the database due to an internal error.',
  },
  PGRST002: {
    status: 503,
    message:
      'Could not connect with the database when building the Schema Cache due to the PostgreSQL service not running.',
  },
  PGRST003: {
    status: 504,
    message:
      'The request timed out waiting for a pool connection to be available. See db-pool-acquisition-timeout.',
  },
  PGRST100: {
    status: 400,
    message:
      'Parsing error in the query string parameter. See Horizontal Filtering, Operators and Ordering.',
  },
  PGRST101: {
    status: 405,
    message:
      'For functions, only GET and POST verbs are allowed. Any other verb will throw this error.',
  },
  PGRST102: {
    status: 400,
    message:
      'An invalid request body was sent(e.g. an empty body or malformed JSON).',
  },
  PGRST103: {
    status: 416,
    message: 'An invalid range was specified for Limits and Pagination.',
  },
  PGRST105: {
    status: 405,
    message: 'An invalid PUT request was done',
  },
  PGRST106: {
    status: 406,
    message:
      'The schema specified when switching schemas is not present in the db-schemas configuration variable.',
  },
  PGRST107: {
    status: 415,
    message: 'The Content-Type sent in the request is invalid.',
  },
  PGRST108: {
    status: 400,
    message:
      'The filter is applied to a embedded resource that is not specified in the select part of the query string. See Embedded Filters.',
  },
  PGRST111: {
    status: 500,
    message: 'An invalid response.headers was set. See Response Headers.',
  },
  PGRST112: {
    status: 500,
    message:
      'The status code must be a positive integer. See Response Status Code.',
  },
  PGRST114: {
    status: 400,
    message: 'For an UPSERT using PUT, when limits and offsets are used.',
  },
  PGRST115: {
    status: 400,
    message:
      'For an UPSERT using PUT, when the primary key in the query string and the body are different.',
  },
  PGRST116: {
    status: 406,
    message:
      'More than 1 or no items where returned when requesting a singular response. See Singular or Plural.',
  },
  PGRST117: {
    status: 405,
    message: 'The HTTP verb used in the request in not supported.',
  },
  PGRST118: {
    status: 400,
    message:
      'Could not order the result using the related table because there is no many-to-one or one-to-one relationship between them.',
  },
  PGRST120: {
    status: 400,
    message:
      'An embedded resource can only be filtered using the is.null or not.is.null operators.',
  },
  PGRST121: {
    status: 500,
    message:
      'PostgREST can’t parse the JSON objects in RAISE PGRST error. See raise headers.',
  },
  PGRST122: {
    status: 400,
    message:
      'Invalid preferences found in Prefer header with Prefer: handling=strict. See Strict or Lenient Handling.',
  },
  PGRST123: {
    status: 400,
    message: 'Aggregate functions are disabled. See db-aggregates-enabled.',
  },
  PGRST124: {
    status: 400,
    message: 'max-affected preference is violated. See Max Affected.',
  },
  PGRST125: {
    status: 404,
    message: 'Invalid path is specified in request URL.',
  },
  PGRST126: {
    status: 404,
    message:
      'Open API config is disabled but API root path is accessed. See openapi-mode.',
  },
  PGRST127: {
    status: 400,
    message: 'The feature specified in the details field is not implemented.',
  },
  PGRST128: {
    status: 400,
    message:
      'max-affected preference is violated with RPC call. See Max Affected.',
  },
} as const;
