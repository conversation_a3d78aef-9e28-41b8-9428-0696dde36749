/**
 * 시설 정보 표시명 매핑 상수
 * 
 * amenities 데이터의 키와 한국어 표시명을 매핑합니다.
 * 전역적으로 일관된 시설명 표시를 위해 사용됩니다.
 */

export const AMENITY_DISPLAY_NAMES = {
  parking: '주차',
  shower: '샤워',
  locker: '락커',
  workoutClothes: '운동복',
  towel: '수건',
  others: '기타',
} as const;

export type AmenityKey = keyof typeof AMENITY_DISPLAY_NAMES;

/**
 * 시설 키에 대응하는 한국어 표시명을 반환합니다.
 * 
 * @param key - 시설 키 (parking, shower, locker, etc.)
 * @returns 한국어 표시명 또는 원본 키 (매핑되지 않은 경우)
 */
export function getAmenityDisplayName(key: string): string {
  return AMENITY_DISPLAY_NAMES[key as AmenityKey] || key;
}
