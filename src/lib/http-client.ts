import { configManager } from './config/config-manager';

interface NextCacheOptions {
  revalidate?: number | false;
  tags?: string[];
}

interface ApiClientOptions extends RequestInit {
  next?: NextCacheOptions;
}

interface ErrorData {
  message?: string;
  [key: string]: any;
}

/**
 * API 요청에서 발생한 에러를 위한 커스텀 에러 클래스입니다.
 * HTTP 상태 코드와 서버에서 받은 에러 데이터를 포함합니다.
 */
class ApiError extends Error {
  public readonly status: number;
  public readonly data: ErrorData;

  constructor(message: string, status: number, data: ErrorData) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * 실제 fetch 요청을 수행하고 응답을 처리하는 핵심 함수입니다.
 * @param endpoint - 요청할 엔드포인트 (예: '/users')
 * @param options - fetch에 전달할 옵션 객체 (Next.js cache 옵션 포함)
 * @returns 성공 시 파싱된 JSON 데이터, 실패 시 throw ApiError
 */
async function fetcher<T = unknown>(
  endpoint: string,
  options: ApiClientOptions = {}
): Promise<T> {
  const baseUrl = configManager.getValue('app.baseUrl');
  // baseUrl 끝의 슬래시와 endpoint 시작의 슬래시 중복 제거
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const url = `${cleanBaseUrl}${cleanEndpoint}`;

  // 인증 토큰을 localStorage에서 가져옵니다.
  const token =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;

  // 기본 헤더 설정
  const defaultHeaders: Record<string, string> = {};

  // FormData가 아닌 경우에만 Content-Type 설정
  const isFormData = options.body instanceof FormData;
  if (!isFormData) {
    defaultHeaders['Content-Type'] = 'application/json';
  }

  // 인증 토큰이 있으면 Authorization 헤더 추가
  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  // 기본 옵션과 사용자 정의 옵션을 병합합니다.
  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  // Next.js cache 옵션이 있으면 추가
  if (options.next) {
    config.next = options.next;
  }

  try {
    const response = await fetch(url, config);

    // fetch는 4xx, 5xx 에러를 throw하지 않으므로, `response.ok`로 직접 확인해야 합니다.
    if (response.ok) {
      // 204 No Content와 같이 응답 본문이 없는 경우
      if (response.status === 204) {
        return undefined as unknown as T; // undefined 반환
      }
      // 성공적인 응답의 경우 JSON 파싱
      return await response.json();
    }

    // 응답이 'ok'가 아닌 경우 (예: 401, 404, 500)
    // 서버에서 보낸 에러 메시지를 파싱하여 커스텀 에러를 throw합니다.
    const errorData: ErrorData = await response.json().catch(() => ({
      message: '서버 에러 응답을 파싱할 수 없습니다.',
    }));

    // 전역 에러 처리 (예: 401 Unauthorized 시 로그인 페이지로 리디렉션)
    if (response.status === 401) {
      console.error('인증 에러! 로그인이 필요합니다.');
      // 필요하다면 여기서 로그인 페이지로 리디렉션 처리
      // window.location.href = '/login';
    }

    console.log('errorData', JSON.stringify(errorData));

    throw new ApiError(
      errorData.message || 'API 요청에 실패했습니다.',
      response.status,
      errorData
    );
  } catch (error) {
    // 네트워크 에러 또는 위에서 throw한 ApiError를 처리합니다.
    console.error('API 클라이언트 에러:', error);

    // 에러를 다시 throw하여 호출한 쪽(예: React Query)에서 처리할 수 있도록 합니다.
    throw error;
  }
}

export const httpClient = {
  get: <T = unknown>(endpoint: string, options: ApiClientOptions = {}) =>
    fetcher<T>(endpoint, { ...options, method: 'GET' }),

  post: <T = unknown>(
    endpoint: string,
    body?: unknown,
    options: ApiClientOptions = {}
  ) =>
    fetcher<T>(endpoint, {
      ...options,
      method: 'POST',
      body:
        body instanceof FormData
          ? body
          : body
            ? JSON.stringify(body)
            : undefined,
    }),

  put: <T = unknown>(
    endpoint: string,
    body?: unknown,
    options: ApiClientOptions = {}
  ) =>
    fetcher<T>(endpoint, {
      ...options,
      method: 'PUT',
      body:
        body instanceof FormData
          ? body
          : body
            ? JSON.stringify(body)
            : undefined,
    }),

  patch: <T = unknown>(
    endpoint: string,
    body?: unknown,
    options: ApiClientOptions = {}
  ) =>
    fetcher<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body:
        body instanceof FormData
          ? body
          : body
            ? JSON.stringify(body)
            : undefined,
    }),

  delete: <T = unknown>(endpoint: string, options: ApiClientOptions = {}) =>
    fetcher<T>(endpoint, { ...options, method: 'DELETE' }),
};

export type HttpClient = typeof httpClient;
