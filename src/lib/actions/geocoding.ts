'use server';

import type { Coordinates } from '@/types/address';
import { configManager } from '../config/config-manager';

/**
 * Kakao 지도 API를 사용하여 주소를 좌표로 변환
 *
 * @param address 변환할 주소
 * @returns 좌표 정보 또는 null
 */
export async function geocodeAddress(
  address: string
): Promise<Coordinates | null> {
  try {
    // Kakao Maps API 키가 설정되어 있는지 확인
    const kakaoApiKey = configManager.getValue('kakao.apiKey');
    if (!kakaoApiKey) {
      console.warn('Kakao Maps API 키가 설정되지 않았습니다.');
      return null;
    }

    const response = await fetch(
      `https://dapi.kakao.com/v2/local/search/address.json?query=${encodeURIComponent(address)}`,
      {
        headers: {
          // Authorization: `KakaoAK ${kakaoApiKey}`,
          Authorization: `KakaoAK ${kakaoApiKey}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`지오코딩 API 호출 실패: ${response.status}`);
    }

    const data = await response.json();

    if (data.documents && data.documents.length > 0) {
      const result = data.documents[0];
      return {
        latitude: parseFloat(result.y),
        longitude: parseFloat(result.x),
      };
    }

    return null;
  } catch (error) {
    console.error('지오코딩 에러:', error);
    return null;
  }
}

/**
 * 좌표 기준으로 가장 가까운 지하철역 조회 (Kakao Local Category Search)
 */
export async function findNearestSubwayStationByCoords(
  coords: Coordinates,
  options?: { radiusMeters?: number }
): Promise<{ stationName: string; distanceMeters: number } | null> {
  try {
    const kakaoApiKey = configManager.getValue('kakao.apiKey');
    if (!kakaoApiKey) {
      console.warn('Kakao Maps API 키가 설정되지 않았습니다.');
      return null;
    }

    const radius = options?.radiusMeters ?? 2000;
    const url = new URL('https://dapi.kakao.com/v2/local/search/category.json');
    url.searchParams.set('category_group_code', 'SW8');
    url.searchParams.set('x', String(coords.longitude));
    url.searchParams.set('y', String(coords.latitude));
    url.searchParams.set('radius', String(radius));
    url.searchParams.set('sort', 'distance');

    const response = await fetch(url.toString(), {
      headers: { Authorization: `KakaoAK ${kakaoApiKey}` },
    });

    if (!response.ok) {
      throw new Error(`카테고리 검색 실패: ${response.status}`);
    }

    const data = await response.json();
    const first = data?.documents?.[0];
    if (!first) return null;

    const stationName: string = first.place_name;
    const distanceMeters = parseInt(first.distance ?? '0', 10) || 0;
    return { stationName, distanceMeters };
  } catch (error) {
    console.error('가까운 지하철역 조회 에러:', error);
    return null;
  }
}
