import { z } from 'zod';
import { getByPath, Path } from 'dot-path-value';

const appConfigSchema = z.object({
  app: z.object({
    baseUrl: z.url({ message: 'Base URL must be a valid URL' }),
    environment: z.string(),
  }),
  kakao: z.object({
    apiKey: z.string(),
  }),
  naver: z.object({
    clientId: z.string(),
  }),
  client: z.object({
    email: z.string(),
  }),
  gaId: z.string(),
  supabase: z.object({
    url: z.string(),
    anonKey: z.string(),
  }),
  databaseUrl: z.string().optional(),
});

type AppConfig = z.infer<typeof appConfigSchema>;

class ConfigManager {
  private static instance: ConfigManager;
  private _config: AppConfig;

  private constructor() {
    this._config = this.loadConfiguration();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  public getConfig(): Readonly<AppConfig> {
    return Object.freeze({ ...this._config });
  }

  public getValue<T extends Path<AppConfig>>(path: T) {
    return getByPath(this._config, path);
  }

  private loadConfiguration() {
    // 서버 사이드에서만 databaseUrl 포함
    const isServer = typeof window === 'undefined';

    const rawConfig: Partial<AppConfig> = {
      app: {
        baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
        environment: process.env.NODE_ENV || 'development',
      },
      kakao: {
        apiKey: process.env.NEXT_PUBLIC_KAKAO_MAP_API_KEY,
      },
      naver: {
        clientId: process.env.NEXT_PUBLIC_NAVER_MAP_CLIENT_ID,
      },
      client: {
        email: '<EMAIL>',
      },
      gaId: process.env.NEXT_PUBLIC_GA_ID,
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      },
      ...(isServer && { databaseUrl: process.env.DATABASE_URL }),
    };

    try {
      // 클라이언트에서는 민감한 정보 로깅 방지
      if (isServer && process.env.NODE_ENV === 'development') {
        console.log('Server config loaded');
      }
      return appConfigSchema.parse(rawConfig);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.issues
          .map(err => `${err.path.join('.')}: ${err.message}`)
          .join('\n');
        throw new Error(`Configuration validation failed:\n${errorMessages}`);
      }
      throw error;
    }
  }

  get config() {
    return this._config;
  }

  isLocal() {
    return this.config.app.environment === 'development';
  }
  isProduction() {
    return this.config.app.environment === 'production';
  }
}

export const configManager = ConfigManager.getInstance();
