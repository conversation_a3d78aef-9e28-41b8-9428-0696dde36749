const loadedEnv = {
  NEXT_PUBLIC_NAVER_MAP_API_KEY: process.env.NEXT_PUBLIC_NAVER_MAP_API_KEY,
  NEXT_PUBLIC_KAKAO_REST_API_KEY: process.env.NEXT_PUBLIC_KAKAO_REST_API_KEY,
  KAKAO_CLIENT_SECRET: process.env.KAKAO_CLIENT_SECRET,
  NEXT_PUBLIC_PORTONE_USER_CODE: process.env.NEXT_PUBLIC_PORTONE_USER_CODE,
  PORTONE_API_KEY: process.env.PORTONE_API_KEY,
  PORTONE_API_SECRET: process.env.PORTONE_API_SECRET,
  NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
  NODE_ENV: process.env.NODE_ENV,
  ADMIN_EMAILS: process.env.ADMIN_EMAILS,
  NEXT_PUBLIC_NAVER_MAP_CLIENT_ID: process.env.NEXT_PUBLIC_NAVER_MAP_CLIENT_ID,
  // 토스페이먼츠 결제위젯용 키 (v2 위젯에 사용)
  NEXT_PUBLIC_TOSS_WIDGET_CLIENT_KEY:
    process.env.NEXT_PUBLIC_TOSS_WIDGET_CLIENT_KEY,
  TOSS_WIDGET_SECRET_KEY: process.env.TOSS_WIDGET_SECRET_KEY,

  // 토스페이먼츠 API 개별 연동 키 (레거시/API용)
  NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY:
    process.env.NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY,
  TOSS_SECRET_KEY: process.env.TOSS_PAYMENTS_SECRET_KEY,
};

type EnvKey = keyof typeof loadedEnv;

export default function getEnv(key: EnvKey) {
  if (!loadedEnv[key]) {
    console.error(`Environment variable ${key} is not set`);
    return '';
  }

  return loadedEnv[key];
}
