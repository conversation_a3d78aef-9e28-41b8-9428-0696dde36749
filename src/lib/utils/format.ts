import type { DateCompatible } from '@/types/common';
import dayjs from 'dayjs';
import 'dayjs/locale/ko';
import { ClassLevelText } from '../db/schema';

dayjs.locale('ko');

export function formatDate(date: DateCompatible): string {
  return dayjs(date).format('YYYY.MM.DD(ddd) HH:mm');
}
export const mapLevelToLabel = (level: string): string => {
  const levelMap: Record<string, string> = {
    [ClassLevelText.BEGINNER]: '입문',
    [ClassLevelText.INTERMEDIATE]: '중급',
    [ClassLevelText.ADVANCED]: '상급',
    all: '전체',
  };
  return levelMap[level.toLowerCase()] || level;
};

// 운동 종류를 사용자 친화적인 라벨로 매핑
export const mapSpecialtyToLabel = (specialty: string): string => {
  const specialtyMap: Record<string, string> = {
    fitness: '피트니스',
    pilates: '필라테스',
    yoga: '요가',
    crossfit: '크로스핏',
    spinning: '스피닝',
    boxing: '복싱',
    swimming: '수영',
    dance: '댄스',
    martial_arts: '무술',
    rehabilitation: '재활',
  };
  return specialtyMap[specialty.toLowerCase()] || specialty;
};

export function mapPaymentMethod(method: string): string {
  const methodMap: Record<string, string> = {
    credit_card: '신용·체크카드',
    debit_card: '체크카드',
    bank_transfer: '계좌이체',
    mobile_payment: '휴대폰결제',
    kakaopay: '카카오페이',
    naverpay: '네이버페이',
    payco: '페이코',
  };
  return methodMap[method] || method;
}

const dayOfWeekMap: Record<string, string> = {
  sun: '일',
  mon: '월',
  tue: '화',
  wed: '수',
  thu: '목',
  fri: '금',
  sat: '토',
};

export function mapDayOfWeek(dayOfWeek: string): string {
  return dayOfWeekMap[dayOfWeek.toLowerCase()] || dayOfWeek;
}

export function mapGender(gender: string | null): string {
  const genderMap: Record<string, string> = {
    MALE: '남자',
    FEMALE: '여자',
    OTHER: '알수없음',
  };
  return genderMap[gender ?? 'OTHER'] || '알수없음';
}
