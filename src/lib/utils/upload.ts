/**
 * 이미지 업로드 관련 유틸리티 함수
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * 허용된 이미지 MIME 타입
 */
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/heic',
] as const;

/**
 * 최대 파일 크기 (25MB)
 */
export const MAX_FILE_SIZE = 25 * 1024 * 1024;

/**
 * 최대 이미지 개수
 */
export const MAX_IMAGE_COUNT = 10;

/**
 * 파일 유효성 검사
 */
export function validateImageFile(file: File): {
  valid: boolean;
  error?: string;
} {
  // 파일 크기 검사
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `파일 크기는 ${MAX_FILE_SIZE / 1024 / 1024}MB 이하여야 합니다.`,
    };
  }

  // MIME 타입 검사
  if (!ALLOWED_IMAGE_TYPES.includes(file.type as any)) {
    return {
      valid: false,
      error: '허용된 이미지 형식: JPG, JPEG, PNG, WEBP, HEIC',
    };
  }

  return { valid: true };
}

/**
 * 여러 이미지 파일 유효성 검사
 */
export function validateImageFiles(files: File[]): {
  valid: boolean;
  error?: string;
} {
  // 개수 검사
  if (files.length > MAX_IMAGE_COUNT) {
    return {
      valid: false,
      error: `최대 ${MAX_IMAGE_COUNT}장까지 업로드 가능합니다.`,
    };
  }

  // 각 파일 유효성 검사
  for (const file of files) {
    const result = validateImageFile(file);
    if (!result.valid) {
      return result;
    }
  }

  return { valid: true };
}

/**
 * 고유한 파일명 생성
 */
export function generateUniqueFileName(
  originalName: string,
  prefix?: string
): string {
  const timestamp = Date.now();
  const uuid = uuidv4().slice(0, 8);
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  
  if (prefix) {
    return `${prefix}-${timestamp}-${uuid}.${extension}`;
  }
  
  return `${timestamp}-${uuid}.${extension}`;
}

/**
 * 스튜디오 이미지 경로 생성
 */
export function generateStudioImagePath(
  partnerId: string,
  studioId: string | null,
  fileName: string
): string {
  const id = studioId || uuidv4();
  return `studios/${partnerId}/${id}/${fileName}`;
}

/**
 * 강사 이미지 경로 생성
 */
export function generateInstructorImagePath(
  instructorId: string | null,
  fileName: string
): string {
  const id = instructorId || uuidv4();
  return `instructors/${id}/${fileName}`;
}

/**
 * 클래스 이미지 경로 생성
 */
export function generateClassImagePath(
  classId: string | null,
  fileName: string
): string {
  const id = classId || uuidv4();
  return `classes/${id}/${fileName}`;
}

/**
 * Supabase Storage 공개 URL 생성
 */
export function getPublicImageUrl(
  supabaseUrl: string,
  bucketName: string,
  filePath: string
): string {
  return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${filePath}`;
}

/**
 * 이미지 변환 URL 생성 (썸네일 등)
 */
export function getTransformedImageUrl(
  supabaseUrl: string,
  bucketName: string,
  filePath: string,
  options?: {
    width?: number;
    height?: number;
    quality?: number;
  }
): string {
  const params = new URLSearchParams();
  
  if (options?.width) params.append('width', options.width.toString());
  if (options?.height) params.append('height', options.height.toString());
  if (options?.quality) params.append('quality', options.quality.toString());
  
  const queryString = params.toString();
  const baseUrl = `${supabaseUrl}/storage/v1/render/image/public/${bucketName}/${filePath}`;
  
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
}

/**
 * base64 이미지를 File 객체로 변환
 */
export function base64ToFile(base64: string, fileName: string): File {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], fileName, { type: mime });
}

/**
 * 이미지 파일을 base64로 변환
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}