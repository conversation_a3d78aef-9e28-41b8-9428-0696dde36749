/**
 * 폰 번호 포맷팅 유틸리티 함수들
 */

/**
 * 숫자만 입력받아 010-1234-5678 형식으로 포맷팅
 * @param input - 숫자만 포함된 문자열 (예: "01012345678")
 * @returns 포맷팅된 폰 번호 문자열 (예: "010-1234-5678")
 */
export function formatPhoneNumber(input: string): string {
  // 숫자만 추출
  const numbers = input.replace(/[^0-9]/g, '');
  
  // 빈 문자열이면 그대로 반환
  if (!numbers) return '';
  
  // 11자리로 제한
  const limitedNumbers = numbers.slice(0, 11);
  
  // 길이에 따라 포맷팅
  if (limitedNumbers.length <= 3) {
    return limitedNumbers;
  } else if (limitedNumbers.length <= 7) {
    return `${limitedNumbers.slice(0, 3)}-${limitedNumbers.slice(3)}`;
  } else {
    return `${limitedNumbers.slice(0, 3)}-${limitedNumbers.slice(3, 7)}-${limitedNumbers.slice(7)}`;
  }
}

/**
 * 하이픈을 제거하여 숫자만 반환
 * @param phoneNumber - 포맷팅된 폰 번호 (예: "010-1234-5678")
 * @returns 숫자만 포함된 문자열 (예: "01012345678")
 */
export function removePhoneFormat(phoneNumber: string): string {
  return phoneNumber.replace(/[^0-9]/g, '');
}

/**
 * 폰 번호 유효성 검사 (하이픈 포함/제외 모두 지원)
 * @param phoneNumber - 검사할 폰 번호
 * @returns 유효한 폰 번호인지 여부
 */
export function validatePhoneNumber(phoneNumber: string): boolean {
  const numbersOnly = removePhoneFormat(phoneNumber);
  
  // 11자리 숫자이고 010으로 시작하는지 확인
  return /^010\d{8}$/.test(numbersOnly);
}

/**
 * 폰 번호를 표시용 형태로 변환 (항상 하이픈 포함)
 * @param phoneNumber - 폰 번호 (하이픈 포함/제외 상관없음)
 * @returns 표시용 폰 번호 (예: "010-1234-5678")
 */
export function displayPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return '';
  
  const numbersOnly = removePhoneFormat(phoneNumber);
  
  if (numbersOnly.length !== 11) return phoneNumber; // 유효하지 않으면 원본 반환
  
  return `${numbersOnly.slice(0, 3)}-${numbersOnly.slice(3, 7)}-${numbersOnly.slice(7)}`;
}

/**
 * 폰 번호를 저장용 형태로 변환 (항상 하이픈 제거)
 * @param phoneNumber - 폰 번호 (하이픈 포함/제외 상관없음)
 * @returns 저장용 폰 번호 (예: "01012345678")
 */
export function normalizePhoneNumber(phoneNumber: string): string {
  return removePhoneFormat(phoneNumber);
}

/**
 * 사용자 입력을 실시간으로 포맷팅하기 위한 함수
 * @param currentValue - 현재 입력 필드의 값
 * @param newInput - 새로 입력된 문자열
 * @returns 포맷팅된 결과
 */
export function handlePhoneInput(currentValue: string, newInput: string): string {
  // 백스페이스나 삭제 등의 경우를 고려하여 전체 입력을 다시 포맷팅
  return formatPhoneNumber(newInput);
}