/**
 * 썸네일 생성 유틸리티
 * Canvas를 사용해 크롭된 썸네일 이미지를 생성합니다.
 */

export interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
  unit: 'px' | '%';
}

export interface ImageWithThumbnail {
  url: string;
  path: string;
  thumbnail?: {
    url: string;
    path: string;
  };
}

export interface ThumbnailOptions {
  size?: number; // 썸네일 크기 (정사각형)
  quality?: number; // JPEG 품질 (0.0 - 1.0)
  format?: 'image/jpeg' | 'image/png' | 'image/webp';
}

/**
 * 이미지를 로드하고 Canvas에 그릴 수 있는 HTMLImageElement로 변환
 */
export const loadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // CORS 문제 방지
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
};

/**
 * 크롭 영역을 픽셀 좌표로 변환
 */
export const convertCropToPixels = (
  cropArea: CropArea,
  imageWidth: number,
  imageHeight: number
): { x: number; y: number; width: number; height: number } => {
  if (cropArea.unit === 'px') {
    return {
      x: cropArea.x,
      y: cropArea.y,
      width: cropArea.width,
      height: cropArea.height,
    };
  }

  // 퍼센트를 픽셀로 변환
  return {
    x: (cropArea.x / 100) * imageWidth,
    y: (cropArea.y / 100) * imageHeight,
    width: (cropArea.width / 100) * imageWidth,
    height: (cropArea.height / 100) * imageHeight,
  };
};

/**
 * 크롭된 썸네일 이미지를 생성
 */
export const generateThumbnail = async (
  imageUrl: string,
  cropArea: CropArea,
  options: ThumbnailOptions = {}
): Promise<Blob> => {
  const {
    size = 200,
    quality = 0.8,
    format = 'image/jpeg'
  } = options;

  try {
    // 이미지 로드
    const img = await loadImage(imageUrl);
    
    // 크롭 영역을 픽셀 좌표로 변환
    const cropPixels = convertCropToPixels(cropArea, img.width, img.height);
    
    // Canvas 생성
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Canvas context를 생성할 수 없습니다.');
    }
    
    // 썸네일 크기 설정
    canvas.width = size;
    canvas.height = size;
    
    // 고품질 렌더링 설정
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    // 크롭된 이미지를 정사각형 썸네일로 그리기
    ctx.drawImage(
      img,
      cropPixels.x,
      cropPixels.y,
      cropPixels.width,
      cropPixels.height,
      0,
      0,
      size,
      size
    );
    
    // Blob으로 변환
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('썸네일 생성에 실패했습니다.'));
          }
        },
        format,
        quality
      );
    });
    
  } catch (error) {
    console.error('썸네일 생성 중 오류:', error);
    throw error;
  }
};

/**
 * 썸네일 파일명 생성
 */
export const generateThumbnailFileName = (originalFileName: string): string => {
  const extension = originalFileName.split('.').pop() || 'jpg';
  const nameWithoutExt = originalFileName.replace(`.${extension}`, '');
  return `${nameWithoutExt}_thumb.${extension}`;
};

/**
 * 썸네일 업로드 경로 생성
 */
export const generateThumbnailPath = (originalPath: string): string => {
  const pathParts = originalPath.split('/');
  const fileName = pathParts.pop() || '';
  const thumbnailFileName = generateThumbnailFileName(fileName);
  return [...pathParts, thumbnailFileName].join('/');
};

/**
 * 이미지 URL 가져오기 (썸네일 우선)
 * 썸네일이 있으면 썸네일 URL, 없으면 원본 URL 반환
 */
export const getImageUrl = (image: ImageWithThumbnail | null | undefined, fallback?: string): string => {
  if (!image) {
    return fallback || '';
  }

  // 썸네일이 있으면 썸네일 URL 반환
  if (image.thumbnail?.url) {
    return image.thumbnail.url;
  }

  // 썸네일이 없으면 원본 URL 반환
  if (image.url) {
    return image.url;
  }

  // 둘 다 없으면 fallback 반환
  return fallback || '';
};

/**
 * 강사 프로필 이미지 배열에서 첫 번째 이미지 URL 가져오기
 */
export const getInstructorProfileImageUrl = (
  profileImages: ImageWithThumbnail[] | null | undefined,
  fallback?: string
): string => {
  const firstImage = profileImages?.[0];
  return getImageUrl(firstImage, fallback);
};

/**
 * 이미지가 썸네일을 가지고 있는지 확인
 */
export const hasThumbnail = (image: ImageWithThumbnail | null | undefined): boolean => {
  return !!(image?.thumbnail?.url);
};