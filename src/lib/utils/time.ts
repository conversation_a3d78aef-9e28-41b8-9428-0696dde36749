/**
 * 시간 관련 유틸리티 함수들
 */

/**
 * HH:mm:ss 형식의 시간을 HH:mm 형식으로 변환
 * @param timeString - HH:mm 또는 HH:mm:ss 형식의 시간 문자열
 * @returns HH:mm 형식의 시간 문자열
 * @example
 * formatTimeToHHMM('10:30:00') // '10:30'
 * formatTimeToHHMM('10:30') // '10:30'
 * formatTimeToHHMM('') // ''
 */
export const formatTimeToHHMM = (timeString: string): string => {
  if (!timeString) return '';
  return timeString.slice(0, 5); // HH:mm:ss → HH:mm 또는 HH:mm → HH:mm
};

/**
 * HH:mm 형식의 시간을 HH:mm:ss 형식으로 변환 (초는 00으로 설정)
 * @param timeString - HH:mm 형식의 시간 문자열
 * @returns HH:mm:ss 형식의 시간 문자열
 * @example
 * formatTimeToHHMMSS('10:30') // '10:30:00'
 * formatTimeToHHMMSS('') // ''
 */
export const formatTimeToHHMMSS = (timeString: string): string => {
  if (!timeString) return '';
  return timeString.includes(':') && timeString.split(':').length === 2
    ? `${timeString}:00`
    : timeString;
};

/**
 * 시간 문자열이 유효한 HH:mm 또는 HH:mm:ss 형식인지 확인
 * @param timeString - 검증할 시간 문자열
 * @returns 유효한 시간 형식인지 여부
 * @example
 * isValidTimeFormat('10:30') // true
 * isValidTimeFormat('10:30:00') // true
 * isValidTimeFormat('25:00') // false
 */
export const isValidTimeFormat = (timeString: string): boolean => {
  if (!timeString) return false;
  
  // HH:mm 또는 HH:mm:ss 형식 검증
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
  return timeRegex.test(timeString);
};