import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { configManager } from '../config/config-manager';

export const createSupabaseClient = async () => {
  const cookieStore = await cookies();
  return createServerClient(
    configManager.getValue('supabase.url'),
    configManager.getValue('supabase.anonKey'),
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
};
