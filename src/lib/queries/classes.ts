import { db } from '@/lib/db';
import { ClassLevelText, studios } from '@/lib/db/schema';
import { sql } from 'drizzle-orm';
import { mapLevelToLabel } from '../utils/format';

/**
 * 모든 레벨 옵션을 가져오는 함수
 */
export async function getAllLevels() {
  try {
    // ClassLevelText enum에서 모든 값들을 반환
    const levels = Object.values(ClassLevelText).map(level => ({
      value: level,
      label: mapLevelToLabel(level),
    }));

    return levels;
  } catch (error) {
    console.error('getAllLevels::error', error);
    throw error;
  }
}

/**
 * 모든 가까운 역 옵션을 가져오는 함수 (실제 DB에서 존재하는 데이터만)
 */
export async function getAllNearestStations() {
  try {
    const stationsQuery = await db
      .select({
        station: studios.nearestStation,
      })
      .from(studios)
      .where(sql`${studios.nearestStation} IS NOT NULL`)
      .groupBy(studios.nearestStation)
      .orderBy(studios.nearestStation);

    const stations = stationsQuery
      .map(item => item.station)
      .filter((station): station is string => station !== null)
      .map(station => ({
        value: station,
        label: station,
      }));

    return stations;
  } catch (error) {
    console.error('getAllNearestStations::error', error);
    throw error;
  }
}

/**
 * 레벨 값을 한국어로 매핑하는 헬퍼 함수
 */
function mapClassLevelToKorean(level: string) {
  switch (level) {
    case 'beginner':
      return '초급';
    case 'intermediate':
      return '중급';
    case 'advanced':
      return '고급';
    case 'all_levels':
      return '모든 레벨';
    default:
      return level;
  }
}
