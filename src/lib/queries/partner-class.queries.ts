import { db } from '@/lib/db';
import { classes, studios, instructors, ClassStatusText } from '@/lib/db/schema';
import { eq, and, desc, isNull, count } from 'drizzle-orm';

export type InsertClass = typeof classes.$inferInsert;
export type SelectClass = typeof classes.$inferSelect;

/**
 * 파트너 클래스 관련 순수 Drizzle 쿼리들
 */
export const partnerClassQueries = {
  /**
   * 새로운 클래스 생성 (트랜잭션 내에서)
   */
  create: (tx: any, data: InsertClass) =>
    tx.insert(classes).values(data).returning(),

  /**
   * ID로 클래스 조회 (삭제되지 않은 것만)
   */
  findById: (classId: string) =>
    db.select()
      .from(classes)
      .where(and(eq(classes.id, classId), isNull(classes.deleted_at)))
      .limit(1),

  /**
   * 파트너의 클래스 목록 조회 (페이지네이션, 필터링 포함)
   */
  findByPartner: (partnerId: string, filters: { studioId?: string; page: number; limit: number }) => {
    const conditions = [
      eq(classes.partner_id, partnerId),
      isNull(classes.deleted_at)
    ];
    
    // 스튜디오 필터 적용
    if (filters.studioId) {
      conditions.push(eq(classes.studio_id, filters.studioId));
    }

    return {
      // 전체 개수 조회
      count: () =>
        db.select({ value: count() })
          .from(classes)
          .where(and(...conditions)),

      // 페이지네이션 적용하여 목록 조회
      list: () =>
        db.select({
          id: classes.id,
          partnerId: classes.partner_id,
          studioId: classes.studio_id,
          instructorId: classes.instructor_id,
          title: classes.title,
          description: classes.description,
          category: classes.category,
          level: classes.level,
          target: classes.target,
          maxParticipants: classes.max_participants,
          pricePerSession: classes.price_per_session,
          sessionDurationMinutes: classes.session_duration_minutes,
          durationWeeks: classes.duration_weeks,
          sessionsPerWeek: classes.sessions_per_week,
          images: classes.images,
          status: classes.status,
          visible: classes.visible,
          createdAt: classes.created_at,
          updatedAt: classes.updated_at,
          // 조인된 정보
          studioName: studios.name,
          instructorName: instructors.name,
        })
        .from(classes)
        .leftJoin(studios, eq(classes.studio_id, studios.id))
        .leftJoin(instructors, eq(classes.instructor_id, instructors.id))
        .where(and(...conditions))
        .orderBy(desc(classes.created_at))
        .limit(filters.limit)
        .offset((filters.page - 1) * filters.limit)
    };
  },

  /**
   * 클래스 정보 업데이트
   */
  update: (tx: any, classId: string, data: Partial<InsertClass>) =>
    tx.update(classes)
      .set({ ...data, updated_at: new Date() })
      .where(eq(classes.id, classId))
      .returning(),

  /**
   * 클래스 소프트 삭제
   */
  softDelete: (classId: string) =>
    db.update(classes)
      .set({
        deleted_at: new Date(),
        status: ClassStatusText.INACTIVE
      })
      .where(eq(classes.id, classId)),

  /**
   * 파트너가 소유한 스튜디오 ID 목록 조회
   */
  getPartnerStudioIds: (partnerId: string) =>
    db.select({ id: studios.id })
      .from(studios)
      .where(eq(studios.partnerId, partnerId)),

  /**
   * 클래스에 활성화된 수강신청이 있는지 확인
   */
  hasActiveEnrollments: (classId: string) => {
    // TODO: 새로운 enrollment 테이블 구현 후 수정 필요
    // 현재는 임시로 false 반환
    return Promise.resolve(false);
  }
};