import { db } from '@/lib/db';
import { enrollments, members, classes, class_schedule_groups } from '@/lib/db/schema';
import { eq, and, desc, asc } from 'drizzle-orm';

export interface EnrollmentFilters {
  sortBy?: 'enrollmentOrder' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 특정 스케줄 그룹의 수강 신청 목록 조회
 */
export async function findEnrollmentsByScheduleGroup(
  scheduleGroupId: number,
  filters: EnrollmentFilters = {}
) {
  const { sortBy = 'enrollmentOrder', sortOrder = 'asc' } = filters;

  // 기본 쿼리
  const query = db
    .select({
      // Enrollment 정보
      id: enrollments.id,
      memberId: enrollments.memberId,
      status: enrollments.status,
      enrollmentOrder: enrollments.enrollmentOrder,
      createdAt: enrollments.createdAt,
      
      // Member 정보 (필요한 것만 - 존재하는 컬럼만)
      nickname: members.nickname,
      email: members.email,
    })
    .from(enrollments)
    .innerJoin(members, eq(enrollments.memberId, members.id))
    .where(eq(enrollments.scheduleGroupId, scheduleGroupId));

  // 정렬
  const orderByClause = sortBy === 'createdAt' 
    ? (sortOrder === 'desc' ? desc(enrollments.createdAt) : asc(enrollments.createdAt))
    : (sortOrder === 'desc' ? desc(enrollments.enrollmentOrder) : asc(enrollments.enrollmentOrder));

  return await query.orderBy(orderByClause);
}

/**
 * 파트너가 특정 스케줄 그룹의 소유자인지 확인
 */
export async function verifyPartnerOwnership(
  scheduleGroupId: number,
  partnerId: string
): Promise<boolean> {
  const result = await db
    .select({
      partnerId: classes.partner_id,
    })
    .from(class_schedule_groups)
    .innerJoin(classes, eq(class_schedule_groups.class_id, classes.id))
    .where(eq(class_schedule_groups.id, scheduleGroupId))
    .limit(1);

  if (result.length === 0) {
    return false;
  }

  return result[0].partnerId === partnerId;
}

export const enrollmentQueries = {
  findEnrollmentsByScheduleGroup,
  verifyPartnerOwnership,
};