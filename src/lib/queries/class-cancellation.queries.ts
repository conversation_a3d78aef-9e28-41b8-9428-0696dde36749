import { db } from '@/lib/db';
import { 
  classes, 
  class_schedule_groups, 
  enrollments,
  payments,
  ClassScheduleStatusText 
} from '@/lib/db/schema';
import { eq, and, isNull, asc, inArray, or } from 'drizzle-orm';

/**
 * 파트너 수업 취소 관련 순수 Drizzle 쿼리들
 */
export const classCancellationQueries = {
  /**
   * 파트너 소유권 검증과 함께 클래스 정보 조회
   * 삭제되지 않은 클래스 대상 (상태 무관)
   */
  findClassByPartner: (classId: string, partnerId: string) =>
    db.select({
      id: classes.id,
      partnerId: classes.partner_id,
      studioId: classes.studio_id,
      instructorId: classes.instructor_id,
      title: classes.title,
      maxParticipants: classes.max_participants,
      status: classes.status,
      visible: classes.visible,
      deletedAt: classes.deleted_at,
    })
    .from(classes)
    .where(and(
      eq(classes.id, classId),
      eq(classes.partner_id, partnerId),
      isNull(classes.deleted_at)
    ))
    .limit(1),

  /**
   * 스케줄 그룹 상태 조회
   * 취소 가능한 상태인지 검증용
   */
  findScheduleGroupById: (classId: string, scheduleGroupId: number) =>
    db.select({
      id: class_schedule_groups.id,
      classId: class_schedule_groups.class_id,
      startDate: class_schedule_groups.start_date,
      status: class_schedule_groups.status,
      createdAt: class_schedule_groups.created_at,
      updatedAt: class_schedule_groups.updated_at,
    })
    .from(class_schedule_groups)
    .where(and(
      eq(class_schedule_groups.id, scheduleGroupId),
      eq(class_schedule_groups.class_id, classId)
    ))
    .limit(1),

  /**
   * 취소 대상 수강신청들 조회 (paid, confirmed 상태)
   * enrollmentOrder 순서대로 정렬
   */
  findEnrollmentsToCancel: (classId: string, scheduleGroupId: number) =>
    db.select({
      id: enrollments.id,
      memberId: enrollments.memberId,
      classId: enrollments.classId,
      scheduleGroupId: enrollments.scheduleGroupId,
      status: enrollments.status,
      enrollmentOrder: enrollments.enrollmentOrder,
      totalAmount: enrollments.totalAmount,
      depositAmount: enrollments.depositAmount,
      createdAt: enrollments.createdAt,
      updatedAt: enrollments.updatedAt,
    })
    .from(enrollments)
    .where(and(
      eq(enrollments.classId, classId),
      eq(enrollments.scheduleGroupId, scheduleGroupId),
      or(
        eq(enrollments.status, 'paid'),
        eq(enrollments.status, 'confirmed')
      )
    ))
    .orderBy(asc(enrollments.enrollmentOrder)),

  /**
   * 수강신청 ID로 결제 정보 조회
   */
  findPaymentsByEnrollmentIds: (enrollmentIds: string[]) =>
    db.select({
      id: payments.id,
      enrollmentId: payments.enrollmentId,
      memberId: payments.memberId,
      amount: payments.amount,
      paymentMethod: payments.paymentMethod,
      transactionId: payments.transactionId,
      pgTransactionId: payments.pgTransactionId,
      merchantUid: payments.merchantUid,
      paymentStatus: payments.paymentStatus,
      paidAt: payments.paidAt,
    })
    .from(payments)
    .where(and(
      inArray(payments.enrollmentId, enrollmentIds),
      eq(payments.paymentStatus, 'paid')
    )),

  /**
   * 스케줄 그룹 상태를 cancelled로 변경하고 취소 사유 저장
   */
  cancelScheduleGroup: (tx: typeof db, scheduleGroupId: number, cancellationReason: string) =>
    tx.update(class_schedule_groups)
      .set({
        status: ClassScheduleStatusText.CANCELLED,
        cancellation_reason: cancellationReason,
        updated_at: new Date(),
      })
      .where(eq(class_schedule_groups.id, scheduleGroupId))
      .returning({
        id: class_schedule_groups.id,
        status: class_schedule_groups.status,
        cancellationReason: class_schedule_groups.cancellation_reason,
        updatedAt: class_schedule_groups.updated_at,
      }),

  /**
   * 수강신청 상태를 refunded로 일괄 변경
   */
  bulkUpdateEnrollmentStatusToRefunded: (tx: typeof db, enrollmentIds: string[]) =>
    tx.update(enrollments)
      .set({
        status: 'refunded',
        updatedAt: new Date(),
      })
      .where(inArray(enrollments.id, enrollmentIds))
      .returning({
        id: enrollments.id,
        memberId: enrollments.memberId,
        status: enrollments.status,
        updatedAt: enrollments.updatedAt,
      }),

  /**
   * 결제 상태를 refunded로 일괄 변경
   */
  bulkUpdatePaymentStatusToRefunded: (tx: typeof db, paymentIds: string[]) =>
    tx.update(payments)
      .set({
        paymentStatus: 'refunded',
        refundedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(inArray(payments.id, paymentIds))
      .returning({
        id: payments.id,
        enrollmentId: payments.enrollmentId,
        paymentStatus: payments.paymentStatus,
        refundedAt: payments.refundedAt,
      }),
};