import { db } from '@/lib/db';
import { members, classes, studios, instructors, class_schedule_groups, class_schedules } from '@/lib/db/schema';
import { enrollments } from '@/lib/db/schema/enrollments';
import { payments } from '@/lib/db/schema/payments';
import { eq, and, sql, gte, min, inArray } from 'drizzle-orm';
import type { UserStats, BookingHistoryResponse, Booking } from '@/schemas/profile';
import { BookingSchema } from '@/schemas/profile';
import { z } from 'zod';

export class ProfileService {
  /**
   * 회원 프로필 정보 조회
   * @param memberId 회원 ID
   * @returns 회원 통계 정보
   */
  async getMemberProfile(memberId: string): Promise<UserStats | null> {
    // 1. 회원 정보 조회
    const memberResult = await db
      .select({
        nickname: members.nickname,
      })
      .from(members)
      .where(eq(members.id, memberId))
      .limit(1);

    if (!memberResult.length) {
      return null;
    }

    const member = memberResult[0];

    // 2. 수강 통계 조회 (신청, 진행중, 완료된 클래스)
    const enrollmentStats = await db
      .select({
        enrollmentId: enrollments.id,
        classId: enrollments.classId,
        scheduleGroupId: enrollments.scheduleGroupId,
        enrollmentStatus: enrollments.status,
        scheduleStatus: class_schedule_groups.status,
        startDate: class_schedule_groups.start_date,
        durationWeeks: classes.duration_weeks,
        sessionsPerWeek: classes.sessions_per_week,
      })
      .from(enrollments)
      .innerJoin(classes, eq(enrollments.classId, classes.id))
      .innerJoin(class_schedule_groups, eq(enrollments.scheduleGroupId, class_schedule_groups.id))
      .where(and(
        eq(enrollments.memberId, memberId),
        eq(enrollments.status, 'confirmed')
      ));

    // 3. 총 수업일수와 출석일수 계산
    let totalClassDays = 0;
    let attendedDays = 0;
    const today = new Date();

    enrollmentStats.forEach(enrollment => {
      if (enrollment.scheduleStatus === 'confirmed' && enrollment.startDate) {
        const startDate = new Date(enrollment.startDate);
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + (enrollment.durationWeeks * 7));

        // 진행중이거나 완료된 클래스만 계산
        if (startDate <= today) {
          const actualEndDate = endDate > today ? today : endDate;
          const daysDiff = Math.floor((actualEndDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          const weeks = Math.floor(daysDiff / 7);
          const classDays = weeks * enrollment.sessionsPerWeek;
          
          totalClassDays += classDays;
          // 출석일수는 총 수업일수와 동일하다고 가정
          attendedDays += classDays;
        }
      }
    });

    // 4. 다음 수업 일정 조회 (최대 3개)
    const nextClassesQuery = await db
      .select({
        classId: classes.id,
        classTitle: classes.title,
        studioName: studios.name,
        studioStation: studios.nearestStation,
        startDate: class_schedule_groups.start_date,
        dayOfWeek: class_schedules.day_of_week,
        startTime: class_schedules.start_time,
        endTime: class_schedules.end_time,
        durationWeeks: classes.duration_weeks,
      })
      .from(enrollments)
      .innerJoin(classes, eq(enrollments.classId, classes.id))
      .innerJoin(class_schedule_groups, eq(enrollments.scheduleGroupId, class_schedule_groups.id))
      .innerJoin(class_schedules, eq(class_schedules.schedule_group_id, class_schedule_groups.id))
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .where(and(
        eq(enrollments.memberId, memberId),
        eq(enrollments.status, 'confirmed'),
        eq(class_schedule_groups.status, 'confirmed'),
        gte(class_schedule_groups.start_date, sql`CURRENT_DATE`)
      ))
      .orderBy(class_schedule_groups.start_date, class_schedules.start_time);

    // 5. 다음 수업 일정 계산
    const nextClasses: Array<{
      classId: string;
      title: string;
      date: string;
      time: string;
      location: string;
    }> = [];
    const processedDates = new Set<string>();

    for (const schedule of nextClassesQuery) {
      if (nextClasses.length >= 3) break;

      if (!schedule.startDate) continue;

      const startDate = new Date(schedule.startDate);
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + (schedule.durationWeeks * 7));

      // 수업 기간 내의 다음 수업 날짜 찾기
      const nextClassDate = getNextClassDate(
        startDate,
        endDate,
        schedule.dayOfWeek,
        today
      );

      if (nextClassDate) {
        const dateKey = `${nextClassDate.toISOString().split('T')[0]}-${schedule.startTime}`;
        
        if (!processedDates.has(dateKey)) {
          processedDates.add(dateKey);
          nextClasses.push({
            classId: schedule.classId,
            title: schedule.classTitle,
            date: nextClassDate.toISOString().split('T')[0],
            time: `${schedule.startTime} ~ ${schedule.endTime}`,
            location: schedule.studioStation ? `${schedule.studioName} (${schedule.studioStation}역)` : schedule.studioName,
          });
        }
      }
    }

    return {
      nickname: member.nickname || '회원',
      totalClassDays,
      attendedDays,
      nextClasses,
    };
  }

  /**
   * 회원 수강신청 내역 조회
   * @param memberId 회원 ID
   * @returns 상태별 수강신청 내역
   */
  async getMemberBookings(memberId: string): Promise<BookingHistoryResponse> {
    // 모든 confirmed 상태의 수강신청 조회
    const bookings = await db
      .select({
        // Enrollment 정보
        enrollmentId: enrollments.id,
        enrollmentStatus: enrollments.status,
        enrolledAt: enrollments.createdAt,
        totalAmount: enrollments.totalAmount,
        depositAmount: enrollments.depositAmount,
        
        // Class 정보
        classId: classes.id,
        classTitle: classes.title,
        category: classes.category,
        level: classes.level,
        pricePerSession: classes.price_per_session,
        sessionsPerWeek: classes.sessions_per_week,
        durationWeeks: classes.duration_weeks,
        maxParticipants: classes.max_participants,
        
        // Schedule Group 정보
        scheduleGroupId: enrollments.scheduleGroupId,
        scheduleStatus: class_schedule_groups.status,
        startDate: class_schedule_groups.start_date,
        
        // Studio 정보
        studioName: studios.name,
        studioStation: studios.nearestStation,
        
        // Instructor 정보
        instructorName: instructors.name,
        
        // Payment 정보
        paymentKey: payments.pgTransactionId,
        
        // Schedule 정보 (시간) - 첫 번째 스케줄만
        startTime: min(class_schedules.start_time),
        endTime: min(class_schedules.end_time),
        dayOfWeek: min(class_schedules.day_of_week),
      })
      .from(enrollments)
      .innerJoin(classes, eq(enrollments.classId, classes.id))
      .innerJoin(class_schedule_groups, eq(enrollments.scheduleGroupId, class_schedule_groups.id))
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .innerJoin(instructors, eq(classes.instructor_id, instructors.id))
      .leftJoin(payments, eq(enrollments.id, payments.enrollmentId))
      .leftJoin(class_schedules, eq(class_schedule_groups.id, class_schedules.schedule_group_id))
      .where(and(
        eq(enrollments.memberId, memberId),
        inArray(enrollments.status, ['paid', 'confirmed'])
      ))
      .groupBy(
        enrollments.id,
        enrollments.createdAt,
        enrollments.totalAmount,
        enrollments.depositAmount,
        classes.id,
        classes.title,
        classes.category,
        classes.level,
        classes.price_per_session,
        classes.sessions_per_week,
        classes.duration_weeks,
        classes.max_participants,
        enrollments.scheduleGroupId,
        class_schedule_groups.status,
        class_schedule_groups.start_date,
        studios.name,
        studios.nearestStation,
        instructors.name,
        payments.pgTransactionId
      )
      .orderBy(enrollments.createdAt);

    // 상태별로 분류
    const today = new Date();
    const applied: Booking[] = [];
    const inProgress: Booking[] = [];
    const completed: Booking[] = [];

    for (const booking of bookings) {
      // 종료일 계산
      let endDate: Date | null = null;
      if (booking.startDate) {
        endDate = new Date(booking.startDate);
        endDate.setDate(endDate.getDate() + (booking.durationWeeks * 7));
      }

      // 날짜와 시간 포맷팅 (시작일 기준)
      let formattedDate: string | null = null;
      let formattedTime: string | null = null;
      if (booking.startDate) {
        formattedDate = booking.startDate;
        // 실제 수업 시간 정보 사용
        formattedTime = booking.startTime && booking.endTime 
          ? `${booking.startTime} ~ ${booking.endTime}`
          : null;
      }

      const baseData = {
        id: booking.enrollmentId, // components expect 'id'
        enrollmentId: booking.enrollmentId,
        enrolledAt: booking.enrolledAt?.toISOString() || new Date().toISOString(),
        classId: booking.classId,
        classTitle: booking.classTitle,
        category: booking.category,
        level: booking.level,
        coachName: booking.instructorName, // components expect 'coachName'
        instructorName: booking.instructorName,
        studioName: booking.studioName,
        location: booking.studioStation,
        scheduleGroupId: booking.scheduleGroupId,
        date: formattedDate,
        time: formattedTime,
        startDate: booking.startDate,
        endDate: endDate?.toISOString().split('T')[0] || null,
        sessionsPerWeek: booking.sessionsPerWeek,
        durationWeeks: booking.durationWeeks,
        schedule: `주 ${booking.sessionsPerWeek}회, 총 ${booking.durationWeeks * booking.sessionsPerWeek}회`,
        maxParticipants: booking.maxParticipants,
        price: booking.pricePerSession, // components expect 'price'
        pricePerSession: booking.pricePerSession,
        totalAmount: booking.totalAmount,
        depositAmount: booking.depositAmount,
        payment: {
          status: 'completed' as const, // 결제 완료 상태로 기본값 설정
          paymentKey: booking.paymentKey ?? undefined,
        },
        paymentDueDate: undefined, // 결제 마감일 정보 없음
      };

      // 상태 분류 - enrollments.status 우선 체크
      if (booking.enrollmentStatus === 'confirmed') {
        // 확정된 수강신청: 날짜 기준으로 진행중/완료 분류
        if (endDate && today > endDate) {
          // 종료됨 = 완료 상태
          completed.push({
            ...baseData,
            status: 'completed' as const,
          });
        } else {
          // 진행중 (시작일 관계없이)
          inProgress.push({
            ...baseData,
            status: 'in_progress' as const,
          });
        }
      } else if (booking.scheduleStatus === 'pending') {
        // 결제 완료, 스케줄 미확정 = 모집중
        applied.push({
          ...baseData,
          status: 'recruiting' as const,
        });
      } else if (booking.scheduleStatus === 'confirmed' && booking.startDate) {
        const startDate = new Date(booking.startDate);
        
        if (today < startDate) {
          // 시작 전 = 예약 완료
          applied.push({
            ...baseData,
            status: 'reserved' as const,
          });
        } else {
          // 시작됐지만 아직 확정 안된 경우 = 예약 완료
          applied.push({
            ...baseData,
            status: 'reserved' as const,
          });
        }
      } else {
        // 기타 = 예약 완료로 처리
        applied.push({
          ...baseData,
          status: 'reserved' as const,
        });
      }
    }

    return {
      applied,
      inProgress,
      completed,
    };
  }
}

/**
 * 다음 수업 날짜 계산
 */
function getNextClassDate(
  startDate: Date,
  endDate: Date,
  dayOfWeek: string,
  fromDate: Date
): Date | null {
  const dayMap: Record<string, number> = {
    'sun': 0,
    'mon': 1,
    'tue': 2,
    'wed': 3,
    'thu': 4,
    'fri': 5,
    'sat': 6,
  };

  const targetDay = dayMap[dayOfWeek];
  if (targetDay === undefined) return null;

  const current = new Date(Math.max(startDate.getTime(), fromDate.getTime()));
  current.setHours(0, 0, 0, 0);

  // 최대 2주 내에서 다음 수업 날짜 찾기
  for (let i = 0; i < 14; i++) {
    if (current > endDate) break;
    
    if (current.getDay() === targetDay && current >= fromDate) {
      return new Date(current);
    }
    
    current.setDate(current.getDate() + 1);
  }

  return null;
}

// Singleton instance
export const profileService = new ProfileService();