import { db } from '@/lib/db';
import { classCancellationQueries } from '@/lib/queries/class-cancellation.queries';
import { cancelTossPayment } from '@/lib/api/toss-payments';
import { 
  CancelClassParams,
  CancelClassResult,
  PaymentInfo,
  RefundProcessResult,
  BusinessRuleError 
} from '@/lib/types/class-cancellation.types';

/**
 * 파트너 수업 취소 관련 비즈니스 로직 처리
 */
export const classCancellationService = {
  /**
   * 파트너 수업 취소 메인 로직
   * 1. 권한 및 상태 검증 2. 취소 조건 확인 3. 환불 처리 4. 상태 업데이트
   */
  async cancelClass(params: CancelClassParams): Promise<CancelClassResult> {
    return await db.transaction(async (tx) => {
      // 1. 파트너 소유권 및 클래스 상태 검증
      await this.validateClassAndOwnership(params);
      
      // 2. 스케줄 그룹 상태 및 취소 조건 검증
      await this.validateCancellationConditions(params);
      
      // 3. 취소 대상 수강신청 및 결제 정보 조회
      const { enrollments, payments } = await this.getEnrollmentsAndPayments(params);
      
      if (enrollments.length === 0) {
        throw new BusinessRuleError('NO_ENROLLMENTS_TO_REFUND', '환불할 수강신청이 없습니다');
      }
      
      // 4. 토스페이먼츠 환불 처리
      const refundResults = await this.processRefunds(payments, params.cancelReason);
      
      // 5. 환불 성공한 것들만 DB 상태 업데이트
      const successfulRefunds = refundResults.filter(r => r.status === 'success');
      const successfulEnrollmentIds = successfulRefunds.map(r => r.enrollmentId);
      const successfulPaymentIds = successfulRefunds.map(r => r.paymentId);
      
      if (successfulRefunds.length === 0) {
        throw new BusinessRuleError('REFUND_FAILED', '모든 환불 처리에 실패했습니다');
      }
      
      // 6. DB 상태 일괄 업데이트 (원자성 보장)
      const [, , cancelledScheduleGroup] = await Promise.all([
        classCancellationQueries.bulkUpdateEnrollmentStatusToRefunded(tx, successfulEnrollmentIds),
        classCancellationQueries.bulkUpdatePaymentStatusToRefunded(tx, successfulPaymentIds),
        classCancellationQueries.cancelScheduleGroup(tx, params.scheduleGroupId, params.cancelReason)
      ]);
      
      const totalRefundAmount = successfulRefunds.reduce((sum, refund) => sum + refund.refundAmount, 0);
      
      return {
        classId: params.classId,
        scheduleGroupId: params.scheduleGroupId,
        cancelledAt: cancelledScheduleGroup[0].updatedAt!.toISOString(),
        refundedCount: successfulRefunds.length,
        totalRefundAmount,
        refunds: successfulRefunds,
      };
    });
  },

  /**
   * 파트너 소유권 및 클래스 상태 검증
   */
  async validateClassAndOwnership(params: { classId: string; partnerId: string }) {
    const [classInfo] = await classCancellationQueries.findClassByPartner(params.classId, params.partnerId);
    
    if (!classInfo) {
      throw new BusinessRuleError('CLASS_NOT_FOUND', '클래스를 찾을 수 없거나 접근 권한이 없습니다');
    }
    
    return classInfo;
  },

  /**
   * 취소 조건 검증 (시작일 확인, D-1 마감 확인, 이미 취소된 상태 확인)
   */
  async validateCancellationConditions(params: { classId: string; scheduleGroupId: number }) {
    const [scheduleGroup] = await classCancellationQueries.findScheduleGroupById(params.classId, params.scheduleGroupId);
    
    if (!scheduleGroup) {
      throw new BusinessRuleError('SCHEDULE_GROUP_NOT_FOUND', '스케줄 그룹을 찾을 수 없습니다');
    }
    
    if (scheduleGroup.status === 'cancelled') {
      throw new BusinessRuleError('ALREADY_CANCELLED', '이미 취소된 클래스입니다');
    }
    
    // 시작일이 설정된 경우에만 시간 관련 검증 수행 (확정된 클래스)
    if (scheduleGroup.startDate) {
      const startDate = new Date(scheduleGroup.startDate);
      const now = new Date();
      const oneDayBefore = new Date(startDate);
      oneDayBefore.setDate(oneDayBefore.getDate() - 1);
      
      // 이미 시작된 수업인지 확인
      if (now >= startDate) {
        throw new BusinessRuleError('CLASS_ALREADY_STARTED', '이미 진행 중인 수업은 취소할 수 없습니다');
      }
      
      // D-1 마감 확인
      if (now >= oneDayBefore) {
        throw new BusinessRuleError('CANCELLATION_DEADLINE_PASSED', '수업 시작 하루 전부터는 취소할 수 없습니다');
      }
    }
    
    return scheduleGroup;
  },

  /**
   * 취소 대상 수강신청 및 결제 정보 조회
   */
  async getEnrollmentsAndPayments(params: { classId: string; scheduleGroupId: number }) {
    const enrollments = await classCancellationQueries.findEnrollmentsToCancel(
      params.classId, 
      params.scheduleGroupId
    );
    
    if (enrollments.length === 0) {
      return { enrollments: [], payments: [] };
    }
    
    const payments = await classCancellationQueries.findPaymentsByEnrollmentIds(
      enrollments.map(e => e.id)
    );
    
    return { enrollments, payments };
  },

  /**
   * 토스페이먼츠 환불 처리
   */
  async processRefunds(payments: PaymentInfo[], cancelReason: string): Promise<RefundProcessResult[]> {
    const refundPromises = payments.map(async (payment): Promise<RefundProcessResult> => {
      try {
        // 1. 결제 상태 검증
        if (payment.paymentStatus !== 'paid') {
          return {
            enrollmentId: payment.enrollmentId,
            memberId: payment.memberId,
            paymentId: payment.id,
            refundAmount: payment.amount,
            status: 'failed',
            error: '취소 가능한 결제 상태가 아닙니다'
          };
        }

        // 2. paymentKey 검증 및 선택 (pgTransactionId 우선, 토스페이먼츠 권장)
        const paymentKey = payment.pgTransactionId || payment.transactionId;
        
        if (!paymentKey) {
          return {
            enrollmentId: payment.enrollmentId,
            memberId: payment.memberId,
            paymentId: payment.id,
            refundAmount: payment.amount,
            status: 'failed',
            error: '결제 키를 찾을 수 없습니다'
          };
        }

        // 3. 결제 방법별 취소 제한 확인
        const cancellationValidation = this.validateCancellationByPaymentMethod(payment);
        if (!cancellationValidation.isValid) {
          return {
            enrollmentId: payment.enrollmentId,
            memberId: payment.memberId,
            paymentId: payment.id,
            refundAmount: payment.amount,
            status: 'failed',
            error: cancellationValidation.reason
          };
        }

        // 4. Idempotency Key 생성 (중복 취소 방지)
        const idempotencyKey = `cancel_${payment.id}_${Date.now()}`;

        await cancelTossPayment(paymentKey, {
          cancelReason: cancelReason,
          cancelAmount: payment.amount,
        }, idempotencyKey);

        return {
          enrollmentId: payment.enrollmentId,
          memberId: payment.memberId,
          paymentId: payment.id,
          refundAmount: payment.amount,
          status: 'success'
        };
      } catch (error) {
        console.error(`환불 실패 - 결제 ID: ${payment.id}`, error);
        return {
          enrollmentId: payment.enrollmentId,
          memberId: payment.memberId,
          paymentId: payment.id,
          refundAmount: payment.amount,
          status: 'failed',
          error: error instanceof Error ? error.message : '환불 처리 중 오류가 발생했습니다'
        };
      }
    });

    return Promise.all(refundPromises);
  },

  /**
   * 결제 방법별 취소 제한 확인
   */
  validateCancellationByPaymentMethod(payment: PaymentInfo): { isValid: boolean; reason?: string } {
    if (!payment.paidAt) {
      return { isValid: false, reason: '결제 일시를 확인할 수 없습니다' };
    }

    const now = new Date();
    const paidDate = new Date(payment.paidAt);
    const daysDiff = Math.floor((now.getTime() - paidDate.getTime()) / (1000 * 60 * 60 * 24));

    switch (payment.paymentMethod) {
      case 'virtual_account':
        // 가상계좌: 365일 이내
        if (daysDiff > 365) {
          return { isValid: false, reason: '가상계좌 결제는 365일 이내에만 취소 가능합니다' };
        }
        break;
      
      case 'mobile_payment':
        // 휴대폰 결제: 당월 내만 가능
        const paidMonth = paidDate.getMonth();
        const paidYear = paidDate.getFullYear();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();
        
        if (paidYear !== currentYear || paidMonth !== currentMonth) {
          return { isValid: false, reason: '휴대폰 결제는 당월 내에만 취소 가능합니다' };
        }
        break;
      
      case 'account_transfer':
        // 계좌이체: 180일 이내
        if (daysDiff > 180) {
          return { isValid: false, reason: '계좌이체는 180일 이내에만 취소 가능합니다' };
        }
        break;
      
      case 'credit_card':
      case 'debit_card':
        // 카드 결제: 1년 이내 권장 (강제 제한은 없음)
        if (daysDiff > 365) {
          console.warn(`카드 결제 취소 권장 기간(1년)을 초과했습니다. 결제 ID: ${payment.id}`);
        }
        break;
      
      default:
        // 기타 결제 수단: 1년 이내 기본 제한
        if (daysDiff > 365) {
          return { isValid: false, reason: '결제 후 1년이 지나 취소할 수 없습니다' };
        }
        break;
    }

    return { isValid: true };
  }
};