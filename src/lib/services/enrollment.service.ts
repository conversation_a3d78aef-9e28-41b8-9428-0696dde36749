import { db } from '@/lib/db';
import {
  enrollments,
  payments,
  paymentEvents,
  paymentPrepare,
  type Enrollment,
  type NewPaymentEvent,
} from '@/lib/db/schema';
import {
  classes,
  class_schedule_groups as classScheduleGroups,
  class_schedules as classSchedules,
} from '@/lib/db/schema';
import { eq, and, ne, sql, lt } from 'drizzle-orm';
import { confirmTossPayment, cancelTossPayment } from '@/lib/api/toss-payments';

interface CreateEnrollmentParams {
  memberId: string;
  classId: string;
  scheduleGroupId: number;
}

interface PreparePaymentParams {
  enrollmentId: string;
  orderId: string;
  amount: number;
  memberId: string;
}

interface ConfirmPaymentParams {
  paymentKey: string;
  orderId: string;
  amount: number;
  memberId: string;
}

interface CancelPaymentParams {
  paymentKey: string;
  memberId: string;
  cancelReason?: string; // 사용자 입력 취소 사유 (선택적)
}

interface RefundPolicy {
  isRefundable: boolean;
  refundRate: number; // 0.0 ~ 1.0
  refundAmount: number;
  reason: string;
}

// 토스페이먼츠 위젯 초기화 데이터
interface PreparePaymentWidget {
  clientKey: string;
  amount: number;
  orderId: string;
  orderName: string;
  customerName: string;
  successUrl: string;
  failUrl: string;
}

// 비즈니스 규칙 위반 시 사용하는 커스텀 에러
class BusinessRuleError extends Error {
  constructor(
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'BusinessRuleError';
  }
}

/**
 * 수강신청 관련 비즈니스 로직 처리 (Drizzle Style)
 */
export const enrollmentService = {
  /**
   * 수강신청 + 결제 준비
   */
async createEnrollment(params: CreateEnrollmentParams) {
    const result = await db.transaction(async tx => {
      try {
        // 1. 기존 수강신청 내역 확인 및 처리
        const existingEnrollment = await this.handleExistingEnrollment(
          tx,
          params
        );

        // 2. 수업 정보 유효성 검사 및 조회
        const classInfo = await this.validateAndGetClassInfo(
          tx,
          params.classId,
          params.scheduleGroupId
        );

        // 3. 금액 계산 (총액, 예약금)
        const { totalAmount, depositAmount } = this.calculatePricing(classInfo);

        let enrollment;

        // 4. 기존 enrollment 재사용 또는 신규 생성
        if (existingEnrollment) {
          enrollment = existingEnrollment;

          // 가격이 변경된 경우 업데이트
          if (
            enrollment.totalAmount !== totalAmount ||
            enrollment.depositAmount !== depositAmount
          ) {
            [enrollment] = await tx
              .update(enrollments)
              .set({
                totalAmount,
                depositAmount,
                updatedAt: new Date(),
              })
              .where(eq(enrollments.id, enrollment.id))
              .returning();
          }
        } else {
          // 새로운 enrollment 생성
          [enrollment] = await tx
            .insert(enrollments)
            .values({
              memberId: params.memberId,
              classId: params.classId,
              scheduleGroupId: params.scheduleGroupId,
              status: 'pending',
              totalAmount,
              depositAmount,
              enrollmentOrder: null,
            })
            .returning();
        }

        // 5. 결제 위젯 데이터 준비
        const paymentWidget = await this.preparePaymentWidget(
          enrollment,
          classInfo,
          depositAmount
        );

        return {
          enrollment,
          paymentWidget,
        };
      } catch (error) {
        console.error('Enrollment creation error:', error);
        throw error;
      }
    });

    return result;
  },

  /**
   * 기존 신청 확인 및 처리
   * - pending 상태: 기존 enrollment 재사용
   * - paid/confirmed 상태: 중복 에러
   * - cancelled 상태: 새 신청 허용
   */
  async handleExistingEnrollment(tx: any, params: CreateEnrollmentParams) {
    // 취소된 것을 제외한 기존 수강신청 조회
    const existing = await tx
      .select()
      .from(enrollments)
      .where(
        and(
          eq(enrollments.memberId, params.memberId),
          eq(enrollments.classId, params.classId),
          eq(enrollments.scheduleGroupId, params.scheduleGroupId),
          ne(enrollments.status, 'cancelled')
        )
      )
      .limit(1);

    if (existing.length > 0) {
      const enrollment = existing[0];

      // pending 상태면 재사용
      if (enrollment.status === 'pending') {
        return enrollment;
      }

      // 이미 결제완료된 상태면 중복 에러
      if (enrollment.status === 'paid' || enrollment.status === 'confirmed') {
        throw new BusinessRuleError(
          'DUPLICATE_ENROLLMENT',
          '이미 해당 수업에 신청하셨습니다'
        );
      }
    }

    return null;
  },

  /**
   * 수업 정보 검증 및 조회 (금액 정보 포함)
   */
  async validateAndGetClassInfo(
    tx: any,
    classId: string,
    scheduleGroupId: number
  ) {
    // 수업 정보 조회 (가격 계산에 필요한 정보 포함)
    const [classInfo] = await tx
      .select({
        id: classes.id,
        title: classes.title,
        status: classes.status,
        pricePerSession: classes.price_per_session,
        sessionsPerWeek: classes.sessions_per_week,
        durationWeeks: classes.duration_weeks,
      })
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);

    if (!classInfo) {
      throw new BusinessRuleError('CLASS_NOT_FOUND', '수업을 찾을 수 없습니다');
    }

    // 수업이 활성 상태인지 확인
    if (classInfo.status !== 'active') {
      throw new BusinessRuleError(
        'CLASS_NOT_ACTIVE',
        '현재 신청할 수 없는 수업입니다'
      );
    }

    // 해당 수업의 스케줄 그룹이 존재하는지 검증
    const [scheduleGroup] = await tx
      .select()
      .from(classScheduleGroups)
      .where(
        and(
          eq(classScheduleGroups.class_id, classId),
          eq(classScheduleGroups.id, scheduleGroupId)
        )
      )
      .limit(1);

    if (!scheduleGroup) {
      throw new BusinessRuleError(
        'SCHEDULE_GROUP_NOT_FOUND',
        '수업 일정을 찾을 수 없습니다'
      );
    }

    return classInfo;
  },

  /**
   * 금액 계산 (비즈니스 규칙 적용)
   */
  calculatePricing(classInfo: any) {
    // 총 세션 수 계산
    const totalSessions = classInfo.sessionsPerWeek * classInfo.durationWeeks;

    // 총 금액 계산
    const totalAmount = classInfo.pricePerSession * totalSessions;

    // 예약금 계산 (15% 고정)
    const depositAmount = Math.floor(totalAmount * 0.15);

    return {
      totalAmount,
      depositAmount,
      totalSessions,
    };
  },

  /**
   * 결제 관련 이벤트 로깅 (추적 및 디버깅용)
   */
  async logEvent(
    tx: any,
    eventData: Omit<NewPaymentEvent, 'id' | 'createdAt'>
  ) {
    await tx.insert(paymentEvents).values({
      ...eventData,
    });
  },

  /**
   * 결제 승인 처리 (모든 비즈니스 로직 포함)
   */
  async confirmPayment(params: ConfirmPaymentParams) {
    const { paymentKey, orderId, amount, memberId } = params;

    return await db.transaction(async tx => {
      try {
        // 1. enrollment 존재 및 권한 확인
        const enrollment = await this.validateEnrollmentOwnership(
          tx,
          orderId,
          memberId
        );

        // 2. 결제 금액 일치성 확인
        this.validatePaymentAmount(enrollment, amount);

        // 3. 준비된 결제 정보 검증 및 사용 처리
        const prepare = await this.validatePreparedPayment(
          tx,
          orderId,
          amount,
          memberId
        );
        await this.markPrepareAsUsed(tx, prepare.id);

        // 4. 중복 결제 확인
        await this.validateDuplicatePayment(tx, paymentKey);

        // 5. 토스페이먼츠 승인 API 호출
        const tossResult = await confirmTossPayment({
          paymentKey,
          orderId,
          amount,
        });

        // 6. enrollment_order 할당 (동시성 제어)
        const enrollmentOrder = await this.assignEnrollmentOrder(
          tx,
          enrollment.classId,
          enrollment.scheduleGroupId
        );

        // 7. payment 레코드 생성
        const [payment] = await tx
          .insert(payments)
          .values({
            enrollmentId: enrollment.id,
            memberId,
            amount,
            paymentType: 'deposit',
            paymentMethod: tossResult.method,
            paymentStatus: 'paid',
            currency: 'KRW',
            transactionId: paymentKey,
            pgTransactionId: tossResult.paymentKey,
            merchantUid: orderId,
            paymentData: {
              method: tossResult.method,
              approvedAt: tossResult.approvedAt,
              totalAmount: tossResult.totalAmount,
            },
            paidAt: new Date(),
          })
          .returning();

        // 8. enrollment 상태 업데이트
        const [updatedEnrollment] = await tx
          .update(enrollments)
          .set({
            status: 'paid',
            enrollmentOrder,
            updatedAt: new Date(),
          })
          .where(eq(enrollments.id, orderId))
          .returning();

        // 9. 성공 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: payment.id,
          enrollmentId: enrollment.id,
          eventType: 'payment_succeeded',
          eventData: {
            paymentKey,
            enrollmentOrder,
            amount,
            method: tossResult.method,
            memberId,
          },
        });

        return {
          enrollment: updatedEnrollment,
          payment,
          tossResult,
        };
      } catch (error) {
        // 비즈니스 룰 에러는 그대로 전파
        if (error instanceof BusinessRuleError) {
          throw error;
        }

        console.error('error: ', error);

        // 예상치 못한 오류 시 실패 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: null, // payment가 생성되지 않았으므로 null
          enrollmentId: orderId,
          eventType: 'payment_failed',
          eventData: {
            paymentKey,
            amount,
            memberId,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
          errorMessage:
            error instanceof Error ? error.message : 'Unknown error',
        });

        throw new BusinessRuleError(
          'PAYMENT_PROCESSING_FAILED',
          '결제 처리 중 오류가 발생했습니다'
        );
      }
    });
  },

  /**
   * enrollment 존재 및 소유권 확인
   */
  async validateEnrollmentOwnership(
    tx: any,
    enrollmentId: string,
    memberId: string
  ) {
    // pending 상태의 enrollment만 결제 가능
    const [enrollment] = await tx
      .select()
      .from(enrollments)
      .where(
        eq(enrollments.id, enrollmentId),
        eq(enrollments.memberId, memberId),
        eq(enrollments.status, 'pending')
      )
      .limit(1);

    if (!enrollment) {
      throw new BusinessRuleError(
        'ENROLLMENT_NOT_FOUND',
        '수강신청 정보를 찾을 수 없습니다'
      );
    }

    return enrollment;
  },

  /**
   * 결제 금액 일치성 확인
   */
  validatePaymentAmount(enrollment: any, amount: number) {
    if (enrollment.depositAmount !== amount) {
      throw new BusinessRuleError(
        'INVALID_PAYMENT_AMOUNT',
        '결제 금액이 일치하지 않습니다'
      );
    }
  },

  /**
   * 중복 결제 확인
   */
  async validateDuplicatePayment(tx: any, paymentKey: string) {
    // 동일한 paymentKey로 이미 처리된 결제가 있는지 확인
    const existingPayment = await tx
      .select()
      .from(payments)
      .where(eq(payments.transactionId, paymentKey))
      .limit(1);

    if (existingPayment.length > 0) {
      throw new BusinessRuleError(
        'ENROLLMENT_ALREADY_PROCESSED',
        '이미 처리된 결제입니다'
      );
    }
  },

  /**
   * enrollment_order 할당 (동시성 제어)
   * 같은 클래스+스케줄그룹 내에서 신청 순서를 부여
   */
  async assignEnrollmentOrder(
    tx: any,
    classId: string,
    scheduleGroupId: number
  ): Promise<number> {
    // 동시성 제어를 위한 락 (같은 클래스+스케줄그룹의 paid 상태 enrollment들)
    await tx
      .select()
      .from(enrollments)
      .where(
        and(
          eq(enrollments.classId, classId),
          eq(enrollments.scheduleGroupId, scheduleGroupId),
          eq(enrollments.status, 'paid')
        )
      )
      .for('update');

    // 현재 최대 order 조회하여 다음 순번 계산
    const [result] = await tx
      .select({
        maxOrder: sql<number>`COALESCE(MAX(${enrollments.enrollmentOrder}), 0)`,
      })
      .from(enrollments)
      .where(
        and(
          eq(enrollments.classId, classId),
          eq(enrollments.scheduleGroupId, scheduleGroupId),
          eq(enrollments.status, 'paid')
        )
      );

    return result.maxOrder + 1;
  },

  /**
   * 결제 준비 처리 (TossPayments 보안 가이드라인)
   */
  async preparePayment(params: PreparePaymentParams): Promise<string> {
    const { enrollmentId, orderId, amount, memberId } = params;

    return await db.transaction(async tx => {
      // 수강신청 유효성 검증
      const enrollment = await this.validateEnrollmentOwnership(
        tx,
        enrollmentId,
        memberId
      );

      // 결제 금액 검증
      this.validatePaymentAmount(enrollment, amount);

      // 만료된 준비 정보 정리
      await this.cleanupExpiredPrepares(tx);

      // 기존 준비 정보가 있는지 확인
      const existingPrepare = await tx
        .select()
        .from(paymentPrepare)
        .where(
          and(
            eq(paymentPrepare.enrollmentId, enrollmentId),
            eq(paymentPrepare.orderId, orderId),
            eq(paymentPrepare.isUsed, 0)
          )
        )
        .limit(1);

      if (existingPrepare.length > 0) {
        return existingPrepare[0].id;
      }

      // 새로운 준비 정보 생성 (15분 유효)
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 15);

      const [prepare] = await tx
        .insert(paymentPrepare)
        .values({
          enrollmentId,
          memberId,
          classId: enrollment.classId,
          scheduleGroupId: enrollment.scheduleGroupId,
          orderId,
          amount,
          isUsed: 0,
          expiresAt,
        })
        .returning();

      // 준비 완료 이벤트 로깅
      await this.logEvent(tx, {
        paymentId: null,
        enrollmentId,
        eventType: 'payment_prepared',
        eventData: {
          prepareId: prepare.id,
          orderId,
          amount,
          classId: enrollment.classId,
          scheduleGroupId: enrollment.scheduleGroupId,
          memberId,
        },
      });

      return prepare.id;
    });
  },

  /**
   * 만료된 준비 정보 정리
   */
  async cleanupExpiredPrepares(tx: any) {
    await tx
      .delete(paymentPrepare)
      .where(lt(paymentPrepare.expiresAt, new Date()));
  },

  /**
   * 준비된 결제 정보 검증
   */
  async validatePreparedPayment(
    tx: any,
    orderId: string,
    amount: number,
    memberId: string
  ) {
    // 사용되지 않은 준비 정보 조회
    const [prepare] = await tx
      .select()
      .from(paymentPrepare)
      .where(
        and(
          eq(paymentPrepare.orderId, orderId),
          eq(paymentPrepare.memberId, memberId),
          eq(paymentPrepare.isUsed, 0)
        )
      )
      .limit(1);

    if (!prepare) {
      throw new BusinessRuleError(
        'PREPARE_NOT_FOUND',
        '결제 준비 정보를 찾을 수 없습니다'
      );
    }

    // 만료 시간 확인
    if (prepare.expiresAt < new Date()) {
      throw new BusinessRuleError(
        'PREPARE_NOT_FOUND',
        '결제 준비 시간이 만료되었습니다'
      );
    }

    // 금액 일치성 확인
    if (prepare.amount !== amount) {
      throw new BusinessRuleError(
        'AMOUNT_MISMATCH',
        '결제 금액이 일치하지 않습니다'
      );
    }

    return prepare;
  },

  /**
   * 준비 정보 사용 처리
   */
  async markPrepareAsUsed(tx: any, prepareId: string) {
    await tx
      .update(paymentPrepare)
      .set({
        isUsed: 1,
      })
      .where(eq(paymentPrepare.id, prepareId));
  },

  /**
   * 결제 위젯 정보 준비
   */
  async preparePaymentWidget(
    enrollment: Enrollment,
    classInfo: any,
    depositAmount: number
  ): Promise<PreparePaymentWidget> {
    // 환경변수 확인
    const clientKey = process.env.NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

    if (!clientKey) {
      throw new Error('NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY is missing');
    }
    if (!baseUrl) {
      throw new Error('NEXT_PUBLIC_BASE_URL is missing');
    }

    // 토스페이먼츠 위젯 초기화 데이터 반환
    return {
      clientKey,
      amount: depositAmount,
      orderId: enrollment.id,
      orderName: `${classInfo.title} 수업 예약금`,
      customerName: '회원님', // 실제로는 member 정보에서 가져옴
      successUrl: `${baseUrl}/payment/success`,
      failUrl: `${baseUrl}/payment/fail`,
    };
  },

  /**
   * 결제 취소 처리 (환불 정책 적용)
   */
  async cancelPayment(params: CancelPaymentParams) {
    const { paymentKey, memberId, cancelReason } = params;

    return await db.transaction(async (tx) => {
      try {
        // 1. 결제 정보 조회 및 권한 확인
        const payment = await this.validatePaymentOwnership(tx, paymentKey, memberId);

        // 2. 취소 가능 상태 확인
        this.validateCancelableStatus(payment);

        // 3. enrollment 정보 조회
        const [enrollment] = await tx
          .select()
          .from(enrollments)
          .where(eq(enrollments.id, payment.enrollmentId))
          .limit(1);

        if (!enrollment) {
          throw new BusinessRuleError('ENROLLMENT_NOT_FOUND', '수강신청 정보를 찾을 수 없습니다');
        }

        // 4. 수업 시작 시간 조회
        const classStartDateTime = await this.getClassStartDateTime(
          tx, 
          enrollment.classId, 
          enrollment.scheduleGroupId
        );

        // 5. 환불 정책 적용
        const refundPolicy = await this.validateRefundPolicy(classStartDateTime, payment.amount);

        if (!refundPolicy.isRefundable) {
          throw new BusinessRuleError('REFUND_NOT_ALLOWED', refundPolicy.reason);
        }

        // 6. 토스페이먼츠 취소 API 호출 (부분 취소 지원)
        const cancelParams: any = {
          cancelReason: `${refundPolicy.reason} (${refundPolicy.refundRate * 100}% 환불)`,
        };

        // 부분 환불인 경우 취소 금액 지정
        if (refundPolicy.refundRate < 1.0) {
          cancelParams.cancelAmount = refundPolicy.refundAmount;
        }

        const tossResult = await cancelTossPayment(paymentKey, cancelParams);

        // 7. payment 상태 업데이트
        const [updatedPayment] = await tx
          .update(payments)
          .set({
            paymentStatus: 'cancelled',
            updatedAt: new Date(),
            paymentData: {
              ...payment.paymentData,
              cancels: tossResult.cancels,
              canceledAt: tossResult.canceledAt,
              refundPolicy: {
                refundRate: refundPolicy.refundRate,
                refundAmount: refundPolicy.refundAmount,
                reason: refundPolicy.reason,
              },
            }
          })
          .where(eq(payments.transactionId, paymentKey))
          .returning();

        // 8. enrollment 상태 업데이트
        const cancellationReason = cancelReason || refundPolicy.reason; // 사용자 입력 사유 우선, 없으면 정책 사유
        const [updatedEnrollment] = await tx
          .update(enrollments)
          .set({
            status: 'cancelled',
            cancellationReason,
            updatedAt: new Date(),
          })
          .where(eq(enrollments.id, payment.enrollmentId))
          .returning();

        // 9. 취소 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: payment.id,
          enrollmentId: payment.enrollmentId,
          eventType: 'payment_cancelled',
          eventData: {
            paymentKey,
            cancelReason: refundPolicy.reason,
            userReason: cancelReason, // 사용자 입력 사유
            finalReason: cancellationReason, // 최종 저장된 사유
            originalAmount: payment.amount,
            refundAmount: refundPolicy.refundAmount,
            refundRate: refundPolicy.refundRate,
            memberId,
            tossTransactionKey: tossResult.cancels[0]?.transactionKey,
            classStartDateTime: classStartDateTime?.toISOString(),
          },
        });

        return {
          payment: updatedPayment,
          enrollment: updatedEnrollment,
          tossResult,
          refundPolicy,
        };

      } catch (error) {
        // 비즈니스 룰 에러는 그대로 전파
        if (error instanceof BusinessRuleError) {
          throw error;
        }

        console.error('Payment cancel error: ', error);

        // 예상치 못한 오류 시 실패 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: null,
          enrollmentId: 'unknown',
          eventType: 'payment_failed',
          eventData: {
            paymentKey,
            memberId,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        });

        throw new BusinessRuleError('PAYMENT_PROCESSING_FAILED', '결제 취소 처리 중 오류가 발생했습니다');
      }
    });
  },

  /**
   * 결제 정보 조회 및 소유권 확인
   */
  async validatePaymentOwnership(tx: any, paymentKey: string, memberId: string) {
    // 본인의 결제 정보만 조회 가능
    const [payment] = await tx
      .select()
      .from(payments)
      .where(
        and(
          eq(payments.transactionId, paymentKey),
          eq(payments.memberId, memberId)
        )
      )
      .limit(1);

    if (!payment) {
      throw new BusinessRuleError('PAYMENT_NOT_FOUND', '결제 정보를 찾을 수 없습니다');
    }

    return payment;
  },

  /**
   * 취소 가능 상태 확인
   */
  validateCancelableStatus(payment: any) {
    if (payment.paymentStatus === 'cancelled') {
      throw new BusinessRuleError('PAYMENT_ALREADY_CANCELLED', '이미 취소된 결제입니다');
    }

    if (payment.paymentStatus !== 'paid') {
      throw new BusinessRuleError('PAYMENT_NOT_CANCELLABLE', '취소할 수 없는 결제 상태입니다');
    }
  },

  /**
   * 수업 시작 시간 계산
   */
  async getClassStartDateTime(tx: any, classId: string, scheduleGroupId: number): Promise<Date | null> {
    // 1. 스케줄 그룹 정보 조회 (start_date)
    const [scheduleGroup] = await tx
      .select()
      .from(classScheduleGroups)
      .where(
        and(
          eq(classScheduleGroups.class_id, classId),
          eq(classScheduleGroups.id, scheduleGroupId)
        )
      )
      .limit(1);

    if (!scheduleGroup || !scheduleGroup.start_date) {
      return null; // 수업 시작일이 확정되지 않음
    }

    // 2. 해당 그룹의 첫 번째 수업 스케줄 조회
    const [firstSchedule] = await tx
      .select()
      .from(classSchedules)
      .where(eq(classSchedules.schedule_group_id, scheduleGroupId))
      .orderBy(classSchedules.day_of_week, classSchedules.start_time)
      .limit(1);

    if (!firstSchedule) {
      return null;
    }

    // 3. 시작 날짜와 첫 번째 수업 시간을 결합
    const startDate = new Date(scheduleGroup.start_date);
    const [hours, minutes] = firstSchedule.start_time.split(':').map(Number);
    
    startDate.setHours(hours, minutes, 0, 0);
    
    return startDate;
  },

  /**
   * 환불 정책 검증
   */
  async validateRefundPolicy(classStartDateTime: Date | null, originalAmount: number): Promise<RefundPolicy> {
    if (!classStartDateTime) {
      return { 
        isRefundable: true, 
        refundRate: 1.0, 
        refundAmount: originalAmount,
        reason: "수업이 확정되지 않아 100% 환불됩니다" 
      };
    }

    const now = new Date();
    const hoursUntilClass = (classStartDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    // 수업이 이미 시작됨
    if (hoursUntilClass < 0) {
      return { 
        isRefundable: false, 
        refundRate: 0, 
        refundAmount: 0,
        reason: "수업이 이미 시작되어 취소할 수 없습니다" 
      };
    }
    
    // 72시간 전: 100% 환불
    if (hoursUntilClass >= 72) {
      return { 
        isRefundable: true, 
        refundRate: 1.0, 
        refundAmount: originalAmount,
        reason: "72시간 전 전액 환불" 
      };
    }
    
    // 24시간 전: 50% 환불
    if (hoursUntilClass >= 24) {
      const refundAmount = Math.floor(originalAmount * 0.5);
      return { 
        isRefundable: true, 
        refundRate: 0.5, 
        refundAmount,
        reason: "24시간 전 50% 환불" 
      };
    }
    
    // 24시간 이내: 환불 불가
    return { 
      isRefundable: false, 
      refundRate: 0, 
      refundAmount: 0,
      reason: "24시간 이내로 환불이 불가능합니다" 
    };
  },

};
