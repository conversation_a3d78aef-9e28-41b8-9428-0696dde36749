import { db } from '@/lib/db';
import { classes, studios, instructors } from '@/lib/db/schema';
import { enrollments } from '@/lib/db/schema/enrollments';
import { eq, and, sql, desc, inArray } from 'drizzle-orm';
import { getNearbyStations } from '@/lib/constants/station-mapping';
import type {
  ClassListQuery,
  ClassListResponse,
  ClassDetailResponse,
} from '@/app/api/classes/schema';

export class ClassService {
  /**
   * 클래스 목록 조회
   *
   * @param params 조회 파라미터
   * @returns 클래스 목록과 페이지네이션 정보
   */
  async getClassList(params: ClassListQuery): Promise<ClassListResponse> {
    // Build where conditions
    const whereConditions = [
      eq(classes.visible, true),
      eq(classes.status, 'active'),
    ];

    // Add filters
    if (params.nearestStation) {
      const stationValues = Array.isArray(params.nearestStation)
        ? params.nearestStation
        : [params.nearestStation];

      // Get all nearby stations for all selected stations
      const allNearbyStations = stationValues.flatMap(station =>
        getNearbyStations(station)
      );

      // Remove duplicates
      const uniqueStations = [...new Set(allNearbyStations)];

      // Use IN clause for nearby stations
      if (uniqueStations.length > 0) {
        whereConditions.push(inArray(studios.nearestStation, uniqueStations));
      }
    }

    if (params.category) {
      const categoryValues = Array.isArray(params.category)
        ? params.category
        : [params.category];
      whereConditions.push(inArray(classes.category, categoryValues));
    }

    if (params.level) {
      const levelValues = Array.isArray(params.level)
        ? params.level
        : [params.level];
      whereConditions.push(inArray(classes.level, levelValues));
    }

    // Calculate offset
    const offset = (params.page - 1) * params.limit;

    // Execute main query
    const classesQuery = db
      .select({
        id: classes.id,
        title: classes.title,
        description: classes.description,
        category: classes.category,
        level: classes.level,
        target: classes.target,
        maxParticipants: classes.max_participants,
        pricePerSession: classes.price_per_session,
        sessionsPerWeek: classes.sessions_per_week,
        images: classes.images,
        createdAt: classes.created_at,

        // Studio fields
        studioId: studios.id,
        studioName: studios.name,
        studioNearestStation: studios.nearestStation,
        studioStationDistance: studios.stationDistance,

        // Instructor fields
        instructorId: instructors.id,
        instructorName: instructors.name,
        instructorExperienceYears: instructors.experienceTotalYears,

        // Enrollment count (subquery)
        enrollmentCount: sql<number>`(
          SELECT COUNT(*)::int 
          FROM ${enrollments} 
          WHERE ${enrollments.classId} = ${classes.id}
        )`.as('enrollment_count'),
      })
      .from(classes)
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .innerJoin(instructors, eq(classes.instructor_id, instructors.id))
      .where(and(...whereConditions))
      .orderBy(
        ...(params.sort === 'popular'
          ? [desc(sql`enrollment_count`), desc(classes.created_at)]
          : [desc(classes.created_at)])
      )
      .limit(params.limit)
      .offset(offset);

    // Execute count query for pagination
    const countQuery = db
      .select({
        count: sql<number>`count(*)::int`,
      })
      .from(classes)
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .innerJoin(instructors, eq(classes.instructor_id, instructors.id))
      .where(and(...whereConditions));

    // Execute both queries
    const [classesResult, countResult] = await Promise.all([
      classesQuery,
      countQuery,
    ]);

    const totalCount = countResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / params.limit);

    // Transform results
    const transformedClasses = classesResult.map(row => ({
      id: row.id,
      title: row.title,
      description: row.description,
      category: row.category,
      level: row.level,
      target: row.target,
      maxParticipants: row.maxParticipants,
      pricePerSession: row.pricePerSession,
      sessionsPerWeek: row.sessionsPerWeek,
      createdAt: row.createdAt?.toISOString() || new Date().toISOString(),
      enrollmentCount: row.enrollmentCount,

      studio: {
        id: row.studioId,
        name: row.studioName,
        nearestStation: row.studioNearestStation,
        stationDistance: row.studioStationDistance,
      },

      instructor: {
        id: row.instructorId,
        name: row.instructorName,
        experienceTotalYears: row.instructorExperienceYears,
      },

      // Extract first image URL as thumbnail
      thumbnailUrl:
        row.images && Array.isArray(row.images) && row.images.length > 0
          ? row.images[0].url || null
          : null,
    }));

    // Build response
    return {
      classes: transformedClasses,
      pagination: {
        page: params.page,
        limit: params.limit,
        total: totalCount,
        totalPages,
      },
    };
  }

  /**
   * 클래스 상세 조회
   *
   * @param classId 클래스 ID
   * @returns 클래스 상세 정보
   */
  async getClassDetail(classId: string): Promise<ClassDetailResponse | null> {
    // Get level-based recommendation targets as array
    const getRecommendedFor = (level: string): string[] => {
      switch (level) {
        case 'beginner':
          return [
            '운동을 처음 시작하는 분',
            '체력 증진을 원하는 분',
            '기초부터 차근차근 배우고 싶은 분',
          ];
        case 'intermediate':
          return [
            '기본기가 있으신 분',
            '운동 경험이 3개월 이상 있으신 분',
            '다음 단계로 도전하고 싶으신 분',
          ];
        case 'advanced':
          return [
            '숙련자 분',
            '고난도 동작에 도전하고 싶으신 분',
            '전문적인 기술을 익히고 싶으신 분',
          ];
        default:
          return ['누구나 참여 가능', '모든 레벨 환영'];
      }
    };

    // Query class with studio and instructor
    const result = await db
      .select({
        // Class fields
        id: classes.id,
        title: classes.title,
        description: classes.description,
        category: classes.category,
        level: classes.level,
        target: classes.target,
        maxParticipants: classes.max_participants,
        pricePerSession: classes.price_per_session,
        sessionDurationMinutes: classes.session_duration_minutes,
        durationWeeks: classes.duration_weeks,
        sessionsPerWeek: classes.sessions_per_week,
        images: classes.images,
        createdAt: classes.created_at,

        // Studio fields
        studioId: studios.id,
        studioName: studios.name,
        studioNearestStation: studios.nearestStation,
        studioStationDistance: studios.stationDistance,
        studioImages: studios.images,
        studioLinks: studios.links,
        studioDescription: studios.description,
        studioAmenities: studios.amenities,
        studioAddress: studios.address,
        studioAddressDetail: studios.addressDetail,
        studioPhone: studios.phone,
        studioLatitude: studios.latitude,
        studioLongitude: studios.longitude,

        // Instructor fields
        instructorId: instructors.id,
        instructorName: instructors.name,
        instructorExperienceYears: instructors.experienceTotalYears,
        instructorDescription: instructors.description,
        instructorCertificates: instructors.certificates,
        instructorProfileImages: instructors.profileImages,
      })
      .from(classes)
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .innerJoin(instructors, eq(classes.instructor_id, instructors.id))
      .where(
        and(
          eq(classes.id, classId),
          eq(classes.status, 'active'),
          eq(classes.visible, true)
        )
      )
      .limit(1);

    if (!result.length) {
      return null;
    }

    const classData = result[0];

    // Transform to response format
    return {
      id: classData.id,
      title: classData.title,
      description: classData.description,
      category: classData.category,
      level: classData.level,
      target: classData.target,
      recommendedFor: getRecommendedFor(classData.level),
      maxParticipants: classData.maxParticipants,
      pricePerSession: classData.pricePerSession,
      sessionDurationMinutes: classData.sessionDurationMinutes,
      durationWeeks: classData.durationWeeks,
      sessionsPerWeek: classData.sessionsPerWeek,
      images: classData.images as { url: string }[] | null,
      createdAt: classData.createdAt?.toISOString() || new Date().toISOString(),

      studio: {
        id: classData.studioId,
        name: classData.studioName,
        nearestStation: classData.studioNearestStation,
        stationDistance: classData.studioStationDistance,
        images: classData.studioImages as
          | { url: string; type?: string; alt_text?: string }[]
          | null,
        links: classData.studioLinks as {
          website?: string;
          sns?: string;
        } | null,
        description: classData.studioDescription,
        amenities: classData.studioAmenities as Record<string, any> | null,
        address: classData.studioAddress,
        addressDetail: classData.studioAddressDetail,
        phone: classData.studioPhone,
        latitude: classData.studioLatitude,
        longitude: classData.studioLongitude,
      },

      instructor: {
        id: classData.instructorId,
        name: classData.instructorName,
        experienceTotalYears: classData.instructorExperienceYears,
        description: classData.instructorDescription || '',
        certificates: classData.instructorCertificates as Array<{
          name: string;
          issuing_organization: string;
          issue_date?: string;
          expiry_date?: string;
          certificate_number?: string;
        }> | null,
        profileImage:
          classData.instructorProfileImages &&
          Array.isArray(classData.instructorProfileImages)
            ? classData.instructorProfileImages[0].url
            : null,
      },
    };
  }
}

// Singleton instance
export const classService = new ClassService();
