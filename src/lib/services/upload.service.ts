/**
 * 범용 파일 업로드 서비스
 * Supabase Storage를 사용한 이미지 업로드 관리
 */

import { createSupabaseClient } from '@/lib/supabase/client';
import { SupabaseClient } from '@supabase/supabase-js';
import {
  validateImageFile,
  validateImageFiles,
  generateUniqueFileName,
  generateStudioImagePath,
  generateInstructorImagePath,
  generateClassImagePath,
  getPublicImageUrl,
  getTransformedImageUrl,
} from '@/lib/utils/upload';

export type UploadType = 'studio' | 'instructor' | 'class';

export interface UploadOptions {
  partnerId?: string;
  studioId?: string;
  instructorId?: string;
  classId?: string;
  prefix?: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  path?: string;
  error?: string;
}

export interface MultiUploadResult {
  success: boolean;
  uploaded: Array<{
    url: string;
    path: string;
    index: number;
  }>;
  failed: Array<{
    index: number;
    error: string;
  }>;
}

export class UploadService {
  private supabase: SupabaseClient;
  private bucketName = 'images';

  constructor(supabase?: SupabaseClient) {
    this.supabase = supabase || createSupabaseClient();
  }

  /**
   * 단일 이미지 업로드
   */
  async uploadImage(
    file: File,
    type: UploadType,
    options: UploadOptions
  ): Promise<UploadResult> {
    try {
      // 파일 유효성 검사
      const validation = validateImageFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // 파일명 생성
      const fileName = generateUniqueFileName(
        file.name,
        options.prefix || 'image'
      );

      // 경로 생성
      const filePath = this.generateFilePath(type, fileName, options);
      if (!filePath) {
        return {
          success: false,
          error: '필요한 ID가 제공되지 않았습니다.',
        };
      }

      // 업로드
      const { data, error } = await this.supabase.storage
        .from(this.bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) {
        console.error('Upload error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // 공개 URL 생성
      const url = getPublicImageUrl(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        this.bucketName,
        data.path
      );

      return {
        success: true,
        url,
        path: data.path,
      };
    } catch (error) {
      console.error('Upload service error:', error);
      return {
        success: false,
        error: '이미지 업로드 중 오류가 발생했습니다.',
      };
    }
  }

  /**
   * 여러 이미지 업로드
   */
  async uploadMultipleImages(
    files: File[],
    type: UploadType,
    options: UploadOptions
  ): Promise<MultiUploadResult> {
    // 파일 개수 유효성 검사
    const validation = validateImageFiles(files);
    if (!validation.valid) {
      return {
        success: false,
        uploaded: [],
        failed: files.map((_, index) => ({
          index,
          error: validation.error || '유효성 검사 실패',
        })),
      };
    }

    const uploaded: MultiUploadResult['uploaded'] = [];
    const failed: MultiUploadResult['failed'] = [];

    // 병렬 업로드
    const uploadPromises = files.map(async (file, index) => {
      const prefix = index === 0 ? 'featured' : 'gallery';
      const result = await this.uploadImage(file, type, {
        ...options,
        prefix,
      });

      if (result.success && result.url && result.path) {
        uploaded.push({
          url: result.url,
          path: result.path,
          index,
        });
      } else {
        failed.push({
          index,
          error: result.error || '업로드 실패',
        });
      }
    });

    await Promise.all(uploadPromises);

    return {
      success: failed.length === 0,
      uploaded: uploaded.sort((a, b) => a.index - b.index),
      failed,
    };
  }

  /**
   * 이미지 삭제
   */
  async deleteImage(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase.storage
        .from(this.bucketName)
        .remove([path]);

      if (error) {
        console.error('Delete error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Delete service error:', error);
      return {
        success: false,
        error: '이미지 삭제 중 오류가 발생했습니다.',
      };
    }
  }

  /**
   * 여러 이미지 삭제
   */
  async deleteMultipleImages(
    paths: string[]
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await this.supabase.storage
        .from(this.bucketName)
        .remove(paths);

      if (error) {
        console.error('Delete error:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Delete service error:', error);
      return {
        success: false,
        error: '이미지 삭제 중 오류가 발생했습니다.',
      };
    }
  }

  /**
   * 이미지 URL에서 경로 추출
   */
  extractPathFromUrl(url: string): string | null {
    try {
      const baseUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${this.bucketName}/`;
      if (url.startsWith(baseUrl)) {
        return url.replace(baseUrl, '');
      }
      return null;
    } catch {
      return null;
    }
  }

  /**
   * 썸네일 URL 생성
   */
  getThumbnailUrl(url: string, width = 200, quality = 75): string {
    const path = this.extractPathFromUrl(url);
    if (!path) return url;

    return getTransformedImageUrl(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      this.bucketName,
      path,
      { width, quality }
    );
  }

  /**
   * 파일 경로 생성 헬퍼
   */
  private generateFilePath(
    type: UploadType,
    fileName: string,
    options: UploadOptions
  ): string | null {
    switch (type) {
      case 'studio':
        if (!options.partnerId) return null;
        return generateStudioImagePath(
          options.partnerId,
          options.studioId || null,
          fileName
        );

      case 'instructor':
        return generateInstructorImagePath(
          options.instructorId || null,
          fileName
        );

      case 'class':
        return generateClassImagePath(options.classId || null, fileName);

      default:
        return null;
    }
  }
}
