import { headers, cookies } from 'next/headers';
import { z } from 'zod';
import {
  createDevLogger,
  extractErrorMessage,
  isAbsoluteUrl,
  isRelativeUrl,
  parseJsonSafe,
  type BaseRequestInit,
  validateResponseWithSchema,
} from '@/lib/api/fetch-base';
import { buildQueryString, type SearchParams } from '@/lib/utils';

export class ServerApiError extends Error {
  public readonly status: number;
  public readonly data: unknown;

  constructor(message: string, status: number, data?: unknown) {
    super(message);
    this.name = 'ServerApiError';
    this.status = status;
    this.data = data;
  }
}

export type ServerRequestInit = BaseRequestInit;

const devLog = createDevLogger('serverFetch');

async function createAbsoluteUrl(
  endpoint: string,
  params?: SearchParams
): Promise<string> {
  try {
    if (isAbsoluteUrl(endpoint)) {
      return buildQueryString(endpoint, params);
    }
    const hdrs = await headers();
    const host = hdrs.get('x-forwarded-host') ?? hdrs.get('host');
    const proto = hdrs.get('x-forwarded-proto') ?? 'http';

    devLog('url-creation', {
      endpoint,
      host,
      proto,
      'x-forwarded-host': hdrs.get('x-forwarded-host'),
      'x-forwarded-proto': hdrs.get('x-forwarded-proto'),
      'original-host': hdrs.get('host'),
    });

    if (!host) {
      console.error('[SERVER_FETCH] Host 헤더 누락:', {
        endpoint,
        allHeaders: Object.fromEntries(hdrs.entries()),
      });
      throw new Error('Host header is missing');
    }

    // host 끝의 슬래시와 endpoint 시작의 슬래시 중복 제거
    const cleanHost = host.endsWith('/') ? host.slice(0, -1) : host;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const finalUrl = buildQueryString(
      `${proto}://${cleanHost}${cleanEndpoint}`,
      params
    );
    devLog('final-url', { endpoint, finalUrl });
    return finalUrl;
  } catch (error) {
    console.error('[SERVER_FETCH] URL 생성 오류:', error);
    throw error;
  }
}

async function attachCookieHeader(
  endpoint: string,
  init?: RequestInit
): Promise<HeadersInit> {
  try {
    // 외부 절대 URL로는 쿠키를 포워딩하지 않음 (보안)
    if (!isRelativeUrl(endpoint)) {
      devLog('skip-cookie', { endpoint, reason: 'external-url' });
      return {
        ...(init?.headers || {}),
      } as HeadersInit;
    }
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    const cookieHeader = allCookies.map(c => `${c.name}=${c.value}`).join('; ');

    devLog('cookie-header', {
      endpoint,
      cookieCount: allCookies.length,
      hasSupabaseCookies: allCookies.some(c => c.name.includes('supabase')),
      cookieNames: allCookies.map(c => c.name),
    });

    return {
      ...(init?.headers || {}),
      Cookie: cookieHeader,
    } as HeadersInit;
  } catch (error) {
    console.error('[SERVER_FETCH] 쿠키 헤더 생성 오류:', error);
    return {
      ...(init?.headers || {}),
    } as HeadersInit;
  }
}

// parseJsonSafe, extractErrorMessage from base

export async function serverApiFetch<T>(
  endpoint: string,
  init: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  const url = await createAbsoluteUrl(endpoint, init.searchParams);
  devLog('request', {
    method: init.method ?? 'GET',
    endpoint,
    url,
    cache: init.cache ?? 'no-store',
  });
  const startedAt = Date.now();

  const res = await fetch(url, {
    ...init,
    headers: await attachCookieHeader(endpoint, init),
    cache: init.cache ?? 'no-store',
  });

  const content: unknown = await parseJsonSafe<unknown>(res);

  if (!res.ok) {
    const durationMs = Date.now() - startedAt;
    console.error('[SERVER_FETCH] API 요청 실패:', {
      method: init.method ?? 'GET',
      url,
      status: res.status,
      statusText: res.statusText,
      durationMs,
      responseHeaders: Object.fromEntries(res.headers.entries()),
      bodyPreview:
        typeof content === 'string' ? content.slice(0, 200) : content,
    });
    devLog('error', {
      method: init.method ?? 'GET',
      url,
      status: res.status,
      durationMs,
      bodyPreview:
        typeof content === 'string' ? content.slice(0, 200) : content,
    });
    throw new ServerApiError(
      extractErrorMessage(content, 'API request failed'),
      res.status,
      content
    );
  }

  if (!content) return undefined as unknown as T;
  if (schema) {
    const validated = validateResponseWithSchema(schema, content);
    if (!validated.ok) {
      throw new ServerApiError(
        'Invalid response schema',
        500,
        validated.issues
      );
    }
    const durationMs = Date.now() - startedAt;
    devLog('response', {
      method: init.method ?? 'GET',
      url,
      status: res.status,
      durationMs,
    });
    return validated.data as T;
  }

  const durationMs = Date.now() - startedAt;
  devLog('response', {
    method: init.method ?? 'GET',
    url,
    status: res.status,
    durationMs,
  });
  return content as T;
}

export async function serverGet<T>(
  endpoint: string,
  options: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  return serverApiFetch<T>(endpoint, { ...options, method: 'GET' }, schema);
}

export async function serverPost<T>(
  endpoint: string,
  body?: unknown,
  options: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  const isFormData =
    typeof FormData !== 'undefined' && body instanceof FormData;
  return serverApiFetch<T>(
    endpoint,
    {
      ...options,
      method: 'POST',
      body: isFormData
        ? (body as BodyInit)
        : body
          ? JSON.stringify(body)
          : undefined,
      headers: {
        'Content-Type': isFormData ? undefined : 'application/json',
        ...(options.headers || {}),
      } as HeadersInit,
    },
    schema
  );
}

export async function serverPut<T>(
  endpoint: string,
  body?: unknown,
  options: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  const isFormData =
    typeof FormData !== 'undefined' && body instanceof FormData;
  return serverApiFetch<T>(
    endpoint,
    {
      ...options,
      method: 'PUT',
      body: isFormData
        ? (body as BodyInit)
        : body
          ? JSON.stringify(body)
          : undefined,
      headers: {
        'Content-Type': isFormData ? undefined : 'application/json',
        ...(options.headers || {}),
      } as HeadersInit,
    },
    schema
  );
}

export async function serverPatch<T>(
  endpoint: string,
  body?: unknown,
  options: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  const isFormData =
    typeof FormData !== 'undefined' && body instanceof FormData;
  return serverApiFetch<T>(
    endpoint,
    {
      ...options,
      method: 'PATCH',
      body: isFormData
        ? (body as BodyInit)
        : body
          ? JSON.stringify(body)
          : undefined,
      headers: {
        'Content-Type': isFormData ? undefined : 'application/json',
        ...(options.headers || {}),
      } as HeadersInit,
    },
    schema
  );
}

export async function serverDelete<T>(
  endpoint: string,
  options: ServerRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  return serverApiFetch<T>(endpoint, { ...options, method: 'DELETE' }, schema);
}
