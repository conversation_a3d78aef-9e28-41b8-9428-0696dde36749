import { z } from 'zod';
import { SearchParams } from '@/lib/utils';

export interface BaseRequestInit extends RequestInit {
  searchParams?: SearchParams;
}

export function isAbsoluteUrl(url: string): boolean {
  return /^https?:\/\//i.test(url);
}

export function isRelativeUrl(url: string): boolean {
  return !isAbsoluteUrl(url);
}

export async function parseJsonSafe<T>(res: Response): Promise<T | undefined> {
  const text = await res.text().catch(() => '');
  return text ? (JSON.parse(text) as T) : undefined;
}

export function extractErrorMessage(
  content: unknown,
  fallbackMessage = 'API 요청에 실패했습니다.'
): string {
  if (typeof content === 'object' && content !== null) {
    const anyContent = content as Record<string, unknown>;
    if (typeof anyContent['message'] === 'string') {
      return anyContent['message'] as string;
    }
    if (
      'error' in anyContent &&
      anyContent.error &&
      typeof (anyContent.error as { message?: unknown }).message === 'string'
    ) {
      return (anyContent.error as { message?: string }).message as string;
    }
  }
  return fallbackMessage;
}

export function createDevLogger(tag: string) {
  const isDev = process.env.NODE_ENV === 'development';
  return (label: string, payload?: unknown) => {
    if (!isDev) return;
    console.debug(`[${tag}] ${label}`, payload ?? '');
  };
}

// Optional helper to validate response with zod in a consistent way across environments
export function validateResponseWithSchema<T>(
  schema: z.ZodSchema<T> | undefined,
  content: unknown
): { ok: true; data: T } | { ok: false; issues: unknown } {
  if (!schema) return { ok: true, data: content as T };
  const parsed = schema.safeParse(content);
  if (!parsed.success) return { ok: false, issues: parsed.error.issues };
  return { ok: true, data: parsed.data };
}
