import { z } from 'zod';
import { configManager } from '@/lib/config/config-manager';
import {
  createDevLogger,
  extractErrorMessage,
  parseJsonSafe,
  type BaseRequestInit,
  validateResponseWithSchema,
} from '@/lib/api/fetch-base';
import { buildQueryString, type SearchParams } from '@/lib/utils';

export type BrowserRequestInit = BaseRequestInit;

function createAbsoluteUrl(endpoint: string, params?: SearchParams): string {
  const baseUrl = configManager.getValue('app.baseUrl');
  if (/^https?:\/\//i.test(endpoint)) return buildQueryString(endpoint, params);
  return buildQueryString(`${baseUrl}${endpoint}`, params);
}

function buildHeaders(init?: RequestInit): HeadersInit {
  const defaultHeaders: Record<string, string> = {};
  const isFormData = init?.body instanceof FormData;
  if (!isFormData) defaultHeaders['Content-Type'] = 'application/json';
  const token =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;
  if (token) defaultHeaders['Authorization'] = `Bearer ${token}`;
  return { ...defaultHeaders, ...(init?.headers || {}) } as HeadersInit;
}

export async function browserFetch<T>(
  endpoint: string,
  init: BrowserRequestInit = {},
  schema?: z.ZodSchema<T>
): Promise<T> {
  const url = createAbsoluteUrl(endpoint, init.searchParams);
  const log = createDevLogger('browserFetch');

  log('request', {
    method: init.method ?? 'GET',
    endpoint,
    url,
  });
  const startedAt = Date.now();

  const res = await fetch(url, { ...init, headers: buildHeaders(init) });
  const content: unknown = await parseJsonSafe(res);

  if (!res.ok) {
    const durationMs = Date.now() - startedAt;
    log('error', {
      method: init.method ?? 'GET',
      url,
      status: res.status,
      durationMs,
    });
    const message = extractErrorMessage(content, 'API 요청에 실패했습니다.');

    const error = new Error(message) as Error & {
      status?: number;
      body?: unknown;
    };

    error.status = res.status;
    error.body = content;
    throw error;
  }

  const durationMs = Date.now() - startedAt;
  log('response', {
    method: init.method ?? 'GET',
    url,
    status: res.status,
    durationMs,
  });
  if (!content) return undefined as unknown as T;
  if (schema) {
    const validated = validateResponseWithSchema(schema, content);
    if (!validated.ok) {
      const error = new Error('Invalid response schema') as Error & {
        status?: number;
        body?: unknown;
      };
      error.status = 500;
      error.body = validated.issues;
      throw error;
    }
    return validated.data as T;
  }
  return content as T;
}

export const browserGet = <T = unknown>(
  endpoint: string,
  init: BrowserRequestInit = {},
  schema?: z.ZodSchema<T>
) => browserFetch<T>(endpoint, { ...init, method: 'GET' }, schema);

export const browserPost = <T = unknown>(
  endpoint: string,
  body?: unknown,
  init: BrowserRequestInit = {},
  schema?: z.ZodSchema<T>
) => {
  const isFormData =
    typeof FormData !== 'undefined' && body instanceof FormData;
  return browserFetch<T>(
    endpoint,
    {
      ...init,
      method: 'POST',
      body: isFormData
        ? (body as BodyInit)
        : body
          ? JSON.stringify(body)
          : undefined,
      headers: {
        'Content-Type': isFormData ? undefined : 'application/json',
        ...(init.headers || {}),
      } as HeadersInit,
    },
    schema
  );
};
