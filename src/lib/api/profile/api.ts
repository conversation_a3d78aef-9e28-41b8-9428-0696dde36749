import { httpClient, type HttpClient } from '@/lib/http-client';
import {
  UserProfileResponseSchema,
  BookingHistoryResponseSchema,
  BookingActionResponseSchema,
  type UserStats,
  type BookingHistoryResponse,
} from '@/schemas/profile';

/**
 * 서버 컴포넌트 전용: 쿠키 인증을 유지하기 위해 상대 경로 fetch 사용
 */
export async function getUserProfile(): Promise<UserStats> {
  const response = await fetch('/api/profile', { cache: 'no-store' });
  const json = await response.json().catch(() => ({}));

  if (!response.ok) {
    throw new Error(json?.error?.message || 'Failed to fetch user profile');
  }

  const parsed = UserProfileResponseSchema.safeParse(json);
  if (!parsed.success) {
    throw new Error('Invalid user profile response');
  }

  return parsed.data;
}

/**
 * 서버 컴포넌트 전용: 쿠키 인증을 유지하기 위해 상대 경로 fetch 사용
 */
export async function getUserBookings(): Promise<BookingHistoryResponse> {
  const response = await fetch('/api/profile/bookings', { cache: 'no-store' });
  const json = await response.json().catch(() => ({}));

  if (!response.ok) {
    throw new Error(json?.error?.message || 'Failed to fetch booking history');
  }

  const parsed = BookingHistoryResponseSchema.safeParse(json);
  if (!parsed.success) {
    throw new Error('Invalid booking history response');
  }

  return parsed.data;
}

/**
 * *.api.ts 패턴을 따르는 클라이언트 전용 API 클래스
 */
export class ProfileApi {
  private http: HttpClient;

  constructor(http: HttpClient) {
    this.http = http;
  }

  /** 클라이언트에서 프로필 조회 (권장: 서버 컴포넌트는 getUserProfile 사용) */
  getUserProfile = async (): Promise<UserStats> => {
    const json = await this.http.get('/api/profile');
    const parsed = UserProfileResponseSchema.safeParse(json);
    if (!parsed.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(parsed.error.issues)}`
      );
    }
    return parsed.data;
  };

  /** 클라이언트에서 수강신청 내역 조회 (권장: 서버 컴포넌트는 getUserBookings 사용) */
  getUserBookings = async (): Promise<BookingHistoryResponse> => {
    const json = await this.http.get('/api/profile/bookings');
    const parsed = BookingHistoryResponseSchema.safeParse(json);
    if (!parsed.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(parsed.error.issues)}`
      );
    }
    return parsed.data;
  };

  payDeposit = async (bookingId: string): Promise<string> => {
    const json = await this.http.post('/api/profile/bookings', {
      action: 'pay_deposit',
      bookingId,
    });
    const parsed = BookingActionResponseSchema.safeParse(json);
    if (!parsed.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(parsed.error.issues)}`
      );
    }
    return parsed.data.message;
  };

  cancelBooking = async (bookingId: string): Promise<string> => {
    const json = await this.http.post('/api/profile/bookings', {
      action: 'cancel_booking',
      bookingId,
    });
    const parsed = BookingActionResponseSchema.safeParse(json);
    if (!parsed.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(parsed.error.issues)}`
      );
    }
    return parsed.data.message;
  };
}

/**
 * 기본 클라이언트 인스턴스
 */
export const profileApiClient = new ProfileApi(httpClient);

/**
 * 서버 컴포넌트 사용을 위한 호환 객체 (page.tsx 유지 목적)
 */
export const profileApi = {
  getUserProfile,
  getUserBookings,
  payDeposit: (bookingId: string) => profileApiClient.payDeposit(bookingId),
  cancelBooking: (bookingId: string) =>
    profileApiClient.cancelBooking(bookingId),
};

// 기존 사용처 호환을 위한 동명 함수 export
export const payDeposit = (bookingId: string) =>
  profileApiClient.payDeposit(bookingId);
export const cancelBooking = (bookingId: string) =>
  profileApiClient.cancelBooking(bookingId);
