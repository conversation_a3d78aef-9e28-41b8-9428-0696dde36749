import { serverGet } from '@/lib/api/server-fetch.server';
import {
  BookingHistoryResponseSchema,
  type BookingHistoryResponse,
} from '@/schemas/profile';

/**
 * 서버 컴포넌트/서버 환경 전용: 회원 수강신청 내역 조회
 * - 상대 경로 + 쿠키 포워딩을 위해 serverGet 사용
 * - 응답은 zod 스키마로 검증
 */
export async function getUserBookings(): Promise<BookingHistoryResponse> {
  return serverGet<BookingHistoryResponse>(
    '/api/profile/bookings',
    { cache: 'no-store' },
    BookingHistoryResponseSchema
  );
}
