import {
  type PaymentSuccessRequest,
  type PaymentSuccessFullResponse,
  type PaymentSuccessErrorResponse,
  type PaymentHistoryRequest,
  type PaymentHistoryResponse,
} from '@/schemas/payment';
import {
  confirmPaymentRequestSchema,
  confirmPaymentSuccessResponseSchema,
  type ConfirmPaymentSuccessResponse,
  type ConfirmPaymentRequest,
} from '@/lib/validations/payment.validation';
import {
  serverGet,
  serverPost,
  type ServerRequestInit,
} from '@/lib/api/server-fetch.server';

const API_BASE_URL = '/api/payment';

export class PaymentApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = 'PaymentApiError';
  }
}

export async function getPaymentSuccess(
  params: PaymentSuccessRequest
): Promise<PaymentSuccessFullResponse> {
  try {
    const data = await serverGet<
      PaymentSuccessFullResponse | PaymentSuccessErrorResponse
    >(`${API_BASE_URL}/success`, {
      searchParams: {
        enrollmentId: params.enrollmentId,
        transactionId: params.transactionId,
      },
    } as ServerRequestInit);

    if (!('success' in data) || !data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(errorData.error, errorData.code, 400);
    }

    return data as PaymentSuccessFullResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to fetch payment success data');
  }
}

export async function confirmPaymentSuccess(
  params: PaymentSuccessRequest
): Promise<PaymentSuccessFullResponse> {
  try {
    const data = await serverPost<
      PaymentSuccessFullResponse | PaymentSuccessErrorResponse
    >(`${API_BASE_URL}/success`, params);

    if (!('success' in data) || !data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(errorData.error, errorData.code, 400);
    }

    return data as PaymentSuccessFullResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to confirm payment success');
  }
}

export async function confirmTossPaymentOnServer(
  params: ConfirmPaymentRequest
): Promise<ConfirmPaymentSuccessResponse> {
  const parsed = confirmPaymentRequestSchema.safeParse(params);
  if (!parsed.success) {
    throw new PaymentApiError(
      'Invalid confirm payment request',
      'VALIDATION_ERROR',
      400
    );
  }

  const data = await serverPost<ConfirmPaymentSuccessResponse>(
    `${API_BASE_URL}/toss/confirm`,
    parsed.data,
    {},
    confirmPaymentSuccessResponseSchema
  );

  return data;
}

export async function getPaymentHistory(
  params: PaymentHistoryRequest
): Promise<PaymentHistoryResponse> {
  try {
    const data = await serverGet<
      PaymentHistoryResponse | PaymentSuccessErrorResponse
    >(`${API_BASE_URL}/history`, {
      searchParams: {
        status: params.status,
        paymentType: params.paymentType,
        startDate: params.startDate,
        endDate: params.endDate,
        limit: params.limit,
        offset: params.offset,
      },
    } as ServerRequestInit);

    if (!('success' in data)) {
      throw new PaymentApiError('Invalid history response');
    }
    if (!data.success) {
      const errorData = data as PaymentSuccessErrorResponse;
      throw new PaymentApiError(errorData.error, errorData.code, 400);
    }

    return data as PaymentHistoryResponse;
  } catch (error) {
    if (error instanceof PaymentApiError) {
      throw error;
    }
    throw new PaymentApiError('Failed to fetch payment history');
  }
}

export const paymentApi = {
  getPaymentSuccess,
  confirmPaymentSuccess,
  getPaymentHistory,
  confirmTossPaymentOnServer,
};
