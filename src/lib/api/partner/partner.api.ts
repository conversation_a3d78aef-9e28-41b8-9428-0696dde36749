import { httpClient, HttpClient } from '@/lib/http-client';
import {
  partnerApiSchema,
  type GetScheduleGroupsRequest,
  type GetScheduleGroupsResponse,
} from './partner.schema';
import {
  enrollmentErrorResponseSchema,
  enrollmentListSuccessResponseSchema,
  type EnrollmentListSuccessResponse,
  type EnrollmentErrorResponse,
  type EnrollmentQuery,
} from '@/lib/validations/enrollments.validation';
import { z } from 'zod';

export class PartnerApi {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * 파트너의 스케줄 그룹 목록을 조회합니다.
   */
  getScheduleGroups = async (
    req: GetScheduleGroupsRequest
  ): Promise<GetScheduleGroupsResponse> => {
    const endpoint = '/api/partner/schedule-groups';

    try {
      const searchParams = new URLSearchParams();

      // 필터 파라미터 추가
      if (req.status) {
        if (Array.isArray(req.status)) {
          req.status.forEach(status => searchParams.append('status', status));
        } else {
          searchParams.append('status', req.status);
        }
      }

      if (req.instructorId) {
        if (Array.isArray(req.instructorId)) {
          req.instructorId.forEach(id =>
            searchParams.append('instructorId', id)
          );
        } else {
          searchParams.append('instructorId', req.instructorId);
        }
      }

      if (req.category) {
        if (Array.isArray(req.category)) {
          req.category.forEach(cat => searchParams.append('category', cat));
        } else {
          searchParams.append('category', req.category);
        }
      }

      // 페이지네이션 파라미터
      searchParams.append('page', req.page.toString());
      searchParams.append('limit', req.limit.toString());

      const url = searchParams.toString()
        ? `${endpoint}?${searchParams.toString()}`
        : endpoint;

      const json = await this.httpClient.get(url);

      const result =
        partnerApiSchema['/api/partner/schedule-groups'].response.safeParse(
          json
        );

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching schedule groups:', error);
      throw error;
    }
  };

  /**
   * 특정 스케줄 그룹의 수강 신청 목록을 조회합니다.
   */
  getScheduleGroupEnrollments = async (
    scheduleGroupId: number,
    query: Partial<EnrollmentQuery> = {}
  ): Promise<EnrollmentListSuccessResponse | EnrollmentErrorResponse> => {
    const endpoint = `/api/partner/schedule-groups/${scheduleGroupId}/enrollments`;
    try {
      const searchParams = new URLSearchParams();
      const sortBy = query.sortBy ?? 'enrollmentOrder';
      const sortOrder = query.sortOrder ?? 'asc';
      searchParams.append('sortBy', sortBy);
      searchParams.append('sortOrder', sortOrder);

      const url = `${endpoint}?${searchParams.toString()}`;
      const json = await this.httpClient.get(url);

      const unionSchema = z.union([
        enrollmentListSuccessResponseSchema,
        enrollmentErrorResponseSchema,
      ]);
      const result = unionSchema.safeParse(json);
      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }
      return result.data;
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      throw error;
    }
  };
}

// 기본 인스턴스 생성 및 내보내기
export const partnerApi = new PartnerApi(httpClient);
