import {
  serverGet,
  serverPost,
  serverPut,
} from '@/lib/api/server-fetch.server';
import type {
  CreateStudioRequest,
  CreateStudioResponse,
  GetStudiosResponse,
  StudioResponse,
  UpdateStudioRequest,
  UpdateStudioResponse,
} from '@/lib/api/partner/studio.schema';
import { studioApiSchema } from '@/lib/api/partner/studio.schema';

export async function getMyStudiosServer(): Promise<GetStudiosResponse> {
  return serverGet<GetStudiosResponse>(
    '/api/partner/studios',
    {
      next: { tags: ['/api/partner/studios'] },
    },
    studioApiSchema['GET /api/partner/studios'].response
  );
}

export async function getActiveStudiosServer(): Promise<GetStudiosResponse> {
  const studios = await getMyStudiosServer();
  // return studios.filter(studio => studio.status === 'active');
  return studios;
}

export async function createStudioServer(
  req: Omit<CreateStudioRequest, 'partnerId'>
): Promise<CreateStudioResponse> {
  return serverPost<CreateStudioResponse>(
    '/api/partner/studios',
    req,
    {},
    studioApiSchema['POST /api/partner/studios'].response
  );
}

export async function getStudioByIdServer(
  studioId: string
): Promise<StudioResponse> {
  return serverGet<StudioResponse>(
    `/api/partner/studios/${studioId}`,
    {},
    studioApiSchema['GET /api/partner/studios/:studioId'].response
  );
}

export async function updateStudioByIdServer(
  studioId: string,
  req: UpdateStudioRequest
): Promise<UpdateStudioResponse> {
  return serverPut<UpdateStudioResponse>(
    `/api/partner/studios/${studioId}`,
    req,
    {},
    studioApiSchema['PUT /api/partner/studios/:studioId'].response
  );
}
