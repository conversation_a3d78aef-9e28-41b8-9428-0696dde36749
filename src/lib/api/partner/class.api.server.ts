import {
  serverDelete,
  serverGet,
  serverPost,
  serverPut,
} from '@/lib/api/server-fetch.server';
import {
  classApiSchema,
  type CreateClassRequest,
  type CreateClassResponse,
  type DeleteClassResponse,
  type GetClassesRequest,
  type GetClassesResponse,
  type GetClassResponse,
  type UpdateClassRequest,
  type UpdateClassResponse,
  type ConfirmScheduleRequest,
} from '@/lib/api/partner/class.schema';

export async function getClassesServer(
  req: GetClassesRequest
): Promise<GetClassesResponse> {
  const searchParams: Record<string, string> = {
    page: String(req.page),
    limit: String(req.limit),
  };
  if (req.studioId) searchParams.studioId = req.studioId;

  return serverGet<GetClassesResponse>(
    '/api/partner/classes',
    {
      searchParams,
      next: {
        tags: ['/api/partner/classes'],
      },
    },
    classApiSchema['GET /api/partner/classes'].response
  );
}

export async function createClassServer(
  payload: CreateClassRequest
): Promise<CreateClassResponse> {
  return serverPost<CreateClassResponse>(
    '/api/partner/classes',
    payload,
    {
      next: {
        tags: ['/api/partner/classes'],
      },
    },
    classApiSchema['POST /api/partner/classes'].response
  );
}

export async function getClassByIdServer(
  classId: string
): Promise<GetClassResponse> {
  return serverGet<GetClassResponse>(
    `/api/partner/classes/${classId}`,
    {
      next: {
        tags: ['/api/partner/classes/' + classId],
      },
    },
    classApiSchema['GET /api/partner/classes/{id}'].response
  );
}

export async function updateClassServer(
  classId: string,
  payload: UpdateClassRequest
): Promise<UpdateClassResponse> {
  return serverPut<UpdateClassResponse>(
    `/api/partner/classes/${classId}`,
    payload,
    {},
    classApiSchema['PUT /api/partner/classes/{id}'].response
  );
}

export async function deleteClassServer(
  classId: string
): Promise<DeleteClassResponse> {
  return serverDelete<DeleteClassResponse>(
    `/api/partner/classes/${classId}`,
    {},
    classApiSchema['DELETE /api/partner/classes/{id}'].response
  );
}

export async function confirmScheduleServer(
  classId: string,
  scheduleGroupId: number,
  payload: ConfirmScheduleRequest
): Promise<void> {
  // 서버 스키마는 union(success|error)로 정의되어 있으므로,
  // 여기서는 단순 POST 호출만 수행하고 에러는 예외로 처리합니다.
  await serverPost<unknown>(
    `/api/partner/classes/${classId}/schedules/${scheduleGroupId}/confirm`,
    payload
  );
}
