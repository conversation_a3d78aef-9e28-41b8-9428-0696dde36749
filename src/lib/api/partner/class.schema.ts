import {
  createClassSchema,
  updateClassSchema,
  patchClassSchema,
  classListFilterSchema,
} from '@/lib/validations/partner-class.validation';
import {
  confirmClassRequestSchema,
  confirmClassSuccessResponseSchema,
  classConfirmationErrorResponseSchema,
} from '@/lib/validations/class-confirmation.validation';
import z from 'zod';

const ScheduleSchema = z.object({
  id: z.number(),
  dayOfWeek: z.string(),
  startTime: z.string(),
  endTime: z.string(),
});

// ScheduleGroup 스키마 (공통)
const ScheduleGroupSchema = z.object({
  id: z.number(),
  status: z.string(),
  schedules: z.array(ScheduleSchema),
});

// 기본 클래스 정보 스키마 (ClassWithScheduleGroups 인터페이스 기반)
const BaseClassResponseSchema = z.object({
  id: z.string(),
  partnerId: z.string(),
  studioId: z.string(),
  instructorId: z.string(),
  title: z.string(),
  description: z.string(),
  category: z.string(),
  level: z.string(),
  target: z.string(),
  maxParticipants: z.number(),
  pricePerSession: z.number(),
  sessionDurationMinutes: z.number(),
  durationWeeks: z.number(),
  sessionsPerWeek: z.number(),
  images: z.any(), // 실제 타입이 any이므로 any로 유지
  status: z.string(),
  visible: z.boolean().nullable(),
  createdAt: z.string().nullable(),
  updatedAt: z.string().nullable(),
  scheduleGroups: z.array(ScheduleGroupSchema),
});

// POST, PUT 응답 스키마 (createClass, updateClass 반환 타입)
export const ClassResponseSchema = BaseClassResponseSchema;

// GET /api/partner/classes 응답의 data 항목 스키마
export const ClassListItemResponseSchema = BaseClassResponseSchema.extend({
  studioName: z.string().nullable(),
  instructorName: z.string().nullable(),
  enrollmentCount: z.number(),
});

export type ClassListItemResponse = z.infer<typeof ClassListItemResponseSchema>;

// 페이지네이션 응답 스키마
export const PaginationResponseSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
});

// GET /api/partner/classes 전체 응답 스키마
export const ClassListResponseSchema = z.object({
  data: z.array(ClassListItemResponseSchema),
  pagination: PaginationResponseSchema,
});

// GET /api/partner/classes/{id} 응답 스키마 (getClassDetail 반환 타입)
export const ClassDetailResponseSchema = BaseClassResponseSchema.extend({
  enrollmentCount: z.number(),
  studio: z.object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    profileImages: z.any(), // 실제 타입이 any이므로 any로 유지
  }),
});

// DELETE /api/partner/classes/{id} 응답 스키마
export const DeleteClassResponseSchema = z.object({
  message: z.string(),
});

export const createClassRequestSchema = createClassSchema
  .omit({
    images: true,
    instructorId: true,
  })
  .extend({
    instructorId: z.uuid({ message: '강사를 선택해주세요' }),
  });

export const patchClassRequestSchema = patchClassSchema;

export const classApiSchema = {
  'GET /api/partner/classes': {
    request: classListFilterSchema,
    response: ClassListResponseSchema,
  },
  'POST /api/partner/classes': {
    request: createClassRequestSchema,
    response: ClassResponseSchema,
  },
  'GET /api/partner/classes/{id}': {
    request: z.object({ id: z.string() }),
    response: ClassDetailResponseSchema,
  },
  'PUT /api/partner/classes/{id}': {
    request: updateClassSchema,
    response: ClassResponseSchema,
  },
  'PATCH /api/partner/classes/{id}': {
    request: patchClassRequestSchema,
    response: z.void(),
  },
  'DELETE /api/partner/classes/{id}': {
    request: z.object({ id: z.string() }),
    response: DeleteClassResponseSchema,
  },
  'POST /api/partner/classes/{id}/schedules/{scheduleGroupId}/confirm': {
    request: confirmClassRequestSchema,
    response: z.union([
      confirmClassSuccessResponseSchema,
      classConfirmationErrorResponseSchema,
    ]),
  },
};

export type ClassApiSchema = typeof classApiSchema;
export type GetClassesRequest = z.infer<typeof classListFilterSchema>;
export type GetClassesResponse = z.infer<typeof ClassListResponseSchema>;

export type CreateClassRequest = z.infer<typeof createClassRequestSchema>;
export type ScheduleGroup = CreateClassRequest['scheduleGroups'][number];
export type Schedule = ScheduleGroup['schedules'][number];

export type CreateClassResponse = z.infer<typeof ClassResponseSchema>;

export type GetClassResponse = z.infer<typeof ClassDetailResponseSchema>;

export type UpdateClassRequest = z.infer<typeof updateClassSchema>;
export type UpdateClassResponse = z.infer<typeof ClassResponseSchema>;

export type PatchClassRequest = z.infer<typeof patchClassRequestSchema>;

export type DeleteClassResponse = z.infer<typeof DeleteClassResponseSchema>;

// 스케줄 확정 관련 타입
export type ConfirmScheduleRequest = z.infer<typeof confirmClassRequestSchema>;
export type ConfirmScheduleResponse = z.infer<
  typeof confirmClassSuccessResponseSchema
>;
