'use server';

import { getPartnerFromCookies } from '@/lib/auth/partner.server';
import { createSupabaseClient } from '@/lib/supabase/server';
import { PartnerLoginFormSchema } from '@/schemas/partner';
import type { PartnerStatusType } from '@/types/partner';

/**
 * 파트너 상태별 리다이렉트 URL 결정
 */
function getRedirectUrlByStatus(status: PartnerStatusType): string {
  switch (status) {
    case 'ACTIVE':
      return '/partner/dashboard';
    case 'PENDING':
      return '/partner/register/complete';
    case 'SUSPENDED':
      return '/partner/suspended';
    case 'REJECTED':
      return '/partner/suspended'; // 거부된 경우도 동일한 페이지로
    default:
      return '/partner/register/complete';
  }
}

/**
 * 파트너 로그인 액션 상태 타입
 */
export interface LoginActionState {
  success?: boolean;
  message?: string;
  redirectTo?: string;
  partnerStatus?: Extract<
    PartnerStatusType,
    'PENDING' | 'SUSPENDED' | 'REJECTED'
  >;
  errors?: {
    email?: string[];
    password?: string[];
    general?: string[];
  };
}

/**
 * 파트너 로그인 서버 액션
 */
export async function loginPartnerAction(
  prevState: LoginActionState,
  formData: FormData
): Promise<LoginActionState> {
  try {
    // 1. 폼 데이터 검증
    const validationResult = PartnerLoginFormSchema.safeParse({
      email: formData.get('email'),
      password: formData.get('password'),
      rememberMe: formData.get('rememberMe') === 'true',
    });

    if (!validationResult.success) {
      const errors: LoginActionState['errors'] = {};

      validationResult.error.issues.forEach(issue => {
        const field = issue.path[0];
        if (typeof field === 'string') {
          if (field === 'email') {
            if (!errors.email) errors.email = [];
            errors.email.push(issue.message);
          } else if (field === 'password') {
            if (!errors.password) errors.password = [];
            errors.password.push(issue.message);
          } else {
            if (!errors.general) errors.general = [];
            errors.general.push(issue.message);
          }
        }
      });

      return {
        success: false,
        message: '입력 정보를 확인해주세요.',
        errors,
      };
    }

    const { email, password, rememberMe } = validationResult.data;

    // 2. Supabase 클라이언트 생성
    const supabase = await createSupabaseClient();

    // 3. 인증 시도
    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      console.error('Supabase Auth 로그인 오류:', authError);

      // 이메일 인증 필요 에러 처리
      if (
        authError.code === 'email_not_confirmed' ||
        authError.message.includes('Email not confirmed')
      ) {
        // 이메일 미인증 사용자도 complete 페이지로 리다이렉트
        return {
          success: true,
          message: '이메일 인증을 완료해주세요.',
          redirectTo: '/partner/register/complete',
        };
      }

      // 인증 실패 에러 처리
      if (
        authError.message.includes('Invalid login credentials') ||
        authError.message.includes('Invalid email or password')
      ) {
        return {
          success: false,
          message: '이메일 또는 비밀번호가 올바르지 않습니다.',
          errors: {
            general: ['이메일 또는 비밀번호가 올바르지 않습니다.'],
          },
        };
      }

      // Rate limiting
      if (
        authError.message.includes('too many') ||
        authError.message.includes('rate limit')
      ) {
        return {
          success: false,
          message:
            '너무 많은 로그인 시도가 있었습니다. 잠시 후 다시 시도해주세요.',
          errors: {
            general: [
              '너무 많은 로그인 시도가 있었습니다. 잠시 후 다시 시도해주세요.',
            ],
          },
        };
      }

      return {
        success: false,
        message: '로그인 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        errors: {
          general: [
            '로그인 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
          ],
        },
      };
    }

    if (!authData.user) {
      return {
        success: false,
        message: '로그인에 실패했습니다.',
        errors: {
          general: ['로그인에 실패했습니다.'],
        },
      };
    }

    // 4. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('id, contact_name, contact_phone, status')
      .eq('user_id', authData.user.id)
      .single();

    if (partnerError) {
      // 파트너 정보가 없는 경우
      if (partnerError.code === 'PGRST116') {
        return {
          success: false,
          message: '파트너 계정을 찾을 수 없습니다.',
          errors: {
            general: ['파트너 계정을 찾을 수 없습니다.'],
          },
        };
      }

      return {
        success: false,
        message: '파트너 정보 조회 중 오류가 발생했습니다.',
        errors: {
          general: ['파트너 정보 조회 중 오류가 발생했습니다.'],
        },
      };
    }

    if (!partner) {
      return {
        success: false,
        message: '파트너 계정을 찾을 수 없습니다.',
        errors: {
          general: ['파트너 계정을 찾을 수 없습니다.'],
        },
      };
    }

    // 5. Remember Me 설정 처리
    if (rememberMe) {
      // 세션을 더 오래 유지 (30일)
      await supabase.auth.updateUser({
        data: { remember_me: true },
      });
    }

    // 6. 파트너 상태별 처리
    if (partner.status === 'ACTIVE') {
      // 활성화된 파트너는 대시보드로 리다이렉트
      const redirectUrl = getRedirectUrlByStatus(partner.status);
      return {
        success: true,
        message: '로그인되었습니다.',
        redirectTo: redirectUrl,
      };
    } else if (partner.status === 'PENDING') {
      // PENDING 상태는 완료 페이지로 리다이렉트
      return {
        success: true,
        message: '로그인되었습니다.',
        redirectTo: '/partner/register/complete',
      };
    } else {
      // SUSPENDED, REJECTED 상태는 상태 메시지 표시
      return {
        success: true,
        message: '로그인되었습니다.',
        partnerStatus: partner.status as Extract<
          PartnerStatusType,
          'SUSPENDED' | 'REJECTED'
        >,
      };
    }
  } catch (error) {
    console.error('파트너 로그인 처리 오류:', error);

    return {
      success: false,
      message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
      errors: {
        general: ['서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'],
      },
    };
  }
}

/**
 * 현재 파트너 상태 확인 액션
 */
export async function checkPartnerStatusAction(): Promise<{
  isAuthenticated: boolean;
  partner?: {
    id: string;
    contact_name: string;
    status: PartnerStatusType;
  };
}> {
  try {
    const partnerAuth = await getPartnerFromCookies();

    if (!partnerAuth.success || !partnerAuth.partner) {
      return { isAuthenticated: false };
    }

    return {
      isAuthenticated: true,
      partner: {
        id: partnerAuth.partner.id,
        contact_name: partnerAuth.partner.contactName,
        status: partnerAuth.partner.status,
      },
    };
  } catch (error) {
    console.error('파트너 상태 확인 오류:', error);
    return { isAuthenticated: false };
  }
}
