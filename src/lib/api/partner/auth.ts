import { createSupabaseClient } from '@/lib/supabase/client';
import {
  PartnerLoginRequest,
  PartnerLoginResponse,
  PartnerStatusType,
} from '@/types/partner';

/**
 * 파트너 인증 관련 유틸리티 함수들
 */

/**
 * 파트너 로그인 처리
 *
 * @param loginData - 로그인 요청 데이터 (이메일, 비밀번호, rememberMe)
 * @returns 로그인 결과
 */
export async function loginPartner(
  loginData: PartnerLoginRequest
): Promise<PartnerLoginResponse> {
  try {
    const supabase = createSupabaseClient();
    const { email, password, rememberMe } = loginData;

    // 1. Supabase Auth 로그인
    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError) {
      console.error('Supabase Auth 로그인 오류:', authError);

      // 이메일 인증 필요 에러 처리
      if (
        authError.code === 'email_not_confirmed' ||
        authError.message.includes('Email not confirmed')
      ) {
        return {
          success: false,
          message:
            '이메일 인증을 완료해주세요. 이메일함을 확인하여 인증 링크를 클릭해주세요.',
          error: authError.message,
        };
      }

      // 인증 실패 에러 처리
      if (
        authError.message.includes('Invalid login credentials') ||
        authError.message.includes('Invalid email or password')
      ) {
        return {
          success: false,
          message: '이메일 또는 비밀번호가 올바르지 않습니다.',
          error: authError.message,
        };
      }

      // Rate limiting
      if (
        authError.message.includes('too many') ||
        authError.message.includes('rate limit')
      ) {
        return {
          success: false,
          message:
            '너무 많은 로그인 시도가 있었습니다. 잠시 후 다시 시도해주세요.',
          error: authError.message,
        };
      }

      return {
        success: false,
        message: '로그인 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        error: authError.message,
      };
    }

    if (!authData.user) {
      return {
        success: false,
        message: '로그인에 실패했습니다.',
      };
    }

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('id, contact_name, contact_phone, status')
      .eq('user_id', authData.user.id)
      .single();

    if (partnerError) {
      console.error('파트너 정보 조회 오류:', partnerError);

      // 파트너 정보가 없는 경우
      if (partnerError.code === 'PGRST116') {
        return {
          success: false,
          message: '파트너 계정을 찾을 수 없습니다.',
        };
      }

      return {
        success: false,
        message: '파트너 정보 조회 중 오류가 발생했습니다.',
      };
    }

    if (!partner) {
      return {
        success: false,
        message: '파트너 계정을 찾을 수 없습니다.',
      };
    }

    // 3. Remember Me 설정 처리
    if (rememberMe) {
      // 세션을 더 오래 유지 (30일)
      await supabase.auth.updateUser({
        data: { remember_me: true },
      });
    }

    // 4. 파트너 상태별 처리
    const redirectUrl = getRedirectUrlByStatus(partner.status);

    // 5. 성공 응답
    return {
      success: true,
      message: '로그인되었습니다.',
      data: {
        user: {
          id: authData.user.id,
          email: authData.user.email!,
        },
        partner: {
          id: partner.id,
          contact_name: partner.contact_name,
          contact_phone: partner.contact_phone,
          status: partner.status,
        },
        redirectUrl,
      },
    };
  } catch (error) {
    console.error('파트너 로그인 처리 오류:', error);

    return {
      success: false,
      message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
    };
  }
}

/**
 * 파트너 상태별 리다이렉트 URL 결정
 *
 * @param status - 파트너 상태
 * @returns 리다이렉트할 URL
 */
function getRedirectUrlByStatus(status: PartnerStatusType): string {
  switch (status) {
    case 'ACTIVE':
      return '/partner/dashboard';
    case 'PENDING':
      return '/partner/register/complete';
    case 'SUSPENDED':
      return '/partner/suspended';
    case 'REJECTED':
      return '/partner/suspended'; // REJECTED도 suspended 페이지로 (별도 페이지 필요시 수정)
    default:
      return '/partner/login';
  }
}

/**
 * 파트너 로그아웃
 */
export async function logoutPartner(): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const supabase = createSupabaseClient();

    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('로그아웃 오류:', error);
      return {
        success: false,
        message: '로그아웃 중 오류가 발생했습니다.',
      };
    }

    return {
      success: true,
      message: '로그아웃되었습니다.',
    };
  } catch (error) {
    console.error('파트너 로그아웃 처리 오류:', error);

    return {
      success: false,
      message: '서버 오류가 발생했습니다.',
    };
  }
}

/**
 * 현재 파트너 정보 조회
 */
export async function getCurrentPartner() {
  try {
    const supabase = createSupabaseClient();

    // 1. 현재 사용자 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return null;
    }

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('id, contact_name, contact_phone, status, created_at, updated_at')
      .eq('user_id', user.id)
      .single();

    if (partnerError || !partner) {
      return null;
    }

    return {
      user: {
        id: user.id,
        email: user.email!,
      },
      partner,
    };
  } catch (error) {
    console.error('현재 파트너 정보 조회 오류:', error);
    return null;
  }
}
