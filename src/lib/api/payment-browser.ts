import { browserPost } from '@/lib/api/browser-fetch';
import {
  confirmPaymentRequestSchema,
  confirmPaymentSuccessResponseSchema,
  type ConfirmPaymentRequest,
  type ConfirmPaymentSuccessResponse,
} from '@/lib/validations/payment.validation';

const API_BASE_URL = '/api/payment';

export async function confirmTossPaymentOnBrowser(
  params: ConfirmPaymentRequest
): Promise<ConfirmPaymentSuccessResponse> {
  const parsed = confirmPaymentRequestSchema.parse(params);
  return await browserPost<ConfirmPaymentSuccessResponse>(
    `${API_BASE_URL}/toss/confirm`,
    parsed,
    {},
    confirmPaymentSuccessResponseSchema
  );
}

export const paymentBrowserApi = {
  confirmTossPaymentOnBrowser,
};
