### 서버 API 호출 유틸(serverApiFetch) 가이드

쿠키 기반(Supabase) 인증을 확실히 유지하며 서버 컴포넌트/서버 코드에서 내부 API Route(`/api/*`)를 호출하기 위한 공용 유틸입니다. 절대 URL을 자동 구성하고, 현재 요청의 쿠키를 API 호출에 붙여 인증을 통과시킵니다. 선택적으로 Zod 스키마로 응답을 검증할 수 있습니다.

#### 언제 사용하나
- 서버 컴포넌트(RSC)에서 내부 API를 호출할 때
- Route Handler, Server Action 등 서버 환경에서 내부 API를 재호출해야 할 때
- 브라우저 클라이언트가 아닌 서버에서 쿠키 인증을 보존해야 할 때

#### 제공 유틸
- `serverApiFetch<T>(endpoint, init?, schema?)`
- HTTP 헬퍼: `serverGet`, `serverPost`, `serverPut`, `serverPatch`, `serverDelete`
- 에러 타입: `ServerApiError(message, status, data)`

#### 브라우저 fetch vs 서버 fetch 차이와 인증 실패 원인
- 브라우저 환경
  - 같은 오리진의 요청은 브라우저가 자동으로 쿠키를 첨부합니다.
  - 상대경로(`/api/...`)도 문제없이 해석됩니다.
  - 그래서 클라이언트 컴포넌트에서의 인증은 자연스럽게 통과합니다.

- 서버(Node/Edge) 환경
  - 표준 `fetch`에는 “브라우저의 쿠키 저장소”가 없습니다. 쿠키를 직접 붙이지 않으면 전달되지 않습니다.
  - 실행 컨텍스트에 따라 상대경로를 절대 URL로 해석할 수 없어 오류가 날 수 있습니다(예: RSC 빌드/특정 런타임에서 `Failed to parse URL from /api/...`).
  - 과거 실패 원인: 절대 URL로 `fetch`했지만 쿠키를 붙이지 않아 Supabase 세션을 읽지 못해 401이 발생했으며, 어떤 경로에서는 상대경로 해석 실패로 URL 파싱 에러가 발생했습니다.

#### 사용 예시

```ts
// 서버 컴포넌트, Route Handler 등 서버 환경 예시
import { serverGet, serverPost, serverApiFetch } from '@/lib/api/server-fetch';
import { UserProfileResponseSchema, BookingHistoryResponseSchema, BookingActionResponseSchema } from '@/schemas/profile';

// 1) GET (스키마 검증 O)
const profile = await serverGet('/api/profile', {}, UserProfileResponseSchema);

// 2) GET + 쿼리스트링 (스키마 검증 O)
const bookings = await serverGet(
  '/api/profile/bookings',
  { searchParams: { status: 'completed' } },
  BookingHistoryResponseSchema
);

// 3) POST (스키마 검증 O)
const result = await serverPost(
  '/api/profile/bookings',
  { action: 'cancel_booking', bookingId: 'enroll_123' },
  {},
  BookingActionResponseSchema
);

// 4) 저수준 API 사용 (스키마 검증 X)
type Health = { ok: boolean };
const health = await serverApiFetch<Health>('/api/health');
```

#### 주요 동작 원리
- 절대 URL 자동 구성: `x-forwarded-host`/`host`, `x-forwarded-proto`/`http|https`를 사용해 `${proto}://${host}${endpoint}`를 생성합니다.
- 쿠키 전달: `next/headers`의 `cookies()`에서 현재 요청의 쿠키를 읽어 `Cookie` 헤더로 붙입니다.
- 캐시 정책: 개인화 데이터 특성상 기본값 `cache: 'no-store'`를 사용합니다.
- 스키마 검증(선택): `schema.safeParse(responseJson)`로 런타임 안전성 향상.
- 에러 처리: `!res.ok` 또는 스키마 불일치 시 `ServerApiError(status, data)`를 던집니다.

#### 베스트 프랙티스
- 인증/리다이렉트
  - 미들웨어에서 비로그인 사용자는 `/login?redirectTo=<원래경로>`로 보내고, 서버 컴포넌트에서도 가드(`getMemberFromCookies`)를 추가해 이중 보호를 유지하세요.
- 보안
  - 쿠키는 내부 API(`/api/*`) 호출에만 전달하세요. 외부 오리진으로 쿠키를 전달하면 안 됩니다.
- 성능/설계
  - 가능하면 서버에서 직접 DB/서비스 호출이 더 빠릅니다(내부 hop 제거). API Route를 공통 계층으로 쓰거나 클라이언트와 공유해야 한다면 `serverApiFetch`를 사용하세요.
  - 클라이언트 상호작용은 React Query를 사용하고, 서버 초기 데이터를 `initialData`로 하이브리드 구성하세요.

#### 흔한 실수
- 서버에서 절대 URL `fetch('https://.../api/...')`를 사용하면서 쿠키를 붙이지 않아 인증 실패(401).
- 서버에서 상대경로 `fetch('/api/...')`를 사용하다 런타임에 따라 URL 파싱 실패.

#### 파일 위치
- 유틸: `src/lib/api/server-fetch.ts`
- 스키마: `src/schemas/*`


