import {
  preparePaymentRequestSchema,
  preparePaymentSuccessResponseSchema,
  type PreparePaymentRequest,
  type PreparePaymentSuccessResponse,
  cancelPaymentRequestSchema,
  type CancelPaymentSuccessResponse,
  type CancelPaymentRequest,
} from '@/lib/validations/payment.validation';
import { browserPost } from '@/lib/api/browser-fetch';
import { z } from 'zod';

/**
 * 클라이언트 환경에서 결제 준비 요청을 수행합니다.
 * 서버 전용 fetch 유틸(next/headers 의존)을 피하기 위해 기본 fetch 사용.
 */
export async function preparePaymentClient(
  params: PreparePaymentRequest
): Promise<PreparePaymentSuccessResponse> {
  const parsed = preparePaymentRequestSchema.parse(params);

  return browserPost<PreparePaymentSuccessResponse>(
    '/api/payment/toss/prepare',
    parsed,
    {},
    preparePaymentSuccessResponseSchema
  );
}

/**
 * 클라이언트 환경에서 결제 취소 요청을 수행합니다.
 */
export async function cancelPaymentClient(
  params: CancelPaymentRequest
): Promise<CancelPaymentSuccessResponse> {
  const parsed = cancelPaymentRequestSchema.parse(params);

  return browserPost<CancelPaymentSuccessResponse>(
    '/api/payment/toss/cancel',
    parsed,
    {},
    z.any()
  );
}
