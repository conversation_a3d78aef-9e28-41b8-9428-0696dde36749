import { httpClient } from '@/lib/http-client';
import type { ScheduleRequestInput } from '@/schemas/schedule-request';

export interface CreateScheduleRequestResponse {
  message?: string;
  [key: string]: unknown;
}

export async function createScheduleRequest(
  input: ScheduleRequestInput
): Promise<CreateScheduleRequestResponse> {
  return await httpClient.post<CreateScheduleRequestResponse>(
    '/api/schedule-requests',
    input
  );
}

export const scheduleRequestsApi = {
  create: createScheduleRequest,
};
