import getEnv from '@/lib/config/get-env';

export interface TossPaymentConfirmRequest {
  paymentKey: string;
  orderId: string;
  amount: number;
}

export interface TossPaymentConfirmResponse {
  paymentKey: string;
  orderId: string;
  status: string;
  totalAmount: number;
  method: string;
  requestedAt: string;
  approvedAt: string;
  receipt?: {
    url: string;
  };
  checkout?: {
    url: string;
  };
}

export interface TossPaymentCancelRequest {
  paymentKey: string;
  cancelReason: string;
  cancelAmount?: number;
}

export interface TossPaymentCancelResponse {
  paymentKey: string;
  orderId: string;
  status: string;
  totalAmount: number;
  cancels: {
    cancelAmount: number;
    cancelReason: string;
    taxFreeAmount: number;
    taxExemptionAmount: number;
    refundableAmount: number;
    easyPayDiscountAmount: number;
    canceledAt: string;
    transactionKey: string;
    receiptKey?: string;
  }[];
  method: string;
  requestedAt: string;
  approvedAt: string;
  canceledAt: string;
}

export class TossPaymentError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = 'TossPaymentError';
  }
}

export async function confirmTossPayment(
  params: TossPaymentConfirmRequest
): Promise<TossPaymentConfirmResponse> {
  try {
    const secretKey = getEnv('TOSS_SECRET_KEY');

    if (!secretKey) {
      throw new TossPaymentError(
        '토스페이먼츠 시크릿 키가 설정되지 않았습니다.'
      );
    }

    // Base64로 인코딩된 시크릿 키 생성
    const encodedSecretKey = Buffer.from(secretKey + ':').toString('base64');

    const response = await fetch(
      'https://api.tosspayments.com/v1/payments/confirm',
      {
        method: 'POST',
        headers: {
          Authorization: `Basic ${encodedSecretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      throw new TossPaymentError(
        data.message || '결제 승인에 실패했습니다.',
        data.code,
        response.status
      );
    }

    return data;
  } catch (error) {
    if (error instanceof TossPaymentError) {
      throw error;
    }
    throw new TossPaymentError('결제 승인 요청 중 오류가 발생했습니다.');
  }
}

export async function cancelTossPayment(
  paymentKey: string,
  params: Omit<TossPaymentCancelRequest, 'paymentKey'>,
  idempotencyKey?: string
): Promise<TossPaymentCancelResponse> {
  try {
    const secretKey = getEnv('TOSS_SECRET_KEY');

    if (!secretKey) {
      throw new TossPaymentError(
        '토스페이먼츠 시크릿 키가 설정되지 않았습니다.'
      );
    }

    // Base64로 인코딩된 시크릿 키 생성
    const encodedSecretKey = Buffer.from(secretKey + ':').toString('base64');

    // 헤더 구성
    const headers: Record<string, string> = {
      Authorization: `Basic ${encodedSecretKey}`,
      'Content-Type': 'application/json',
    };

    // Idempotency Key 추가 (중복 취소 방지)
    if (idempotencyKey) {
      headers['Idempotency-Key'] = idempotencyKey;
    }

    const response = await fetch(
      `https://api.tosspayments.com/v1/payments/${paymentKey}/cancel`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      // 토스페이먼츠 특정 에러 코드 처리
      const errorMessage = getTossErrorMessage(data.code, data.message);
      throw new TossPaymentError(
        errorMessage,
        data.code,
        response.status
      );
    }

    return data;
  } catch (error) {
    if (error instanceof TossPaymentError) {
      throw error;
    }
    throw new TossPaymentError('결제 취소 요청 중 오류가 발생했습니다.');
  }
}

/**
 * 토스페이먼츠 에러 코드에 따른 사용자 친화적 메시지 반환
 */
function getTossErrorMessage(errorCode?: string, originalMessage?: string): string {
  switch (errorCode) {
    case 'ALREADY_CANCELED_PAYMENT':
      return '이미 취소된 결제입니다.';
    case 'CANCEL_AMOUNT_EXCEEDED':
      return '취소 가능 금액을 초과했습니다.';
    case 'CANCELLABLE_AMOUNT_INCONSISTENT':
      return '취소 가능 금액이 일치하지 않습니다.';
    case 'PAYMENT_NOT_FOUND':
      return '결제 정보를 찾을 수 없습니다.';
    case 'NOT_ALLOWED_POINT_USAGE':
      return '포인트 사용 결제는 취소할 수 없습니다.';
    case 'PROVIDER_ERROR':
      return '결제 서비스 제공자 오류입니다. 잠시 후 다시 시도해주세요.';
    case 'EXCEED_MAX_DAILY_PAYMENT_COUNT':
      return '일일 결제 한도를 초과했습니다.';
    case 'NOT_SUPPORTED_BANK':
      return '지원하지 않는 은행입니다.';
    case 'INVALID_REQUEST':
      return '잘못된 요청입니다.';
    case 'NOT_FOUND':
      return '요청한 리소스를 찾을 수 없습니다.';
    case 'FORBIDDEN':
      return '접근 권한이 없습니다.';
    case 'UNAUTHORIZED':
      return '인증에 실패했습니다.';
    default:
      return originalMessage || '결제 취소에 실패했습니다.';
  }
}

