import { z } from 'zod';
import { httpClient } from '@/lib/http-client';

const respSchema = z.object({ latitude: z.number(), longitude: z.number() });

export async function geocode(address: string) {
  const json = await httpClient.post('/api/utils/geocode', { address });
  const parsed = respSchema.safeParse(json);
  if (!parsed.success) {
    throw new Error('Invalid geocode response');
  }
  return parsed.data;
}

const nearestStationSchema = z.object({
  station: z.string(),
  distance: z.number(),
});

export async function findNearestStationByAddress(
  address: string,
  radiusMeters?: number
) {
  const json = await httpClient.post('/api/utils/nearest-station', {
    address,
    ...(radiusMeters ? { radiusMeters } : {}),
  });
  const parsed = nearestStationSchema.safeParse(json);
  if (!parsed.success) {
    throw new Error('Invalid nearest-station response');
  }
  return parsed.data;
}

export async function findNearestStationByCoords(
  latitude: number,
  longitude: number,
  radiusMeters?: number
) {
  const json = await httpClient.post('/api/utils/nearest-station', {
    latitude,
    longitude,
    ...(radiusMeters ? { radiusMeters } : {}),
  });
  const parsed = nearestStationSchema.safeParse(json);
  if (!parsed.success) {
    throw new Error('Invalid nearest-station response');
  }
  return parsed.data;
}
