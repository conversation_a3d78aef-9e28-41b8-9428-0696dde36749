import {
  ClassDetailResponseSchema,
  ClassListResponseSchema,
  ClassListResponse,
  ClassDetailResponse,
} from '@/app/api/classes/schema';
import { httpClient } from '@/lib/http-client';
import { buildQueryString } from '@/lib/utils';
import { z } from 'zod';

// 스키마 정의
const FilterOptionsSchema = z.object({
  levels: z.array(z.object({ value: z.string(), label: z.string() })),
  nearestStations: z.array(z.object({ value: z.string(), label: z.string() })),
  genders: z.array(z.object({ value: z.string(), label: z.string() })),
});

export async function getActiveClasses(options?: {
  page?: number;
  limit?: number;
  filters?: {
    level?: string | string[];
    nearestStation?: string | string[];
    category?: string | string[];
  };
}): Promise<ClassListResponse> {
  try {
    const url = buildQueryString('/api/classes', {
      page: options?.page,
      limit: options?.limit,
      level: options?.filters?.level,
      nearestStation: options?.filters?.nearestStation,
      category: options?.filters?.category,
    });

    const json = await httpClient.get<ClassListResponse>(url, {
      next: { revalidate: 0 },
    });
    const result = ClassListResponseSchema.safeParse(json);

    if (!result.success) {
      console.error('Classes list API 응답 검증 실패:', result.error.issues);
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  } catch (error) {
    console.error('getActiveClasses error:', error);
    throw error;
  }
}

export async function getClassDetailById(
  id: string
): Promise<ClassDetailResponse> {
  try {
    const json = await httpClient.get<ClassDetailResponse>(
      `/api/classes/${id}`
    );

    // 스키마 검증
    const result = ClassDetailResponseSchema.safeParse(json);

    if (!result.success) {
      console.error('Class detail API 응답 검증 실패:', result.error.issues);
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  } catch (error) {
    console.error('getClassDetailById error:', error);
    throw error;
  }
}

export async function getFilterOptions() {
  try {
    const json = await httpClient.get('/api/filter-options');

    // 스키마 검증
    const result = FilterOptionsSchema.safeParse(json);

    if (!result.success) {
      console.error('Filter options API 응답 검증 실패:', result.error.issues);
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error('getFilterOptions error:', error);
    return {
      success: false,
      error: 'Failed to load filter options',
    };
  }
}

export const ClassesApi = {
  getActiveClasses,
  getClassDetailById,
  getFilterOptions,
};
