import {
  ClassDetailResponseSchema,
  ClassListResponseSchema,
  ClassListResponse,
  ClassDetailResponse,
} from '@/app/api/classes/schema';
import { serverGet } from '@/lib/api/server-fetch.server';
import { z } from 'zod';

const FilterOptionsSchema = z.object({
  levels: z.array(z.object({ value: z.string(), label: z.string() })),
  nearestStations: z.array(z.object({ value: z.string(), label: z.string() })),
  genders: z.array(z.object({ value: z.string(), label: z.string() })),
});

export async function getActiveClasses(options?: {
  page?: number;
  limit?: number;
  filters?: {
    level?: string;
    nearestStation?: string;
    gender?: string;
    category?: string;
  };
}): Promise<ClassListResponse> {
  const { page, limit, filters } = options ?? {};
  
  const searchParams = Object.fromEntries(
    Object.entries({
      page: page?.toString(),
      limit: limit?.toString(),
      level: filters?.level,
      nearestStation: filters?.nearestStation,
      category: filters?.category,
    }).filter(([, value]) => value !== undefined)
  );

  if (filters?.gender) {
    console.warn('Gender filter is not supported by API yet');
  }

  return serverGet<ClassListResponse>(
    '/api/classes',
    { searchParams },
    ClassListResponseSchema
  );
}

export async function getClassDetailById(
  id: string
): Promise<ClassDetailResponse> {
  return serverGet<ClassDetailResponse>(
    `/api/classes/${id}`,
    {},
    ClassDetailResponseSchema
  );
}

export async function getFilterOptions() {
  try {
    const data = await serverGet(
      '/api/filter-options',
      {},
      FilterOptionsSchema
    );

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error('getFilterOptions error:', error);
    return {
      success: false,
      error: 'Failed to load filter options',
    };
  }
}
