import {
  ClassSchedulesResponse,
  ClassSchedulesResponseSchema,
} from '@/app/api/classes/schema';
import { httpClient, HttpClient } from '@/lib/http-client';

/**
 * 클래스 API 클래스
 */
export class ClassesApi {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * 클래스 스케줄 조회
   * @param classId 클래스 ID
   * @returns 클래스 상세 정보와 스케줄 그룹 목록
   */
  getClassSchedules = async (
    classId: string
  ): Promise<ClassSchedulesResponse> => {
    const endpoint = `/api/classes/${classId}/schedules`;

    try {
      const json = await this.httpClient.get(endpoint);
      const result = ClassSchedulesResponseSchema.safeParse(json);

      if (!result.success) {
        console.error('클래스 스케줄 API 응답 검증 실패:', result.error.issues);
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getClassSchedules error:', error);
      throw error;
    }
  };

  /**
   * 필터 옵션 조회
   * @returns 필터 옵션들
   */
  getFilterOptions = async () => {
    const endpoint = '/api/filter-options';

    try {
      const json = await this.httpClient.get(endpoint);

      // TODO: 필터 옵션 스키마 정의 필요
      // 현재는 getFilterOptions 액션을 사용하고 있음
      // API 응답 형태가 확정되면 스키마 검증 추가

      return json;
    } catch (error) {
      console.error('getFilterOptions error:', error);
      throw error;
    }
  };
}

/**
 * 클래스 API 인스턴스
 */
export const classesApi = new ClassesApi(httpClient);

// export const getActiveClasses = classesApi.getActiveClassesArray;
// export const getFilterOptions = classesApi.getFilterOptions;
export const getClassSchedules = classesApi.getClassSchedules;
