import { serverGet } from '@/lib/api/server-fetch.server';
import {
  EnrolledSchedulesResponse,
  enrolledSchedulesResponseSchema,
} from '@/lib/api/enrollments/enrollments.schema';

/**
 * 수강신청의 스케줄 그룹 및 스케줄 조회 (Server Components/Route 전용)
 * - 인증 쿠키 자동 전달
 */
export async function getEnrolledSchedules(
  enrollmentId: string
): Promise<EnrolledSchedulesResponse> {
  return await serverGet<EnrolledSchedulesResponse>(
    `/api/enrollments/${enrollmentId}/schedules`,
    {},
    enrolledSchedulesResponseSchema
  );
}
