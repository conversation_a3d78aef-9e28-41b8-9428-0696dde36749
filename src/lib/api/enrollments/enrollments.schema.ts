import { z } from 'zod';

export const enrolledSchedulesResponseSchema = z.object({
  scheduleGroup: z.object({
    id: z.number(),
    status: z.string(),
    startDate: z.string().nullable().optional(),
    schedules: z.array(
      z.object({
        id: z.number(),
        dayOfWeek: z.string(),
        startTime: z.string(),
        endTime: z.string(),
      })
    ),
  }),
});

export type EnrolledSchedulesResponse = z.infer<
  typeof enrolledSchedulesResponseSchema
>;
