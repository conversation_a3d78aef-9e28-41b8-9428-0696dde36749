import { httpClient, HttpClient } from '@/lib/http-client';
import {
  createEnrollmentRequestSchema,
  createEnrollmentResponseSchema,
  CreateEnrollmentRequest,
  CreateEnrollmentResponse,
  ErrorResponse,
} from '@/lib/validations/enrollment.validation';
import { browserGet } from '@/lib/api/browser-fetch';
import {
  EnrolledSchedulesResponse,
  enrolledSchedulesResponseSchema,
} from '@/lib/api/enrollments/enrollments.schema';

/**
 * 수강신청 API 클래스
 */
export class EnrollmentsApi {
  private httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  /**
   * 수강신청 생성
   * @param data 수강신청 요청 데이터
   * @returns 수강신청 생성 결과와 결제 위젯 정보
   */
  createEnrollment = async (
    data: CreateEnrollmentRequest
  ): Promise<CreateEnrollmentResponse> => {
    const endpoint = '/api/enrollments';

    try {
      // 요청 데이터 검증
      const validatedData = createEnrollmentRequestSchema.parse(data);

      // API 호출
      const json = await this.httpClient.post(endpoint, validatedData);

      // 응답 검증
      const result = createEnrollmentResponseSchema.safeParse(json);

      if (!result.success) {
        console.error('수강신청 API 응답 검증 실패:', result.error.issues);
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('createEnrollment error:', error);

      // HTTP 에러인 경우 에러 메시지 추출
      if (error && typeof error === 'object' && 'status' in error) {
        const httpError = error as { status: number; body?: unknown };
        if (httpError.body && typeof httpError.body === 'object') {
          const errorResponse = httpError.body as ErrorResponse;
          throw new Error(errorResponse.message || '수강신청에 실패했습니다');
        }
      }

      throw error;
    }
  };
}

/**
 * 수강신청 API 인스턴스
 */
export const enrollmentsApi = new EnrollmentsApi(httpClient);

// 편의 함수
export const createEnrollment = enrollmentsApi.createEnrollment;

// 클라이언트에서 스케줄 조회 (쿠키는 브라우저가 자동 전송)
export async function getEnrolledSchedulesClient(
  enrollmentId: string
): Promise<EnrolledSchedulesResponse> {
  return await browserGet<EnrolledSchedulesResponse>(
    `/api/enrollments/${enrollmentId}/schedules`,
    {},
    enrolledSchedulesResponseSchema
  );
}
