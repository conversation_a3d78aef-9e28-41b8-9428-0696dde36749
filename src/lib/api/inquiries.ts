import { httpClient } from '@/lib/http-client';
import { CreateInquiryRequest, CreateInquiryResponse } from '@/schemas/inquiry';

/**
 * 문의하기 API 함수
 * @param content 문의 내용
 * @returns 문의 생성 응답
 */
export async function createInquiry(
  content: string
): Promise<CreateInquiryResponse> {
  const request: CreateInquiryRequest = { content };

  return await httpClient.post<CreateInquiryResponse>(
    '/api/inquiries',
    request
  );
}

export const inquiriesApi = {
  create: createInquiry,
};
