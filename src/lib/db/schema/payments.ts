import {
  pgTable,
  pgEnum,
  uuid,
  text,
  integer,
  timestamp,
  json,
  index,
  unique,
  jsonb,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { enrollments } from './enrollments';

// Enum 정의
export const paymentTypeEnum = pgEnum('payment_type', [
  'deposit',
  'remaining',
  'refund',
]);

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending',
  'paid',
  'failed',
  'cancelled',
  'refunded',
]);

export const paymentEventTypeEnum = pgEnum('payment_event_type', [
  'payment_prepared',
  'payment_requested',
  'payment_succeeded',
  'payment_failed',
  'payment_cancelled',
  'refund_requested',
  'refund_completed',
  'webhook_received',
]);

// PG사별 추가 데이터 타입 정의
type PaymentData = {
  method?: string;
  cardType?: string;
  lastFourDigits?: string;
  issuerName?: string;
  acquirerName?: string;
  installmentPlanMonths?: number;
  approvedAt?: string;
  [key: string]: any;
};

// payments 테이블
export const payments = pgTable(
  'payments',
  {
    id: uuid().primaryKey().defaultRandom(),
    enrollmentId: uuid('enrollment_id').notNull(),
    memberId: uuid('member_id').notNull(),

    // 결제 정보
    paymentMethod: text('payment_method').notNull(),
    paymentType: text('payment_type').notNull(),
    paymentStatus: text('payment_status').notNull().default('pending'),
    amount: integer('amount').notNull(),
    currency: text('currency').notNull().default('KRW'),

    // 거래 정보
    transactionId: text('transaction_id'),
    pgTransactionId: text('pg_transaction_id'),
    merchantUid: text('merchant_uid'),
    paymentData: json('payment_data').$type<PaymentData>(),

    // 실패/취소 사유
    failureReason: text('failure_reason'),
    cancelledReason: text('cancelled_reason'),

    // 타임스탬프
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
    paidAt: timestamp('paid_at', { withTimezone: true }),
    failedAt: timestamp('failed_at', { withTimezone: true }),
    cancelledAt: timestamp('cancelled_at', { withTimezone: true }),
    refundedAt: timestamp('refunded_at', { withTimezone: true }),
  },
  table => ({
    // 인덱스 (실제 DB와 일치)
    enrollmentIdx: index('idx_payments_enrollment_id').on(table.enrollmentId),
    memberIdx: index('idx_payments_member_id').on(table.memberId),
    transactionIdx: index('idx_payments_transaction_id').on(
      table.transactionId
    ),
  })
);

// 이벤트 데이터 타입 정의
type PaymentEventData = {
  amount?: number;
  paymentKey?: string;
  enrollmentOrder?: number;
  reason?: string;
  webhookType?: string;
  [key: string]: any;
};

// payment_events 테이블
// payment_prepare 테이블 (결제 준비 정보 저장)
export const paymentPrepare = pgTable(
  'payment_prepare',
  {
    id: uuid().primaryKey().defaultRandom(),
    enrollmentId: uuid('enrollment_id').notNull(),
    memberId: uuid('member_id').notNull(),
    classId: uuid('class_id').notNull(),
    scheduleGroupId: integer('schedule_group_id').notNull(),

    // 준비 정보
    orderId: text('order_id').notNull(),
    amount: integer('amount').notNull(),

    // 상태 및 검증
    isUsed: integer('is_used').notNull().default(0), // 0: 미사용, 1: 사용됨
    expiresAt: timestamp('expires_at', { withTimezone: true }).notNull(),

    // 타임스탬프
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  },
  table => ({
    // 인덱스
    enrollmentIdx: index('idx_payment_prepare_enrollment').on(
      table.enrollmentId
    ),
    memberIdx: index('idx_payment_prepare_member').on(table.memberId),
    classScheduleIdx: index('idx_payment_prepare_class_schedule').on(
      table.classId,
      table.scheduleGroupId
    ),
    orderIdx: index('idx_payment_prepare_order').on(table.orderId),
    expiresIdx: index('idx_payment_prepare_expires').on(table.expiresAt),
  })
);

export const paymentEvents = pgTable(
  'payment_events',
  {
    id: uuid().primaryKey().defaultRandom(),
    paymentId: uuid('payment_id'),
    enrollmentId: uuid('enrollment_id').notNull(),

    // 이벤트 정보
    eventType: text('event_type').notNull(),

    // 이벤트 데이터 및 메타정보
    eventData: jsonb('event_data'),
    errorMessage: text('error_message'),

    // 타임스탬프
    createdAt: timestamp('created_at', {
      precision: 6,
      withTimezone: true,
    }).defaultNow(),
  },
  table => ({
    // 인덱스
    paymentIdx: index('idx_payment_events_payment').on(table.paymentId),
    enrollmentIdx: index('idx_payment_events_enrollment').on(
      table.enrollmentId
    ),
    typeIdx: index('idx_payment_events_type').on(table.eventType),
    createdIdx: index('idx_payment_events_created').on(table.createdAt),
  })
);

// 타입 추출
export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;
export type PaymentUpdate = Partial<
  Pick<
    Payment,
    | 'paymentStatus'
    | 'transactionId'
    | 'pgTransactionId'
    | 'merchantUid'
    | 'paymentData'
    | 'paidAt'
    | 'failedAt'
    | 'cancelledAt'
    | 'refundedAt'
    | 'updatedAt'
    | 'failureReason'
    | 'cancelledReason'
  >
>;

export type PaymentPrepare = typeof paymentPrepare.$inferSelect;
export type NewPaymentPrepare = typeof paymentPrepare.$inferInsert;

export type PaymentEvent = typeof paymentEvents.$inferSelect;
export type NewPaymentEvent = typeof paymentEvents.$inferInsert;

// Relations 정의
export const enrollmentRelations = relations(enrollments, ({ many }) => ({
  payments: many(payments),
  paymentEvents: many(paymentEvents),
}));

export const paymentRelations = relations(payments, ({ one, many }) => ({
  enrollment: one(enrollments, {
    fields: [payments.enrollmentId],
    references: [enrollments.id],
  }),
  events: many(paymentEvents),
}));

export const paymentEventRelations = relations(paymentEvents, ({ one }) => ({
  payment: one(payments, {
    fields: [paymentEvents.paymentId],
    references: [payments.id],
  }),
  enrollment: one(enrollments, {
    fields: [paymentEvents.enrollmentId],
    references: [enrollments.id],
  }),
}));
