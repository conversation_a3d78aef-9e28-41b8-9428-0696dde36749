import {
  pgTable,
  pgEnum,
  uuid,
  text,
  integer,
  timestamp,
  index,
  unique,
} from 'drizzle-orm/pg-core';

// 수강신청 상태 정의
export const enrollmentStatusEnum = pgEnum('enrollment_status', [
  'pending',    // 신청 완료, 결제 대기 중
  'paid',       // 결제 완료, 수업 확정 대기 중
  'confirmed',  // 수업 확정됨 (정원 내 선착순으로 확정)
  'cancelled',  // 개별 취소 (고객이 직접 취소한 경우)
  'refunded'    // 환불 완료 (수업 취소나 정원 초과로 인한 환불)
]);

// enrollments 테이블
export const enrollments = pgTable(
  'enrollments',
  {
    id: uuid().primaryKey().defaultRandom(),
    memberId: uuid('member_id').notNull(),
    classId: uuid('class_id').notNull(),
    scheduleGroupId: integer('schedule_group_id').notNull(),

    // 상태 관리
    status: enrollmentStatusEnum().notNull().default('pending'),

    // 순서 관리 (선착순 확정을 위함)
    enrollmentOrder: integer('enrollment_order'),

    // 금액 정보
    totalAmount: integer('total_amount').notNull(),
    depositAmount: integer('deposit_amount').notNull(),

    // 취소 관련
    cancellationReason: text('cancellation_reason'),

    // 메타데이터
    createdAt: timestamp('created_at', {
      precision: 6,
      withTimezone: true,
    }).defaultNow(),
    updatedAt: timestamp('updated_at', {
      precision: 6,
      withTimezone: true,
    })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  table => ({
    // 인덱스
    classOrderIdx: index('idx_enrollments_class_order').on(
      table.classId,
      table.scheduleGroupId,
      table.enrollmentOrder
    ),
    statusIdx: index('idx_enrollments_status').on(table.status),
    memberIdx: index('idx_enrollments_member').on(table.memberId),
    createdIdx: index('idx_enrollments_created').on(table.createdAt),
    updatedIdx: index('idx_enrollments_updated').on(table.updatedAt),

    // 유니크 제약
    uniqueMemberClass: unique('enrollments_unique_member_class').on(
      table.memberId,
      table.classId,
      table.scheduleGroupId
    ),
  })
);

// 타입 추출
export type Enrollment = typeof enrollments.$inferSelect;
export type NewEnrollment = typeof enrollments.$inferInsert;
export type EnrollmentUpdate = Partial<
  Pick<Enrollment, 'status' | 'enrollmentOrder' | 'cancellationReason' | 'updatedAt'>
>;
