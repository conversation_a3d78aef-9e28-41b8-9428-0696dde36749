import { drizzle } from 'drizzle-orm/postgres-js';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { configManager } from '../config/config-manager';

// 서버 사이드에서만 DB 연결 생성
function createDatabaseConnection() {
  const connectionString = configManager.getValue('databaseUrl');

  if (!connectionString) {
    throw new Error(
      'Database URL is not available. This should only be used on the server side.'
    );
  }

  const isLocal = configManager.isLocal();
  const client = postgres(connectionString, {
    max: isLocal ? 5 : 20,
    debug: isLocal,
    prepare: false, // Supabase에서 트랜잭션 커밋 문제 해결을 위해 비활성화
    // 기본 트랜잭션 격리 수준 설정 (Drizzle 버그 회피)
    connection: {
      options: '--default-transaction-isolation=repeatable-read',
    },
  });

  return drizzle(client, {
    schema,
    logger: isLocal,
  });
}

// 서버 사이드에서만 접근 가능한 DB 인스턴스
export const db: PostgresJsDatabase<typeof schema> = createDatabaseConnection();

// Health check function (옵션: 필요시 사용)
export async function checkDatabaseConnection() {
  try {
    await db.execute('SELECT 1');
    return { success: true, message: 'Database connection successful' };
  } catch (error) {
    return {
      success: false,
      message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

export type * as Schema from './schema';
