'use server';

import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * 서버용 통합 사용자 인증 유틸리티
 * 기존 코드의 공통 부분을 추출하여 한 번의 호출로 최적화
 */

export interface UserData {
  id: string;
  email: string;
  user_metadata?: any;
  app_metadata?: any;
}

export interface UserAuthResult {
  success: boolean;
  user: UserData | null;
  role: 'MEMBER' | 'PARTNER' | null;
  error?: string;
}

/**
 * 쿠키에서 Supabase 클라이언트 생성 (기존 코드에서 추출)
 */
function createSupabaseFromCookies() {
  const cookieStore = cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string) {
          return (await cookieStore).get(name)?.value;
        },
      },
    }
  );
}

/**
 * 서버 컴포넌트에서 사용자 정보 및 역할 조회
 * 기존 코드를 활용하되 DB 쿼리를 한 번만 수행하도록 최적화
 * RootLayout에서 초기 하이드레이션용으로 사용
 */
export async function getUserFromCookies(): Promise<UserAuthResult> {
  try {
    const supabase = createSupabaseFromCookies();

    // 1. 사용자 인증 확인 (기존 코드와 동일)
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        user: null,
        role: null,
        error: authError?.message || '인증되지 않은 사용자',
      };
    }

    // 2. 파트너 테이블에서 먼저 확인 (기존 getPartnerFromCookies 로직)
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (!partnerError && partner) {
      return {
        success: true,
        user: {
          id: partner.user_id,
          email: partner.email || user.email || '',
          user_metadata: user.user_metadata,
          app_metadata: user.app_metadata,
        },
        role: 'PARTNER',
      };
    }

    // 3. 파트너가 아니면 회원 테이블에서 확인 (기존 getMemberFromCookies 로직)
    const { data: member, error: memberError } = await supabase
      .from('members')
      .select('*')
      .eq('id', user.id)
      .single();

    if (!memberError && member) {
      return {
        success: true,
        user: {
          id: member.id,
          email: member.email || user.email || '',
          user_metadata: user.user_metadata,
          app_metadata: user.app_metadata,
        },
        role: 'MEMBER',
      };
    }

    // 4. 둘 다 아니면 인증되지 않은 사용자
    return {
      success: false,
      user: null,
      role: null,
      error: '계정 정보를 찾을 수 없습니다.',
    };
  } catch (error) {
    console.error('사용자 정보 조회 오류:', error);
    return {
      success: false,
      user: null,
      role: null,
      error: '서버 오류가 발생했습니다.',
    };
  }
}
