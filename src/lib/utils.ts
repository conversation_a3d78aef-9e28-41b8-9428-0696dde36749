import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { v4 as uuidv4 } from 'uuid';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function uuid() {
  return uuidv4();
}

// 요일 정렬을 위한 유틸리티 함수들
export function getDayOrderIndex(dayLabel: string): number {
  const dayOrderIndexByKoreanLabel: Record<string, number> = {
    월요일: 1,
    화요일: 2,
    수요일: 3,
    목요일: 4,
    금요일: 5,
    토요일: 6,
    일요일: 7,
  };
  return dayOrderIndexByKoreanLabel[dayLabel] ?? Number.POSITIVE_INFINITY;
}

export function parseStartMinutesFromRange(timeRange: string): number {
  const startPart = timeRange.split('~')[0]?.trim() ?? '';
  const [hours, minutes] = startPart.split(':').map(v => Number(v));
  if (Number.isNaN(hours) || Number.isNaN(minutes))
    return Number.POSITIVE_INFINITY;
  return hours * 60 + minutes;
}

export function sortTime(
  times: Array<{
    day: string;
    time: string;
  }>
): Array<{
  day: string;
  time: string;
}> {
  return [...times].sort((a, b) => {
    const dayA = getDayOrderIndex(a.day);
    const dayB = getDayOrderIndex(b.day);
    if (dayA !== dayB) return dayA - dayB;
    const minutesA = parseStartMinutesFromRange(a.time);
    const minutesB = parseStartMinutesFromRange(b.time);
    return minutesA - minutesB;
  });
}

// Query string 생성을 위한 타입과 함수
export type SearchParams = Record<
  string,
  | string
  | number
  | boolean
  | Array<string | number | boolean>
  | null
  | undefined
>;

export function buildQueryString(
  baseUrl: string,
  params?: SearchParams
): string {
  if (!params) return baseUrl;

  const usp = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value === undefined || value === null) return;
    if (Array.isArray(value)) {
      value.forEach(v => {
        if (v === undefined || v === null) return;
        usp.append(key, String(v));
      });
      return;
    }
    usp.set(key, String(value));
  });

  const queryString = usp.toString();
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
}
