// 수강 신청 목록 조회 훅
import type {
  EnrollmentListSuccessResponse,
  EnrollmentErrorResponse,
} from '@/lib/validations/enrollments.validation';
import { useQuery } from '@tanstack/react-query';
import { partnerApi } from '../api/partner/partner.api';

export const useScheduleGroupEnrollments = (
  scheduleGroupId: number,
  enabled: boolean,
  query: {
    sortBy?: 'enrollmentOrder' | 'createdAt';
    sortOrder?: 'asc' | 'desc';
  } = {}
) => {
  return useQuery<EnrollmentListSuccessResponse | EnrollmentErrorResponse>({
    queryKey: [
      'partner',
      'schedule-groups',
      scheduleGroupId,
      'enrollments',
      query,
    ],
    queryFn: async () => {
      const response = await partnerApi.getScheduleGroupEnrollments(
        scheduleGroupId,
        query
      );
      return response;
    },
    enabled,
    staleTime: 0,
  });
};
