import { partnerApi } from '@/lib/api/partner/partner.api';
import type {
  GetScheduleGroupsRequest,
  GetScheduleGroupsResponse,
} from '@/lib/api/partner/partner.schema';
import type { ScheduleGroupStatus } from '@/lib/types/schedule-groups.types';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import type { ScheduleGroupListItem } from '@/lib/types/schedule-groups.types';

/**
 * 파트너 스케줄 그룹 목록을 조회하는 Infinite Suspense Query 훅
 *
 * @param filters - 스케줄 그룹 조회 필터
 * @param options - 쿼리 옵션
 * @returns 스케줄 그룹 목록 데이터와 무한 스크롤 관련 상태
 */
export const useScheduleGroups = (
  filters: {
    status?: ScheduleGroupStatus | ScheduleGroupStatus[];
    instructorId?: string | string[];
    category?: string | string[];
    activeStatusTab?: 'all' | ScheduleGroupStatus;
    limit?: number;
  } = {},
  options: {
    enabled?: boolean;
    staleTime?: number;
    refetchInterval?: number;
    initialData?: GetScheduleGroupsResponse; // 서버에서 하이드레이션된 데이터
  } = {}
) => {
  const { limit = 10, activeStatusTab, ...restFilters } = filters;
  const { staleTime = 60 * 1000, refetchInterval, initialData } = options;

  const statusFromTab =
    activeStatusTab && activeStatusTab !== 'all' ? activeStatusTab : undefined;

  // 쿼리 키를 단순화하여 필터 변경 시 확실한 리페치 보장
  // 배열을 문자열로 직렬화하여 키 변경을 확실히 감지
  const queryKey = [
    'partner',
    'schedule-groups',
    'infinite',
    statusFromTab,
    restFilters.instructorId
      ? JSON.stringify(restFilters.instructorId)
      : undefined,
    restFilters.category ? JSON.stringify(restFilters.category) : undefined,
    limit,
  ];

  const isDefaultState =
    statusFromTab === 'recruiting' &&
    !restFilters.instructorId &&
    !restFilters.category;

  const result = useSuspenseInfiniteQuery({
    queryKey,
    queryFn: ({ pageParam = 1 }) => {
      const request: GetScheduleGroupsRequest = {
        page: pageParam,
        limit,
        ...restFilters,
        ...(statusFromTab ? { status: statusFromTab } : {}),
      } as GetScheduleGroupsRequest;

      return partnerApi.getScheduleGroups(request);
    },
    staleTime,
    refetchInterval,
    // 기본 상태일 때만 initialData 사용
    initialData:
      isDefaultState && initialData
        ? {
            pages: [initialData],
            pageParams: [1],
          }
        : undefined,
    // 무한 스크롤 페이지네이션 설정
    initialPageParam: 1,
    getNextPageParam: lastPage => {
      if (lastPage?.success && lastPage.data?.pagination?.hasNext) {
        return lastPage.data.pagination.page + 1;
      }
      return undefined;
    },
    getPreviousPageParam: firstPage => {
      if (
        firstPage?.success &&
        firstPage.data?.pagination?.page &&
        firstPage.data.pagination.page > 1
      ) {
        return firstPage.data.pagination.page - 1;
      }
      return undefined;
    },
  });

  // 모든 페이지의 스케줄 그룹 데이터를 평탄화
  const scheduleGroups: ScheduleGroupListItem[] =
    result.data?.pages?.flatMap(page =>
      page?.success && Array.isArray(page.data?.scheduleGroups)
        ? page.data.scheduleGroups
        : []
    ) || [];

  // 첫 번째 페이지의 전체 pagination 정보
  const pagination = result.data?.pages?.[0]?.success
    ? result.data.pages[0].data.pagination
    : null;

  return {
    ...result,
    // 편의를 위해 평탄화된 데이터와 pagination 정보 제공
    scheduleGroups,
    pagination,
    // 호환성을 위한 기존 data 형태도 제공
    data: result.data?.pages?.[0] || null,
  };
};

export const scheduleGroupsKeys = {
  all: ['partner', 'schedule-groups'] as const,
  lists: () => [...scheduleGroupsKeys.all, 'list'] as const,
  list: (filters: GetScheduleGroupsRequest) =>
    [...scheduleGroupsKeys.lists(), filters] as const,
};
