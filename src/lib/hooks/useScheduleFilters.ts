import { useState, useCallback } from 'react';
import type { ScheduleGroupStatus } from '@/lib/types/schedule-groups.types';

export interface ScheduleFiltersState {
  instructorId?: string[];
  category?: string[];
  activeStatusTab: 'all' | ScheduleGroupStatus;
  page: number;
  limit: number;
}

const DEFAULT_FILTERS: ScheduleFiltersState = {
  activeStatusTab: 'all',
  page: 1,
  limit: 10,
};

/**
 * 스케줄 그룹 필터 상태를 관리하는 훅
 */
export const useScheduleFilters = (
  initialFilters: Partial<ScheduleFiltersState> = {}
) => {
  const [filters, setFilters] = useState<ScheduleFiltersState>({
    ...DEFAULT_FILTERS,
    ...initialFilters,
  });

  const updateFilters = useCallback(
    (newFilters: Partial<ScheduleFiltersState>) => {
      setFilters(prev => ({
        ...prev,
        ...newFilters,
        // 필터가 변경되면 첫 페이지로 리셋
        page: newFilters.page !== undefined ? newFilters.page : 1,
      }));
    },
    []
  );

  const setInstructorId = useCallback(
    (instructorId: string | undefined) => {
      updateFilters({
        instructorId: instructorId ? [instructorId] : undefined,
      });
    },
    [updateFilters]
  );

  const setCategory = useCallback(
    (category: string | undefined) => {
      updateFilters({ category: category ? [category] : undefined });
    },
    [updateFilters]
  );

  const setPage = useCallback(
    (page: number) => {
      updateFilters({ page });
    },
    [updateFilters]
  );

  const clearAllFilters = useCallback(() => {
    // 상태 탭은 유지하고, 나머지 필터만 초기화
    setFilters(prev => ({
      activeStatusTab: prev.activeStatusTab,
      instructorId: undefined,
      category: undefined,
      page: 1,
      limit: prev.limit ?? DEFAULT_FILTERS.limit,
    }));
  }, []);

  const setActiveStatusTab = useCallback(
    (activeStatusTab: 'all' | ScheduleGroupStatus) => {
      updateFilters({ activeStatusTab });
    },
    [updateFilters]
  );

  const hasActiveFilters = !!(
    (filters.instructorId && filters.instructorId.length > 0) ||
    (filters.category && filters.category.length > 0)
  );

  return {
    filters,
    setInstructorId,
    setCategory,
    setPage,
    clearAllFilters,
    setActiveStatusTab,
    hasActiveFilters,
    updateFilters,
  };
};

export type UseScheduleFiltersReturn = ReturnType<typeof useScheduleFilters>;
