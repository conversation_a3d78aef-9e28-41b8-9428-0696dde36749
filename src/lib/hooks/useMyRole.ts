'use client';

import { useSuspenseQuery } from '@tanstack/react-query';
import { z } from 'zod';

const MyRoleResponseSchema = z.object({
  userId: z.string().min(1),
});

type MyRoleResponse = z.infer<typeof MyRoleResponseSchema>;

export function useMyRole() {
  return useSuspenseQuery<MyRoleResponse, Error>({
    queryKey: ['my-role'],
    queryFn: async () => {
      const res = await fetch('/api/user/role', { cache: 'no-store' });
      if (!res.ok) {
        const text = await res.text().catch(() => '');
        throw new Error(text || '사용자 정보를 불러오지 못했습니다.');
      }
      const json = await res.json();
      return MyRoleResponseSchema.parse(json);
    },
    staleTime: 5 * 60 * 1000,
  });
}
