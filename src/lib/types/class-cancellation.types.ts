/**
 * 파트너 수업 취소 관련 타입 정의
 */

export interface CancelClassParams {
  classId: string;
  scheduleGroupId: number;
  partnerId: string;
  cancelReason: string;
}

export interface PaymentInfo {
  id: string;
  enrollmentId: string;
  memberId: string;
  amount: number;
  paymentMethod: string;
  transactionId: string | null;
  pgTransactionId: string | null;
  merchantUid: string | null;
  paymentStatus: string;
  paidAt: Date | null;
}

export interface RefundProcessResult {
  enrollmentId: string;
  memberId: string;
  paymentId: string;
  refundAmount: number;
  status: 'success' | 'failed';
  error?: string;
}

export interface CancelClassResult {
  classId: string;
  scheduleGroupId: number;
  cancelledAt: string;
  refundedCount: number;
  totalRefundAmount: number;
  refunds: RefundProcessResult[];
}

export class BusinessRuleError extends Error {
  constructor(public code: string, message: string) {
    super(message);
    this.name = 'BusinessRuleError';
  }
}