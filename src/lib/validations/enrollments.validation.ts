import { z } from 'zod';

/**
 * 쿼리 파라미터 스키마
 */
export const enrollmentQuerySchema = z.object({
  sortBy: z
    .enum(['enrollmentOrder', 'createdAt'])
    .optional()
    .default('enrollmentOrder'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('asc'),
});

/**
 * 수강 신청 정보 스키마
 */
export const enrollmentItemSchema = z.object({
  id: z.string(),
  memberId: z.string(),
  nickname: z.string(),
  name: z.string().nullish(),
  gender: z.string().nullish(),
  birthDate: z.string().nullish(),
  status: z.string(),
  enrollmentOrder: z.number().nullish(),
  createdAt: z.string(),
});

/**
 * API 성공 응답 스키마
 */
export const enrollmentListSuccessResponseSchema = z.object({
  success: z.literal(true),
  data: z.object({
    scheduleGroupId: z.number(),
    enrollments: z.array(enrollmentItemSchema),
  }),
});

/**
 * API 에러 응답 스키마
 */
export const enrollmentErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  message: z.string(),
});

/**
 * 타입 추출
 */
export type EnrollmentQuery = z.infer<typeof enrollmentQuerySchema>;
export type EnrollmentItem = z.infer<typeof enrollmentItemSchema>;
export type EnrollmentListSuccessResponse = z.infer<
  typeof enrollmentListSuccessResponseSchema
>;
export type EnrollmentErrorResponse = z.infer<
  typeof enrollmentErrorResponseSchema
>;
