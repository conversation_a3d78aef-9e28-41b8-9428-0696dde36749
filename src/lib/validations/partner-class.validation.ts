import { z } from 'zod';
import {
  CLASS_CATEGORIES,
  ClassLevelText,
  ClassTargetText,
  ClassStatusText,
  DayOfWeekCode,
} from '@/lib/db/schema';

// 클래스 카테고리 enum - CLASS_CATEGORIES에서 자동 생성
const categoryEnum = z.enum(
  CLASS_CATEGORIES.map(category => category.code) as [string, ...string[]]
);

// 클래스 레벨 enum
const levelEnum = z.enum([
  ClassLevelText.BEGINNER,
  ClassLevelText.INTERMEDIATE,
  ClassLevelText.ADVANCED,
]);

// 클래스 대상 enum
const targetEnum = z.enum([
  ClassTargetText.WOMEN_ONLY,
  ClassTargetText.MEN_ONLY,
  ClassTargetText.MIXED,
]);

// 개별 스케줄 검증 스키마
const scheduleSchema = z.object({
  dayOfWeek: z.enum([
    DayOfWeekCode.SUN,
    DayOfWeekCode.MON,
    DayOfWeekCode.TUE,
    DayOfWeekCode.WED,
    DayOfWeekCode.THU,
    DayOfWeekCode.FRI,
    DayOfWeekCode.SAT,
  ]), // sun, mon, tue, wed, thu, fri, sat
  startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/), // HH:MM 또는 HH:MM:SS
  endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/),
});

// 스케줄 그룹 검증 스키마
const scheduleGroupSchema = z.object({
  schedules: z
    .array(scheduleSchema)
    .min(1, '각 그룹에는 최소 1개 이상의 스케줄이 필요합니다')
    .max(7, '각 그룹에는 최대 7개까지 스케줄 설정 가능합니다')
    .refine(
      schedules => {
        // 중복된 요일 체크 (그룹 내에서)
        const days = schedules.map(s => s.dayOfWeek);
        return new Set(days).size === days.length;
      },
      {
        message:
          '같은 그룹 내에서 동일한 요일에 여러 스케줄을 설정할 수 없습니다',
      }
    )
    .refine(
      schedules => {
        // 시작 시간이 종료 시간보다 빠른지 체크
        return schedules.every(s => s.startTime < s.endTime);
      },
      {
        message: '시작 시간은 종료 시간보다 빨라야 합니다',
      }
    ),
  // .refine((schedules) => {
  //   // 같은 그룹 내 모든 스케줄은 동일한 시간대여야 함
  //   if (schedules.length <= 1) return true;
  //   const firstSchedule = schedules[0];
  //   return schedules.every(s =>
  //     s.startTime === firstSchedule.startTime &&
  //     s.endTime === firstSchedule.endTime
  //   );
  // }, {
  //   message: '같은 그룹 내 모든 스케줄은 동일한 시간대여야 합니다'
  // }),
});

// 클래스 생성 스키마
export const createClassSchema = z.object({
  studioId: z.uuid({ message: '유효한 스튜디오 ID가 아닙니다' }),
  instructorId: z.uuid({ message: '유효한 강사 ID가 아닙니다' }),
  title: z
    .string()
    .min(1, '클래스 이름은 필수입니다')
    .max(30, '클래스 이름은 최대 30자까지 가능합니다'),
  description: z
    .string()
    .min(1, '클래스 설명은 필수입니다')
    .max(500, '클래스 설명은 최대 500자까지 가능합니다'),
  category: categoryEnum,
  level: levelEnum,
  target: targetEnum,
  maxParticipants: z
    .number()
    .int('정수값을 입력해주세요')
    .min(2, '최소 2명 이상이어야 합니다')
    .max(10, '최대 10명까지 가능합니다'),
  pricePerSession: z
    .number()
    .int('정수값을 입력해주세요')
    .min(5000, '최소 5,000원 이상이어야 합니다')
    .max(40000, '최대 40,000원까지 가능합니다')
    .multipleOf(1000, '1,000원 단위로 입력해주세요'),
  sessionDurationMinutes: z
    .number()
    .int('정수값을 입력해주세요')
    .min(30, '최소 30분 이상이어야 합니다')
    .max(180, '최대 180분까지 가능합니다'),
  durationWeeks: z
    .number()
    .int('정수값을 입력해주세요')
    .refine(val => val === 4 || val === 8, {
      message: '수업 기간은 4주(1개월) 또는 8주(2개월)만 가능합니다',
    }),
  sessionsPerWeek: z
    .number()
    .int('정수값을 입력해주세요')
    .min(1, '주 1회 이상이어야 합니다')
    .max(7, '주 7회까지 가능합니다'),
  images: z
    .array(
      z.object({
        url: z.url({ message: '유효한 URL이 아닙니다' }),
        path: z.string().min(1, '이미지 경로는 필수입니다'),
      })
    )
    .min(1, '최소 1개 이상의 이미지는 필수입니다')
    .max(5, '이미지는 최대 5장까지 업로드 가능합니다'),
  scheduleGroups: z
    .array(scheduleGroupSchema)
    .min(1, '최소 1개 이상의 스케줄 그룹을 설정해주세요')
    .max(5, '최대 5개까지 스케줄 그룹 설정 가능합니다'),
});

// 클래스 수정 스키마 (모든 필드 optional)
export const updateClassSchema = createClassSchema.partial().extend({
  visible: z.boolean().optional(),
  status: z.enum([ClassStatusText.ACTIVE, ClassStatusText.INACTIVE]).optional(),
});

// 클래스 부분 업데이트 스키마 (PATCH용)
export const patchClassSchema = z.object({
  visible: z.boolean().optional(),
  status: z.enum([ClassStatusText.ACTIVE, ClassStatusText.INACTIVE]).optional(),
}).refine(
  (data) => Object.keys(data).length > 0,
  {
    message: '최소 하나 이상의 필드가 필요합니다',
  }
);

// 클래스 목록 조회 필터 스키마 (간소화)
export const classListFilterSchema = z.object({
  studioId: z.uuid().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(10),
});

// 타입 추출
export type CreateClassInput = z.infer<typeof createClassSchema>;
export type UpdateClassInput = z.infer<typeof updateClassSchema>;
export type PatchClassInput = z.infer<typeof patchClassSchema>;
export type ClassListFilter = z.infer<typeof classListFilterSchema>;
