import { z } from 'zod';

// 결제 준비 요청 스키마
export const preparePaymentRequestSchema = z.object({
  enrollmentId: z.uuid('올바른 수강신청 ID가 아닙니다'),
  orderId: z.string().min(1, '주문 ID가 필요합니다'),
  amount: z.number().int().positive('결제 금액은 0보다 커야 합니다'),
});

// 결제 준비 성공 응답 스키마
export const preparePaymentSuccessResponseSchema = z.object({
  success: z.literal(true),
  prepareId: z.uuid(),
  message: z.string(),
});

// 결제 준비 에러 응답 스키마
export const preparePaymentErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().min(1, '에러 코드가 필요합니다'),
  message: z.string().min(1, '에러 메시지가 필요합니다'),
  details: z.unknown().optional(),
});

// 결제 승인 요청 스키마 (extra 필드 허용)
export const confirmPaymentRequestSchema = z.object({
  paymentKey: z.string().min(1, 'Payment key is required'),
  orderId: z.uuid('올바른 주문 ID가 아닙니다'),
  amount: z.number().int().positive('Amount must be greater than 0'),
}); // strict() 없음으로 확장성 보장

// 결제 승인 성공 응답 스키마
export const confirmPaymentSuccessResponseSchema = z.object({
  success: z.literal(true),
  enrollment: z.object({
    id: z.uuid(),
    status: z.literal('paid'),
    enrollmentOrder: z.number().int().positive(),
    totalAmount: z.number().int().positive(),
    depositAmount: z.number().int().positive(),
    updatedAt: z.string(), // ISO string
  }),
  payment: z.object({
    id: z.string().uuid(),
    status: z.literal('paid'),
    amount: z.number().int().positive(),
    paymentMethod: z.string().min(1),
    transactionId: z.string().min(1),
    paidAt: z.string(), // ISO string
  }),
  tossResult: z.object({
    paymentKey: z.string(),
    orderId: z.string(),
    status: z.string(),
    totalAmount: z.number(),
    method: z.string(),
    approvedAt: z.string(),
  }),
});

// 결제 승인 에러 응답 스키마 (enrollments와 통일)
export const confirmPaymentErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().min(1, '에러 코드가 필요합니다'),
  message: z.string().min(1, '에러 메시지가 필요합니다'),
  details: z.unknown().optional(), // 추가 에러 정보
});

// 토스페이먼츠 웹훅 스키마
export const tossWebhookSchema = z.object({
  eventType: z.string(),
  createdAt: z.string(),
  data: z.object({
    paymentKey: z.string(),
    orderId: z.string(),
    status: z.string(),
    totalAmount: z.number(),
    method: z.string().optional(),
    approvedAt: z.string().optional(),
    cancels: z.array(z.any()).optional(),
  }),
});

// 타입 추출
export type PreparePaymentRequest = z.infer<typeof preparePaymentRequestSchema>;
export type PreparePaymentSuccessResponse = z.infer<typeof preparePaymentSuccessResponseSchema>;
export type PreparePaymentErrorResponse = z.infer<typeof preparePaymentErrorResponseSchema>;
export type PreparePaymentResponse = PreparePaymentSuccessResponse | PreparePaymentErrorResponse;

export type ConfirmPaymentRequest = z.infer<typeof confirmPaymentRequestSchema>;
export type ConfirmPaymentSuccessResponse = z.infer<
  typeof confirmPaymentSuccessResponseSchema
>;
export type ConfirmPaymentErrorResponse = z.infer<
  typeof confirmPaymentErrorResponseSchema
>;
export type ConfirmPaymentResponse =
  | ConfirmPaymentSuccessResponse
  | ConfirmPaymentErrorResponse;
export type TossWebhook = z.infer<typeof tossWebhookSchema>;

// 결제 취소 요청 스키마
export const cancelPaymentRequestSchema = z.object({
  paymentKey: z.string().min(1, 'Payment key가 필요합니다'),
  cancelReason: z.string().optional(), // 취소 사유 (선택적)
});

// 결제 취소 성공 응답 스키마
export const cancelPaymentSuccessResponseSchema = z.object({
  success: z.literal(true),
  cancellation: z.object({
    cancelAmount: z.number(),
    refundAmount: z.number(), // 실제 환불될 금액
    refundRate: z.number(),   // 환불율 (0~1)
    cancelReason: z.string(),
    canceledAt: z.string(),
  }),
  enrollment: z.object({
    id: z.string().uuid(),
    status: z.literal('cancelled'),
    updatedAt: z.string(),
  }),
  payment: z.object({
    id: z.string().uuid(),
    status: z.literal('cancelled'),
    updatedAt: z.string(),
  }),
});

// 결제 취소 에러 응답 스키마
export const cancelPaymentErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().min(1, '에러 코드가 필요합니다'),
  message: z.string().min(1, '에러 메시지가 필요합니다'),
  details: z.unknown().optional(),
});

// 비즈니스 룰 에러 코드
export const PaymentErrorCodes = {
  ENROLLMENT_NOT_FOUND: 'ENROLLMENT_NOT_FOUND',
  ENROLLMENT_ALREADY_PROCESSED: 'ENROLLMENT_ALREADY_PROCESSED',
  PAYMENT_PROCESSING_FAILED: 'PAYMENT_PROCESSING_FAILED',
  INVALID_PAYMENT_AMOUNT: 'INVALID_PAYMENT_AMOUNT',
  AMOUNT_MISMATCH: 'AMOUNT_MISMATCH',
  PREPARE_NOT_FOUND: 'PREPARE_NOT_FOUND',
  FORBIDDEN: 'FORBIDDEN',
  TOSS_PAYMENT_ERROR: 'TOSS_PAYMENT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  PAYMENT_NOT_FOUND: 'PAYMENT_NOT_FOUND',
  PAYMENT_ALREADY_CANCELLED: 'PAYMENT_ALREADY_CANCELLED',
  PAYMENT_NOT_CANCELLABLE: 'PAYMENT_NOT_CANCELLABLE',
  CANCEL_AMOUNT_EXCEEDED: 'CANCEL_AMOUNT_EXCEEDED',
  CANCEL_AMOUNT_NOT_SUPPORTED: 'CANCEL_AMOUNT_NOT_SUPPORTED',
  TOSS_CANCEL_ERROR: 'TOSS_CANCEL_ERROR',
  CLASS_ALREADY_STARTED: 'CLASS_ALREADY_STARTED',
  REFUND_NOT_ALLOWED: 'REFUND_NOT_ALLOWED',
  CLASS_START_TIME_UNKNOWN: 'CLASS_START_TIME_UNKNOWN',
} as const;

// 취소 관련 타입 추출
export type CancelPaymentRequest = z.infer<typeof cancelPaymentRequestSchema>;
export type CancelPaymentSuccessResponse = z.infer<typeof cancelPaymentSuccessResponseSchema>;
export type CancelPaymentErrorResponse = z.infer<typeof cancelPaymentErrorResponseSchema>;
export type CancelPaymentResponse = CancelPaymentSuccessResponse | CancelPaymentErrorResponse;

export type PaymentErrorCode =
  (typeof PaymentErrorCodes)[keyof typeof PaymentErrorCodes];
