import { z } from 'zod';

/**
 * 파트너 수업 취소 관련 검증 스키마
 */

// 수업 취소 요청 스키마
export const cancelClassRequestSchema = z.object({
  cancelReason: z.string().min(1, '취소 사유를 입력해주세요').max(200, '취소 사유는 200자 이하로 입력해주세요').describe('수업 취소 사유')
});

// 환불 정보 스키마
export const refundInfoSchema = z.object({
  enrollmentId: z.uuid(),
  memberId: z.uuid(),
  paymentId: z.uuid(),
  refundAmount: z.number().int().positive(),
  status: z.string(),
});

// 수업 취소 성공 응답 스키마
export const cancelClassSuccessResponseSchema = z.object({
  success: z.literal(true),
  data: z.object({
    classId: z.uuid(),
    scheduleGroupId: z.number().int(),
    cancelledAt: z.string(),
    refundedCount: z.number().int(),
    totalRefundAmount: z.number().int(),
    refunds: z.array(refundInfoSchema)
  })
});

// 에러 응답 스키마
export const classCancellationErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  message: z.string(),
  details: z.unknown().optional()
});

// 타입 추출
export type CancelClassRequest = z.infer<typeof cancelClassRequestSchema>;
export type CancelClassSuccessResponse = z.infer<typeof cancelClassSuccessResponseSchema>;
export type ClassCancellationErrorResponse = z.infer<typeof classCancellationErrorResponseSchema>;
export type RefundInfo = z.infer<typeof refundInfoSchema>;

// 비즈니스 룰 에러 코드
export const ClassCancellationErrorCodes = {
  CLASS_NOT_FOUND: 'CLASS_NOT_FOUND',
  SCHEDULE_GROUP_NOT_FOUND: 'SCHEDULE_GROUP_NOT_FOUND',
  CLASS_DELETED: 'CLASS_DELETED',
  CLASS_NOT_ACTIVE: 'CLASS_NOT_ACTIVE',
  UNAUTHORIZED: 'UNAUTHORIZED',
  NOT_CONFIRMED: 'NOT_CONFIRMED',
  ALREADY_CANCELLED: 'ALREADY_CANCELLED',
  CLASS_ALREADY_STARTED: 'CLASS_ALREADY_STARTED',
  CANCELLATION_DEADLINE_PASSED: 'CANCELLATION_DEADLINE_PASSED',
  NO_ENROLLMENTS_TO_REFUND: 'NO_ENROLLMENTS_TO_REFUND',
  REFUND_FAILED: 'REFUND_FAILED',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;