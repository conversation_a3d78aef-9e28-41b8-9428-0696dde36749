import type { ReactNode } from 'react';
import { Ban, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { Button, buttonVariants } from '@/components/ui/button';
import type { VariantProps } from 'class-variance-authority';

type ButtonVariant = VariantProps<typeof buttonVariants>['variant'];
type ButtonSize = VariantProps<typeof buttonVariants>['size'];

type ErrorAction =
  | {
      kind: 'button';
      label: string;
      onClick: () => void;
      variant?: ButtonVariant;
      size?: ButtonSize;
    }
  | {
      kind: 'link';
      label: string;
      href: string;
      prefetch?: boolean;
      variant?: ButtonVariant;
      size?: ButtonSize;
    };

type Theme = 'user' | 'partner';

type ErrorPageProps = {
  title: string;
  message?: string;
  className?: string;
  theme?: Theme;
  actions?: ErrorAction[];
  children?: ReactNode;
};

export default function ErrorPage({
  title,
  message,
  className,
  theme = 'user',
  actions,
  children,
}: ErrorPageProps) {
  const isPartner = theme === 'partner';
  const Icon = isPartner ? AlertTriangle : Ban;

  const themeStyles = {
    container: isPartner
      ? 'border-orange-200 bg-orange-50/50'
      : 'border-gray-200 bg-white',
    iconBg: isPartner ? 'bg-orange-100' : 'bg-gray-100',
    iconColor: isPartner ? 'text-orange-600' : 'text-gray-500',
    titleColor: isPartner ? 'text-orange-900' : 'text-gray-900',
    messageColor: isPartner ? 'text-orange-700' : 'text-gray-500',
  };

  return (
    <div
      className={cn(
        'flex min-h-[50vh] items-center justify-center px-4',
        className
      )}
    >
      <div
        className={cn(
          'mx-auto w-full max-w-3xl rounded-xl border p-6 text-center shadow-sm',
          themeStyles.container
        )}
      >
        <div
          className={cn(
            'mx-auto mb-4 flex h-10 w-10 items-center justify-center rounded-full',
            themeStyles.iconBg
          )}
        >
          <Icon className={cn('h-5 w-5', themeStyles.iconColor)} />
        </div>
        <h1
          className={cn('mb-2 text-lg font-semibold', themeStyles.titleColor)}
        >
          {title}
        </h1>
        {message ? (
          <p className={cn('mb-4 text-sm', themeStyles.messageColor)}>
            {message}
          </p>
        ) : null}
        {actions && actions.length > 0 ? (
          <div className='mt-2 flex items-center justify-center gap-2'>
            {actions.map((action, idx) =>
              action.kind === 'button' ? (
                <Button
                  key={idx}
                  variant={action.variant}
                  size={action.size}
                  onClick={action.onClick}
                >
                  {action.label}
                </Button>
              ) : (
                <Button
                  key={idx}
                  asChild
                  variant={action.variant}
                  size={action.size}
                >
                  <Link href={action.href} prefetch={action.prefetch ?? false}>
                    {action.label}
                  </Link>
                </Button>
              )
            )}
          </div>
        ) : children ? (
          <div className='mt-2 flex items-center justify-center gap-2'>
            {children}
          </div>
        ) : null}
      </div>
    </div>
  );
}
