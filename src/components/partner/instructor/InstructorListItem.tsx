import { InstructorListItem } from '@/app/partner/_types/client.type';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { getInstructorProfileImageUrl } from '@/lib/utils/thumbnail';
import Link from 'next/link';

interface InstructorListItemProps {
  instructor: InstructorListItem;
  editHref?: string;
}

export default function InstructorListItemComponent({
  instructor,
  editHref,
}: InstructorListItemProps) {
  const getInitials = (name: string): string =>
    name.length > 1 ? name.slice(0, 2) : name;

  return (
    <div className='rounded-md border border-gray-200'>
      <div className='flex items-center justify-between p-2'>
        {/* 프로필 이미지 & 이름 */}
        <div className='flex items-center gap-4'>
          <Avatar className='h-12 w-12'>
            <AvatarImage
              src={getInstructorProfileImageUrl(instructor.profileImages, instructor.profileImageUrl)}
              alt={instructor.name}
            />
            <AvatarFallback className='text-sm font-medium'>
              {getInitials(instructor.name)}
            </AvatarFallback>
          </Avatar>
          <span className='text-base font-medium text-gray-900'>
            {instructor.name}
          </span>
        </div>

        {/* 수정 버튼 */}
        <div className='flex items-center'>
          {editHref ? (
            <Button asChild variant='ghost' size='sm' aria-label='강사 수정'>
              <Link href={editHref}>수정</Link>
            </Button>
          ) : null}
        </div>
      </div>
    </div>
  );
}
