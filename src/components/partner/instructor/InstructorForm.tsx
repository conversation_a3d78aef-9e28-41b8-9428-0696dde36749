import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Certificate,
  GENDER_OPTIONS,
  SPECIALTY_OPTIONS,
  Specialty,
} from '@/lib/schemas/instructor';
import { cn, uuid } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useState } from 'react';
import {
  Controller,
  SubmitHandler,
  useFieldArray,
  useForm,
} from 'react-hook-form';
import { PatternFormat } from 'react-number-format';
import {
  InstructorFormData,
  instructorFormSchema,
} from '@/app/partner/_schemas/form.schema';
import InstructorSpecialties from '@/app/partner/_components/InstructorSpecialties';
import InstructorCertifications from '@/app/partner/_components/InstructorCertifications';
import MultipleImageUpload from '@/app/partner/_components/MultipleImageUpload';
import { InstructorResponse } from '@/lib/api/partner/instructor.schema';

const genderValues = [
  { value: GENDER_OPTIONS.MALE, label: '남성' },
  { value: GENDER_OPTIONS.FEMALE, label: '여성' },
];

// 경력 옵션 (1년부터 20년까지)
const experienceOptions = Array.from({ length: 20 }, (_, i) => ({
  value: (i + 1).toString(),
  label: `${i + 1}년`,
}));

interface InstructorFormProps {
  mode: 'create' | 'edit';
  initialData?: InstructorResponse;
  onSubmit: (data: InstructorFormData) => Promise<void>;
  isLoading: boolean;
}

export default function InstructorForm({
  mode,
  initialData,
  onSubmit,
  isLoading,
}: InstructorFormProps) {
  // 초기 선택된 전문 분야 설정
  const [selectedSpecialties, setSelectedSpecialties] = useState<Specialty[]>(
    mode === 'edit' && initialData?.specialties ? initialData.specialties : []
  );

  const getDefaultValues = (): InstructorFormData => {
    if (mode === 'edit' && initialData) {
      return {
        name: initialData.name,
        gender: initialData.gender,
        contact: initialData.contact ?? undefined,
        description: initialData.description || '',
        links: {
          website: initialData.links?.website || undefined,
          sns: initialData.links?.sns || undefined,
        },
        experienceTotalYears: initialData.experienceTotalYears,
        specialties: initialData.specialties || [],
        certificates: initialData.certificates || [],
        profileImages:
          initialData.profileImages?.map(image => ({
            fileId: uuid(),
            url: image.url,
            path: image.path,
            file: undefined,
            ...(image.thumbnail && {
              thumbnail: {
                url: image.thumbnail.url,
                path: image.thumbnail.path,
              }
            }),
          })) || [],
      };
    }

    // create 모드의 기본값
    return {
      name: '',
      gender: GENDER_OPTIONS.MALE,
      contact: '',
      description: '',
      links: {
        website: undefined,
        sns: undefined,
      },
      experienceTotalYears: 1,
      specialties: [],
      certificates: [],
      profileImages: [],
    };
  };

  const form = useForm<InstructorFormData>({
    resolver: zodResolver(instructorFormSchema),
    defaultValues: getDefaultValues(),
  });

  const {
    fields: certificationFields,
    append: appendCertification,
    remove: removeCertification,
    update: updateCertification,
  } = useFieldArray({
    control: form.control,
    name: 'certificates',
  });

  const handleSubmit: SubmitHandler<InstructorFormData> = async data => {
    // 업로드/변환 중에는 제출 방지
    if (form.getValues('profileImages')?.some(img => !img.path || !img.url)) {
      // 검증 에러 메시지로 안내
      form.setError('profileImages', {
        type: 'validate',
        message: '이미지 업로드가 완료될 때까지 기다려주세요',
      });
      return;
    }
    await onSubmit(data);
  };

  // Specialty management
  const toggleSpecialty = useCallback(
    (specialty: string) => {
      const isSelected = selectedSpecialties.some(s => s.type === specialty);

      const newSpecialties = isSelected
        ? selectedSpecialties.filter(s => s.type !== specialty)
        : [
            ...selectedSpecialties,
            { type: specialty as (typeof SPECIALTY_OPTIONS)[number], years: 1 },
          ];

      setSelectedSpecialties(newSpecialties);

      // Update form value
      form.setValue('specialties', newSpecialties);
    },
    [form, selectedSpecialties]
  );

  const adjustExperience = useCallback(
    (specialty: string, adjustment: number) => {
      const currentSpecialties = form.getValues('specialties') || [];
      const updatedSpecialties = currentSpecialties.map(spec => {
        if (spec.type === specialty) {
          const newYears = Math.max(1, Math.min(20, spec.years + adjustment));
          return { ...spec, years: newYears };
        }
        return spec;
      });

      // Update both form value and local state
      const updatedSelectedSpecialties = selectedSpecialties.map(spec => {
        if (spec.type === specialty) {
          const newYears = Math.max(1, Math.min(20, spec.years + adjustment));
          return { ...spec, years: newYears };
        }
        return spec;
      });

      setSelectedSpecialties(updatedSelectedSpecialties);
      form.setValue('specialties', updatedSpecialties);
    },
    [form, selectedSpecialties]
  );

  // Certification management functions
  const handleAddCertification = useCallback(
    (certificationData: Certificate) => {
      appendCertification({
        ...certificationData,
      });
    },
    [appendCertification]
  );

  const handleUpdateCertification = useCallback(
    (id: string, certificationData: Certificate) => {
      const index = certificationFields.findIndex(cert => cert.id === id);
      if (index !== -1) {
        updateCertification(index, {
          ...certificationData,
        });
      }
    },
    [certificationFields, updateCertification]
  );

  const handleDeleteCertification = useCallback(
    (id: string) => {
      const index = certificationFields.findIndex(cert => cert.id === id);
      if (index !== -1) {
        removeCertification(index);
      }
    },
    [certificationFields, removeCertification]
  );

  const title = mode === 'create' ? '강사 등록' : '강사 정보 수정';
  const buttonText =
    mode === 'create' ? '강사 등록 완료' : '강사 정보 수정 완료';
  const loadingText = mode === 'create' ? '등록 중...' : '수정 중...';

  return (
    <div className='p-3'>
      <div className='mb-4'>
        <h1 className='text-foreground text-lg font-semibold'>{title}</h1>
      </div>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className='flex flex-col gap-6'
        >
          {/* 기본 정보 */}
          <div>
            <h2 className='mb-4 text-lg font-semibold'>기본 정보</h2>
            <div className='flex flex-col gap-4'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      강사 이름{' '}
                      <span className='text-primary font-bold'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='강사 이름을 입력해주세요'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='gender'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      성별 <span className='text-primary font-bold'>*</span>
                    </FormLabel>
                    <div className='flex gap-2'>
                      {genderValues.map(option => {
                        const isSelected = field.value === option.value;
                        return (
                          <button
                            key={option.value}
                            type='button'
                            onClick={() => field.onChange(option.value)}
                            className={cn(
                              'hover:border-primary hover:bg-primary/5 hover:text-primary flex-1 rounded-md border border-gray-400 p-2 text-gray-400 transition-all duration-200 ease-in-out hover:shadow-sm',
                              {
                                'border-primary bg-primary/20 text-primary hover:bg-primary/25':
                                  isSelected,
                              }
                            )}
                          >
                            {option.label}
                          </button>
                        );
                      })}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='contact'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      강사 연락처{' '}
                      <span className='text-xs text-gray-500'>(선택사항)</span>
                    </FormLabel>
                    <FormControl>
                      <PatternFormat
                        format='###-####-####'
                        placeholder='010-0000-0000'
                        value={field.value}
                        className='placeholder:text-placeholder'
                        onValueChange={values => {
                          field.onChange(values.formattedValue);
                        }}
                        customInput={Input}
                        patternChar='#'
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      강사 소개{' '}
                      <span className='text-primary font-bold'>*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='강사 경력, 전문성, 운동 철학 등을 소개해주세요.'
                        className='placeholder:text-placeholder min-h-[120px] resize-none'
                        {...field}
                      />
                    </FormControl>
                    <div className='text-primary rounded-md bg-blue-50 p-3 text-xs'>
                      <p className='mb-1 font-semibold'>작성 팁:</p>
                      <ul className='space-y-0.5'>
                        <li>• 운동 경력과 전문 분야를 구체적으로 기재하세요</li>
                        <li>• 수강생들에게 제공할 수 있는 가치를 어필하세요</li>
                        <li>• 운동에 대한 철학이나 접근 방식을 공유하세요</li>
                        <li>• 친근하고 전문적인 톤으로 작성하세요</li>
                      </ul>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='links.website'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      웹사이트 링크
                      <span className='text-xs text-gray-500'>(선택사항)</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder='웹사이트 URL (선택사항)' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='links.sns'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      SNS 링크
                      <span className='text-xs text-gray-500'>(선택사항)</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='인스타그램, 블로그 등 SNS 링크 (선택사항)'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 강사 대표 사진 */}
              <Controller
                control={form.control}
                name='profileImages'
                render={({ field }) => (
                  <MultipleImageUpload
                    type='instructor'
                    values={field.value || []}
                    onChange={imgs => field.onChange((imgs || []).slice(0, 1))}
                    maxImages={1}
                  />
                )}
              />
              {form.formState.errors.profileImages && (
                <p className='text-destructive mt-1 text-xs font-semibold'>
                  {form.formState.errors.profileImages.message}
                </p>
              )}
            </div>
          </div>

          {/* 경력 사항 */}
          <div>
            <h2 className='mb-4 text-lg font-semibold'>경력 사항</h2>
            <div className='flex flex-col gap-4'>
              <FormField
                control={form.control}
                name='experienceTotalYears'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      총 경력 <span className='text-primary font-bold'>*</span>
                    </FormLabel>
                    <Select
                      onValueChange={value => field.onChange(parseInt(value))}
                      value={field.value?.toString()}
                    >
                      <FormControl>
                        <SelectTrigger className='w-full'>
                          <SelectValue placeholder='총 경력을 선택해주세요' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {experienceOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* <InstructorSpecialties
                selectedSpecialties={selectedSpecialties}
                onSpecialtyToggle={toggleSpecialty}
                onExperienceAdjust={adjustExperience}
                error={form.formState.errors.specialties?.message}
                disabled={isLoading}
              /> */}
            </div>
          </div>

          {/* 자격 사항 */}
          <InstructorCertifications
            certifications={certificationFields}
            onAddCertification={handleAddCertification}
            onUpdateCertification={handleUpdateCertification}
            onDeleteCertification={handleDeleteCertification}
            disabled={isLoading}
          />

          {/* 제출 버튼 */}
          <div className='pt-6'>
            <Button
              type='submit'
              className='h-12 w-full text-base font-medium'
              size='lg'
              disabled={isLoading}
            >
              {isLoading ? loadingText : buttonText}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
