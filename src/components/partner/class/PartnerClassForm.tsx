'use client';
import { ClassScheduleSelector } from '@/app/partner/(dashboard)/classes/new/_components/ClassScheduleSelector';
import { SessionDurationMinutesSelector } from '@/app/partner/(dashboard)/classes/new/_components/SessionDurationMinutesSelector';
import { SpecialtyLevelSelector } from '@/app/partner/(dashboard)/classes/new/_components/SpecialtyLevelSelector';
import { StudioInstructorSelector } from '@/app/partner/(dashboard)/classes/new/_components/StudioInstructorSelector';
import { TargetAudienceSelector } from '@/app/partner/(dashboard)/classes/new/_components/TargetAudienceSelector';
import { ScheduleGroup } from '@/app/partner/(dashboard)/classes/new/_components/types';
import MultipleImageUpload from '@/app/partner/_components/MultipleImageUpload';
import { FormImage } from '@/app/partner/_schemas/form.schema';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { classApi } from '@/lib/api/partner/class.api';
import {
  CreateClassRequest,
  createClassRequestSchema,
  GetClassResponse,
} from '@/lib/api/partner/class.schema';
import { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { uuid } from '@/lib/utils';
import { CreateClassInput } from '@/lib/validations/partner-class.validation';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const capacityOptions = [
  { value: '1', label: '1명' },
  { value: '2', label: '2명' },
  { value: '3', label: '3명' },
  { value: '4', label: '4명' },
  { value: '5', label: '5명' },
  { value: '6', label: '6명' },
  { value: '7', label: '7명' },
  { value: '8', label: '8명' },
  { value: '9', label: '9명' },
  { value: '10', label: '10명' },
];

const weeklyFrequencyOptions = [
  { value: '1', label: '주 1회' },
  { value: '2', label: '주 2회' },
  { value: '3', label: '주 3회' },
];

interface PartnerClassFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: GetClassResponse;
  classId?: string;
  instructors?: GetInstructorsResponse;
  studioId: string;
  studioName: string;
  instructorName?: string;
}

export default function PartnerClassForm({
  mode,
  initialData,
  classId,
  instructors = [],
  studioId,
  studioName,
  instructorName,
}: PartnerClassFormProps) {
  const router = useRouter();
  const [uploadedImages, setUploadedImages] = useState<FormImage[]>(() =>
    initialData?.images
      ? initialData.images.map(image => ({
          fileId: uuid(),
          url: image.url,
          path: image.path,
          file: undefined,
        }))
      : []
  );

  const hasActiveEnrollments =
    mode === 'edit' && (initialData?.enrollmentCount ?? 0) > 0;

  const form = useForm<CreateClassRequest>({
    resolver: zodResolver(createClassRequestSchema),
    defaultValues:
      mode !== 'create' && initialData
        ? {
            studioId: initialData.studioId,
            instructorId: initialData.instructorId,
            title: initialData.title,
            description: initialData.description,
            category: initialData.category,
            level: initialData.level as
              | 'beginner'
              | 'intermediate'
              | 'advanced',
            target: initialData.target as 'women_only' | 'men_only' | 'mixed',
            maxParticipants: initialData.maxParticipants,
            sessionsPerWeek: initialData.sessionsPerWeek,
            pricePerSession: initialData.pricePerSession,
            sessionDurationMinutes: initialData.sessionDurationMinutes,
            durationWeeks: initialData.durationWeeks,
            scheduleGroups: initialData.scheduleGroups.map(group => ({
              schedules: group.schedules.map(schedule => ({
                ...schedule,
                dayOfWeek: schedule.dayOfWeek as
                  | 'sun'
                  | 'mon'
                  | 'tue'
                  | 'wed'
                  | 'thu'
                  | 'fri'
                  | 'sat',
              })),
            })),
          }
        : {
            studioId: studioId || undefined,
            instructorId: '',
            title: '',
            description: '',
            category: 'yoga',
            level: 'beginner',
            target: 'mixed',
            maxParticipants: 4,
            sessionsPerWeek: 2,
            pricePerSession: 20000,
            sessionDurationMinutes: 60,
            durationWeeks: 4,
            scheduleGroups: [],
          },
  });

  const weeklyFrequency = form.watch('sessionsPerWeek');
  const duration = form.watch('sessionDurationMinutes');
  const scheduleGroups = form.watch('scheduleGroups');

  const handleScheduleGroupsChange = (newScheduleGroups: ScheduleGroup[]) => {
    console.log('newScheduleGroups', newScheduleGroups);
    form.setValue('scheduleGroups', newScheduleGroups);
  };

  const onSubmit = async (data: CreateClassRequest) => {
    console.log('data', data);
    console.log('scheduleGroups (watch):', scheduleGroups);
    console.log('data.scheduleGroups:', data.scheduleGroups);
    try {
      // 전체 스케줄 개수 계산
      const totalScheduleCount = data.scheduleGroups.reduce(
        (acc, group) => acc + group.schedules.length,
        0
      );

      // 이미 업로드된 이미지들에서 url과 path가 있는 것만 사용
      const images = uploadedImages
        .filter(img => img.url && img.path)
        .map(img => ({
          url: img.url!,
          path: img.path!,
        }));

      const payload: CreateClassInput = {
        ...data,
        images,
        scheduleGroups: data.scheduleGroups,
        pricePerSession: Number(data.pricePerSession),
        maxParticipants: Number(data.maxParticipants),
        sessionDurationMinutes: Number(data.sessionDurationMinutes),
        durationWeeks: Number(data.durationWeeks),
        sessionsPerWeek: Number(data.sessionsPerWeek),
      };

      if (mode === 'create') {
        await classApi.createClass(payload);
        toast.success('클래스가 성공적으로 등록되었습니다!');
        router.push('/partner/classes');
      } else if (mode === 'edit' && classId) {
        await classApi.updateClass(classId, payload);
        toast.success('클래스가 성공적으로 수정되었습니다!');
        router.push(`/partner/classes/${classId}`);
      }
    } catch (error) {
      console.error('클래스 처리 실패:', error);
      toast.error(
        mode === 'create'
          ? '클래스 등록에 실패했습니다.'
          : '클래스 수정에 실패했습니다.'
      );
    }
  };

  const isReadOnly = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  // 필드 비활성화 조건: 읽기 모드이거나, 수정 모드에서 수강생이 있는 경우
  const isFieldDisabled = isReadOnly || hasActiveEnrollments;

  return (
    <div className='p-3'>
      <div className='mb-4'>
        <h1 className='text-foreground text-lg font-semibold'>
          {isCreateMode ? '수업 등록' : isEditMode ? '수업 수정' : '수업 상세'}
        </h1>
      </div>

      {/* 수강생이 있을 때 안내 메시지 */}
      {hasActiveEnrollments && (
        <div className='mb-4 rounded-lg border border-orange-200 bg-orange-50 p-4'>
          <div className='flex items-start'>
            <div className='flex-shrink-0'>
              <svg
                className='h-5 w-5 text-orange-400'
                viewBox='0 0 20 20'
                fill='currentColor'
              >
                <path
                  fillRule='evenodd'
                  d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z'
                  clipRule='evenodd'
                />
              </svg>
            </div>
            <div className='ml-3'>
              <h3 className='text-sm font-medium text-orange-800'>
                수강생이 있어 일부 정보만 수정 가능합니다
              </h3>
              <p className='mt-1 text-sm text-orange-700'>
                현재 {initialData?.enrollmentCount}명이 수강 중입니다. 스케줄
                추가만 가능하며, 기본 정보는 수정할 수 없습니다.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className=''>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit, error => {
              console.error('handleSubmit error', error);
            })}
            className='flex flex-col gap-6'
          >
            {/* 센터/강사 */}
            {isReadOnly ? (
              <div className=''>
                <h2 className='mb-4 text-base font-semibold'>센터/강사</h2>
                <div className='flex flex-col gap-4'>
                  <div>
                    <div className='text-sm font-medium'>센터</div>
                    <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                      {studioName || '정보 없음'}
                    </div>
                  </div>
                  <div>
                    <div className='text-sm font-medium'>강사</div>
                    <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                      {instructorName ||
                        initialData?.instructor?.name ||
                        '정보 없음'}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <StudioInstructorSelector
                control={form.control}
                studioName={studioName}
                instructors={instructors}
                disabled={isFieldDisabled}
              />
            )}

            {/* 기본 정보 */}
            <div className=''>
              <h2 className='mb-4 text-base font-semibold'>기본 정보</h2>
              <div className='flex flex-col gap-4'>
                {/* 제목 */}
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 제목{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        {isFieldDisabled ? (
                          <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                            {field.value || '정보 없음'}
                          </div>
                        ) : (
                          <Input
                            placeholder='예: 초보자를 위한 하타 요가'
                            {...field}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 상세 소개 */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 상세 소개{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        {isFieldDisabled ? (
                          <div className='border-input bg-muted min-h-[100px] rounded-md border px-3 py-2'>
                            {field.value || '정보 없음'}
                          </div>
                        ) : (
                          <Textarea
                            placeholder='수업의 특징, 커리큘럼, 수강생에게 도움이 되는 점 등을 설명해주세요.'
                            className='min-h-[100px]'
                            {...field}
                          />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 운동 대표 사진 */}
                <MultipleImageUpload
                  values={uploadedImages}
                  onChange={isFieldDisabled ? () => {} : setUploadedImages}
                  type='class'
                  maxImages={5}
                />

                <SpecialtyLevelSelector
                  control={form.control}
                  disabled={isFieldDisabled}
                />

                <div className='flex flex-col gap-4'>
                  <TargetAudienceSelector
                    control={form.control}
                    disabled={isFieldDisabled}
                  />

                  <FormField
                    control={form.control}
                    name='maxParticipants'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          수업 정원{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        {isFieldDisabled ? (
                          <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                            {field.value}명
                          </div>
                        ) : (
                          <Select
                            defaultValue={String(field.value)}
                            onValueChange={value => {
                              field.onChange(Number(value));
                            }}
                            disabled={isFieldDisabled}
                          >
                            <FormControl>
                              <SelectTrigger className='w-full'>
                                <SelectValue placeholder='수업 정원' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {capacityOptions.map(option => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <label className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
                    수업 기간 <span className='text-primary font-bold'>*</span>
                  </label>
                  {isFieldDisabled ? (
                    <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                      1개월
                    </div>
                  ) : (
                    <Select disabled={true} defaultValue='4'>
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='수업 기간' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='4'>1개월</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>

                <div className='flex flex-col gap-4'>
                  <FormField
                    control={form.control}
                    name='sessionsPerWeek'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          주 운동 횟수{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        {isFieldDisabled ? (
                          <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                            주 {field.value}회
                          </div>
                        ) : (
                          <Select
                            onValueChange={value => {
                              field.onChange(Number(value));
                            }}
                            defaultValue={String(field.value)}
                            disabled={isFieldDisabled}
                          >
                            <FormControl>
                              <SelectTrigger className='w-full'>
                                <SelectValue placeholder='주 운동 횟수' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {weeklyFrequencyOptions.map(option => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* 수업 시간 */}
                  <SessionDurationMinutesSelector
                    control={form.control}
                    disabled={isFieldDisabled}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='pricePerSession'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        회당 수강료{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        {isFieldDisabled ? (
                          <div className='border-input bg-muted min-h-[2.5rem] rounded-md border px-3 py-2'>
                            {field.value?.toLocaleString()}원
                          </div>
                        ) : (
                          <div className='relative'>
                            <Input
                              type='number'
                              placeholder='20000'
                              {...field}
                              onChange={e => {
                                const value = e.target.value;
                                field.onChange(
                                  value === '' ? '' : parseInt(value, 10) || 0
                                );
                              }}
                            />
                            <span className='text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm'>
                              원
                            </span>
                          </div>
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 수업 시간 개설 */}
            <ClassScheduleSelector
              setValue={form.setValue}
              weeklyFrequency={weeklyFrequency || 0}
              duration={duration || 0}
              onScheduleGroupsChange={handleScheduleGroupsChange}
              disabled={isReadOnly}
              initialScheduleGroups={scheduleGroups}
            />

            {/* 버튼 영역 */}
            {!isReadOnly && (
              <div className='pt-6'>
                <Button
                  type='submit'
                  className='h-12 w-full text-base font-medium'
                  size='lg'
                >
                  {isCreateMode ? '수업 개설 등록' : '수업 수정'}
                </Button>
              </div>
            )}

            {/* 읽기 모드에서는 수정 버튼 */}
            {isReadOnly && classId && (
              <div className='flex gap-3 pt-6'>
                <Button
                  type='button'
                  variant='outline'
                  className='h-12 flex-1 text-base font-medium'
                  size='lg'
                  onClick={() => router.push('/partner/classes')}
                >
                  목록으로
                </Button>
                <Button
                  type='button'
                  className='h-12 flex-1 text-base font-medium'
                  size='lg'
                  onClick={() =>
                    router.push(`/partner/classes/${classId}/edit`)
                  }
                >
                  수정하기
                </Button>
              </div>
            )}
          </form>
        </Form>
      </div>
      {!isReadOnly && <DevTool control={form.control} placement='top-right' />}
    </div>
  );
}
