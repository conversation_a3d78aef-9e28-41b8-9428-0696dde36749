'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useUIStore } from '@/contexts/ui.store';

export function LogoutDialog() {
  const open = useUIStore(state => state.openMap.LogoutDialog);
  const setOpen = useUIStore(state => state.setOpen);

  return (
    <AlertDialog open={open} onOpenChange={o => setOpen('LogoutDialog', o)}>
      <AlertDialogContent>
        <AlertDialogHeader className='text-left'>
          <AlertDialogTitle>로그아웃</AlertDialogTitle>
          <AlertDialogDescription>로그아웃 하시겠어요?</AlertDialogDescription>
        </AlertDialogHeader>
        <div className='flex flex-col gap-2'>
          <AlertDialogAction
            className='w-full'
            onClick={async () => {
              const { signOut } = await import('@/lib/supabase/auth');
              await signOut();
            }}
          >
            로그아웃
          </AlertDialogAction>
          <AlertDialogCancel className='w-full bg-gray-400 text-white hover:bg-gray-400/90'>
            닫기
          </AlertDialogCancel>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
