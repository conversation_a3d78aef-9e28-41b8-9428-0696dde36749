'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';

export interface TermsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: 'PARTNER_AGREEMENT' | 'PRIVACY_POLICY' | 'THIRD_PARTY_PROVISION' | 'MARKETING_AGREEMENT';
  onAgree?: () => void;
}

const TERMS_CONTENT = {
  PARTNER_AGREEMENT: {
    title: '쉘위 파트너 이용약관',
    content: `
      <div class="space-y-6">
        <div class="text-sm text-gray-600">
          <p><strong>시행일자: 2025년 8월 18일</strong></p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제1장 총칙</h3>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제1조 (목적)</h4>
          <p class="mb-4">본 약관은 주식회사 팀오버플로잉(이하 "회사")이 제공하는 피트니스 그룹 수업 중개 플랫폼 <strong>쉘위</strong>(이하 "서비스")를 이용하는 파트너와 회사 간의 권리·의무·책임을 규정함을 목적으로 합니다.</p>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제2조 (정의)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>"파트너"란 서비스에 수업을 등록·관리하고 회원에게 프로그램을 제공하는 피트니스 센터 또는 개인 트레이너를 말합니다.</li>
            <li>"회원"이란 수업 참가를 목적으로 서비스에 가입한 이용자를 말합니다.</li>
            <li>"수업"이란 파트너가 서비스에 등록한 소개·시간·장소·정원 등에 따라 운영되는 프로그램을 말합니다.</li>
            <li>"예약금"이란 회원이 서비스 내에서 결제하는 <strong>중개 수수료</strong>를 말합니다. (수업 잔액은 센터 현장 결제)</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제3조 (약관의 게시 및 개정)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>본 약관은 회사 웹사이트 및 파트너 가입 절차에서 확인 가능하도록 게시합니다.</li>
            <li>회사는 관련 법령 범위 내에서 약관을 개정할 수 있으며, <strong>변경사항과 적용일을 적용 7일 전</strong> 파트너에게 서면(전자문서 포함)으로 고지합니다.</li>
            <li>파트너가 개정에 동의하지 않을 경우 제2장 제6조에 따라 계약을 해지할 수 있습니다.</li>
            <li><strong>수수료율 등 요율 변경은 적용 30일 전 사전 고지</strong>합니다.</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제2장 계약 체결 및 파트너 자격</h3>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제4조 (제휴 신청 및 승인)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너는 회사가 정한 절차 및 서류를 제출하여 제휴를 신청합니다.</li>
            <li>센터형 파트너는 <strong>사업자등록증을 필수 제출</strong>해야 하며, 회사는 추가 서류 또는 인터뷰를 요청할 수 있습니다.</li>
            <li>회사는 적합성 심사 후 <strong>최대 15영업일 내</strong> 승인 여부를 통지합니다.</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제5조 (계약의 성립 및 효력)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>계약은 본 약관 동의 및 회사 승인으로 성립합니다.</li>
            <li>효력은 서비스 가입일부터 발생하며, 해지 시 종료됩니다.</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제6조 (계약 해지)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너는 <strong>해지일 7영업일 전</strong> 서면 통보로 계약을 해지할 수 있습니다.</li>
            <li>해지 통보 시점에 <strong>진행 중 또는 확정 통보된 수업</strong>은 파트너가 공지된 내용과 일정에 따라 <strong>종료 시까지 운영할 책임</strong>을 집니다.</li>
            <li>파트너가 전항의 의무를 이행하지 않아 회원에게 손해가 발생한 경우, <strong>회원에 대한 환불·보상 책임은 파트너에게</strong> 있으며, 회사에 대해서는 <strong>회원이 납부한 예약금(중개 수수료) 범위 내</strong>에서 손해배상 책임을 집니다.</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제3장 파트너의 의무 및 책임</h3>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제7조 (일반 의무)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너는 관련 법령을 준수하고 회원의 건강·안전을 최우선으로 성실히 수업을 운영합니다.</li>
            <li>파트너는 시설 이용·환불 등 <strong>센터 회원 정책</strong>을 수업 시작 전 고지합니다.</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제8조 (운영 및 서비스 협조 의무)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너는 수업 등록 시 소개·정원·레벨·시간 등 정보를 사실에 근거해 정확히 기재·유지합니다.</li>
            <li>파트너는 <strong>정원 모집 완료 후 24시간 이내</strong> 회원에게 <strong>수업 개시 일정 또는 취소</strong>를 서비스 내 고지합니다.</li>
            <li>수업 운영 관련 민원 발생 시 파트너는 <strong>센터 정책에 따라 1차 대응</strong>하며, 필요 시 회사와 협조합니다.</li>
            <li>파트너는 회사가 제공하는 파트너 어드민을 통해 수업을 등록·관리하고 운영 가이드를 준수합니다.</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제12조 (회원정보 이용 제한)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너는 회사로부터 제공받은 회원 정보를 <strong>수업 운영 목적</strong>으로만 사용하고, 목적 달성 후 <strong>지체 없이 파기</strong>합니다.</li>
            <li>회원 정보의 <strong>마케팅 활용·제3자 제공·외부 거래 유도</strong> 등 목적 외 사용을 금지합니다.</li>
            <li>유출 등 사고 발생 시 파트너는 지체 없이 회사에 통지하고 관련 법령에 따른 조치를 이행합니다.</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제5장 결제·정산·환불</h3>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제19조 (결제 구조와 정책)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>회원은 서비스 내에서 <strong>예약금(중개 수수료)</strong>을 결제합니다.</li>
            <li>파트너는 수업비 중 예약금을 제외한 <strong>잔액을 현장</strong>에서 직접 수납합니다.</li>
            <li>수수료율은 서비스 운영정책 문서로 관리하며, 변경 시 <strong>적용 30일 전</strong> 서면 고지합니다.</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제21조 (파트너 사유 취소·대체 강사)</h4>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>강사 질병·부상, 시설 고장 등 불가피 사유 발생 시 파트너는 <strong>대체 강사 또는 대체 일정</strong>을 우선 제안할 수 있습니다.</li>
            <li>회원이 이를 원하지 않거나 대체가 불가한 경우 회사는 <strong>예약금 전액 환불</strong>합니다.</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제6장 비밀유지·위반·통지·기타</h3>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제25조 (위반 행위)</h4>
          <p class="mb-2">다음 행위가 확인될 경우 회사는 노출 제한·이용 제한 또는 계약 해지를 할 수 있습니다.</p>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>허위 정보 기재, 반복적 무단 취소·시간 변경</li>
            <li>서비스 외부 거래 유도·연락처 수집/활용</li>
            <li>후기 허위 작성·강요 등 부정 행위</li>
            <li><strong>회원정보 목적 외 사용·제3자 제공</strong></li>
            <li>기타 약관·운영정책 위반으로 회사·회원에 손해 우려가 있는 행위</li>
          </ul>
          
          <h4 class="text-md font-medium text-gray-800 mb-2">제28조 (분쟁 해결)</h4>
          <p class="mb-4">본 약관은 대한민국 법률에 따르며, 분쟁 발생 시 <strong>서울중앙지방법원</strong>을 제1심 전속 관할로 합니다.</p>
        </div>

        <div class="text-sm text-gray-600 pt-4 border-t">
          <p><strong>부칙</strong></p>
          <p>본 약관은 <strong>2025년 8월 18일</strong>부터 시행하며, 시행 이전 등록된 모든 파트너에도 동일 적용합니다.</p>
        </div>
      </div>
    `,
  },
  PRIVACY_POLICY: {
    title: '개인정보 수집 및 이용 동의서 (파트너용)',
    content: `
      <div class="space-y-5">
        <div class="text-sm text-gray-600">
          <p><strong>시행일자: 2025년 8월 18일</strong></p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제1조 (수집하는 개인정보 항목)</h3>
          <p class="mb-3">회사는 다음과 같은 정보를 수집합니다.</p>
          
          <ul class="list-disc pl-6 space-y-1 mb-4">
            <li><strong>필수 항목:</strong> 이름, 연락처(전화번호 및 이메일), 계좌정보, 소속 센터명, 센터 주소, 사업자등록 여부</li>
            <li><strong>선택 항목:</strong> 센터 소개 문구, 트레이너 프로필 사진, 자격증, 수업 이미지 등</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제2조 (개인정보 수집 및 이용 목적)</h3>
          <p class="mb-3">회사는 수집한 개인정보를 다음의 목적을 위해 이용합니다.</p>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>파트너 등록 및 자격 검토</li>
            <li>수업 운영 및 관리 (수업 등록/수정/확정 등)</li>
            <li>고객 문의 응대 및 예약 알림</li>
            <li>고객 후기/피드백 대응</li>
            <li>계약 체결 및 관리, 법령상 의무 이행</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제3조 (보유 및 이용기간)</h3>
          <ul class="list-disc pl-6 space-y-2 mb-4">
            <li><strong>보유 기간:</strong> 파트너 탈퇴 또는 계약 해지 시까지</li>
            <li><strong>단,</strong> 관계 법령에 따라 일정 기간 보존이 필요한 경우 해당 기간 동안 보존됨</li>
          </ul>
          
          <div class="ml-6 text-sm text-gray-700">
            <p class="mb-2">전자상거래 등에서의 소비자 보호에 관한 법률에 따른 보존 예: 거래 기록 5년, 소비자 불만 또는 분쟁처리 기록 3년 등</p>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제4조 (동의 거부 권리 및 거부 시 불이익 안내)</h3>
          <p>정보주체는 개인정보 수집 및 이용에 대한 동의를 거부할 권리가 있습니다. 다만, 필수 항목 수집 및 이용에 대한 동의를 거부할 경우, 쉘위 파트너 등록 및 수업 운영이 불가능할 수 있습니다.</p>
        </div>
      </div>
    `,
  },
  THIRD_PARTY_PROVISION: {
    title: '쉘위 개인정보 제3자 제공 동의서',
    content: `
      <div class="space-y-5">
        <div class="text-sm text-gray-600">
          <p><strong>시행일자: 2025년 8월 18일</strong></p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제1조 (제공받는 자)</h3>
          <ul class="list-disc pl-6">
            <li>회원이 예약하거나 수업이 확정된 해당 파트너 센터 및 소속 트레이너</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제2조 (제공 항목)</h3>
          <ul class="list-disc pl-6 space-y-1">
            <li>회원명(또는 마스킹된 이름)</li>
            <li>성별</li>
            <li>연령대</li>
            <li>운동 목적 (예: 체중 감량, 체력 향상 등)</li>
            <li>희망 운동 수준 (예: 입문, 중급 등)</li>
            <li>기타 수업 운영 및 그룹 구성에 필요한 신청 정보</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제3조 (제공 목적)</h3>
          <ul class="list-disc pl-6 space-y-1">
            <li>그룹 수업 구성 및 준비</li>
            <li>회원 특성에 맞는 수업 진행</li>
            <li>맞춤 피드백 제공 및 수업 중 배려 요소 반영</li>
            <li>수업 출결 관리 및 운영 소통</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제4조 (보유 및 이용기간)</h3>
          <ul class="list-disc pl-6 space-y-1">
            <li>수업 종료일로부터 최대 30일까지 보유 후 즉시 파기</li>
            <li>단, 관련 법령에 따라 별도 보관이 필요한 경우 해당 법령 기준에 따름</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제5조 (동의 거부 권리 및 불이익 고지)</h3>
          <p>회원은 위 개인정보 제공에 대해 동의하지 않을 수 있으며, 이 경우 해당 수업 예약 또는 참여가 제한될 수 있습니다.</p>
        </div>
      </div>
    `,
  },
  MARKETING_AGREEMENT: {
    title: '쉘위 파트너 마케팅 정보 수신 동의 약관 (선택)',
    content: `
      <div class="space-y-5">
        <div class="text-sm text-gray-600">
          <p><strong>시행일자: 2025년 8월 18일</strong></p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제1조 (목적)</h3>
          <p class="mb-4">본 약관은 파트너가 쉘위(주식회사 팀오버플로잉) 서비스 이용 중 마케팅 정보 수신에 동의함에 따라, 당사가 파트너에게 제공하는 광고성 정보의 내용, 수신 방법, 수신 철회 절차 등을 명확히 안내하기 위함입니다.</p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제2조 (수집 및 이용 목적)</h3>
          <p class="mb-3">파트너의 수신 동의에 따라 아래와 같은 목적의 정보를 제공할 수 있습니다.</p>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>쉘위 서비스 운영 및 매출 증대에 도움이 되는 제안 제공</li>
            <li>쉘위 또는 제휴사의 파트너 대상 혜택·프로모션·이벤트 안내</li>
            <li>신규 기능, 서비스 정책 변경, 운영 팁 등 비즈니스 관련 정보 제공</li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제3조 (수신 방법 및 범위)</h3>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li><strong>수신 채널:</strong> 카카오 알림톡, 문자(SMS/MMS), 이메일, 앱 푸시</li>
            <li><strong>수신 빈도:</strong> 캠페인·혜택 제공 시 비정기 발송</li>
            <li><strong>주요 발송 항목</strong>
              <ul class="list-disc pl-6 mt-2 space-y-1">
                <li>파트너 전용 운영/매출 향상 캠페인 및 이벤트 안내</li>
                <li>신규 기능·서비스 업데이트 소식</li>
                <li>제휴 마케팅, 광고 집행 기회 안내</li>
                <li>교육/세미나·네트워킹 행사 초대</li>
              </ul>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제4조 (보유 및 이용 기간)</h3>
          <p class="mb-4">마케팅 정보 수신을 위한 개인정보(이메일, 전화번호 등)는 파트너가 수신 동의를 철회하거나 파트너 계정 해지 시까지 보유 및 이용됩니다. 단, 관련 법령에 따라 별도 보존이 필요한 경우에는 해당 법령에서 정한 기간 동안 보관합니다.</p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제5조 (동의 철회 및 설정 변경)</h3>
          <p class="mb-3">파트너는 마케팅 정보 수신 동의를 언제든 철회할 수 있으며, 아래 방법을 통해 즉시 수신 거부가 가능합니다.</p>
          <ul class="list-decimal pl-6 space-y-1 mb-4">
            <li>[파트너 어드민 > 알림 설정] 메뉴에서 ON/OFF 설정 변경</li>
            <li>수신된 메시지 내 "수신 거부" 버튼 또는 링크 클릭</li>
            <li>고객센터 요청 (<EMAIL> 또는 별도 안내 채널)</li>
          </ul>
          <p class="text-sm text-gray-600">※ 철회 즉시 해당 채널을 통한 광고성 정보 발송은 중단됩니다.</p>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">제6조 (유의사항)</h3>
          <ul class="list-decimal pl-6 space-y-1">
            <li>마케팅 정보 수신 동의는 파트너 서비스 이용에 필수적이지 않으며, 동의하지 않아도 기본 서비스 이용에는 제한이 없습니다.</li>
            <li>서비스 필수 알림(예약, 결제, 정산, 정책 변경 등)은 본 동의와 무관하게 발송되며, 알림 설정에서 비활성화할 수 없습니다.</li>
          </ul>
        </div>
      </div>
    `,
  },
};

export const TermsModal: React.FC<TermsModalProps> = ({
  open,
  onOpenChange,
  type,
  onAgree,
}) => {
  const termsData = TERMS_CONTENT[type];
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const isAtBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 10;
    
    if (isAtBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAgree = () => {
    onAgree?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>{termsData.title}</DialogTitle>
        </DialogHeader>
        
        <ScrollArea 
          className="h-96 pr-4" 
          onScrollCapture={handleScroll}
        >
          <div 
            className="prose prose-sm max-w-none text-gray-700"
            dangerouslySetInnerHTML={{ __html: termsData.content }}
          />
        </ScrollArea>
        
        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            취소
          </Button>
          <Button
            onClick={handleAgree}
            disabled={!hasScrolledToBottom}
            className="bg-gradient-to-r from-[#5000D0] to-[#8645EF] hover:from-[#5000D0]/80 hover:to-[#8645EF]/80"
          >
            {hasScrolledToBottom ? '동의' : '끝까지 읽어주세요'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TermsModal;