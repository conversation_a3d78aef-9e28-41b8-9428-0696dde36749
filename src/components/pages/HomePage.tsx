'use client';

import { ClassListResponse } from '@/app/api/classes/schema';
import ClassFilter, {
  type ClassFilters,
} from '@/components/shared/class/ClassFilter';
import ClassListItemComponent from '@/components/shared/class/ClassListItem';
import { Skeleton } from '@/components/ui/skeleton';
import { getActiveClasses } from '@/lib/api/classes/api';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import Footer from '../layout/Footer';
import KakaoInquiry from '../shared/KakaoInquiry';

interface HomePageContentProps {
  initialData: ClassListResponse;
}

// ClassListItem과 동일한 구조의 스켈레톤 컴포넌트
function ClassListItemSkeleton() {
  return (
    <div className='flex h-40'>
      {/* Image Section Skeleton */}
      <div className='relative w-40 flex-shrink-0'>
        <Skeleton className='h-full w-full' />
        {/* Badge Skeleton */}
        <div className='absolute top-0 left-0'>
          <Skeleton className='h-6 w-12 rounded-none' />
        </div>
      </div>

      {/* Content Section Skeleton */}
      <div className='flex flex-1 flex-col justify-between px-4'>
        {/* Top Section */}
        <div className='flex flex-col gap-2'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-5 w-16' />
            <Skeleton className='h-4 w-20' />
            <Skeleton className='h-4 w-16' />
          </div>

          {/* Title Skeleton */}
          <Skeleton className='h-6 w-3/4' />

          {/* Instructor Info Skeleton */}
          <Skeleton className='h-4 w-2/3' />
        </div>

        {/* Bottom Section - Price */}
        <div className='flex items-end justify-end gap-2'>
          <Skeleton className='h-4 w-8' />
          <Skeleton className='h-8 w-20' />
        </div>
      </div>
    </div>
  );
}

// Empty State 컴포넌트
function EmptyState({ hasFilters }: { hasFilters: boolean }) {
  return (
    <div className='flex flex-col items-center justify-center px-4 py-16'>
      <div className='space-y-4 text-center'>
        {/* 아이콘 */}
        <div className='mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-gray-100'>
          <svg
            className='h-12 w-12 text-gray-400'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={1.5}
              d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
            />
          </svg>
        </div>

        {/* 메시지 */}
        <div className='space-y-2'>
          <h3 className='text-lg font-semibold text-gray-900'>
            {hasFilters
              ? '검색 결과가 없습니다'
              : '아직 등록된 클래스가 없습니다'}
          </h3>
          <p className='mx-auto max-w-sm text-sm text-gray-500'>
            {hasFilters
              ? '다른 조건으로 검색해보시거나 필터를 초기화해보세요.'
              : '새로운 클래스가 등록되면 여기에 표시됩니다.'}
          </p>
        </div>

        {/* 액션 버튼 (필터가 있을 때만) */}
        {hasFilters && (
          <button
            onClick={() => window.location.reload()}
            className='bg-primary text-primary-foreground hover:bg-primary/90 mt-4 rounded-md px-4 py-2 text-sm transition-colors'
          >
            전체 클래스 보기
          </button>
        )}
      </div>
    </div>
  );
}

export default function HomePageContent({ initialData }: HomePageContentProps) {
  const [filters, setFilters] = useState<ClassFilters>({});
  const { data, isLoading, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useInfiniteQuery({
      queryKey: ['classes', filters],
      queryFn: ({ pageParam = 1 }) =>
        getActiveClasses({
          page: pageParam,
          limit: 20,
          filters,
        }),
      initialPageParam: 1,
      getNextPageParam: lastPage => {
        const { pagination } = lastPage;
        return pagination.page < pagination.totalPages
          ? pagination.page + 1
          : undefined;
      },
      initialData: Object.values(filters).every(
        filterArray => !filterArray || filterArray.length === 0
      )
        ? {
            pages: [initialData],
            pageParams: [1],
          }
        : undefined,
      enabled: true,
      throwOnError: true,
      staleTime: 0,
      gcTime: 0,
    });

  const handleScroll = useCallback(() => {
    if (
      window.innerHeight + document.documentElement.scrollTop >=
        document.documentElement.offsetHeight - 1000 &&
      hasNextPage &&
      !isFetchingNextPage
    ) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  const allClasses = data?.pages.flatMap(page => page.classes) ?? [];
  const hasFilters = Object.values(filters).some(
    filterArray => filterArray && filterArray.length > 0
  );
  const isEmpty = !isLoading && allClasses.length === 0;
  return (
    <>
      <div className='flex-1'>
        <div className='px-3 pt-3'>
          <ClassFilter filters={filters} onFiltersChange={setFilters} />
        </div>

        <section className='px-3 pt-4'>
          {isLoading ? (
            <div className='flex flex-col gap-4'>
              <ClassListItemSkeleton />
              <ClassListItemSkeleton />
              <ClassListItemSkeleton />
              <ClassListItemSkeleton />
              <ClassListItemSkeleton />
            </div>
          ) : isEmpty ? (
            <EmptyState hasFilters={hasFilters} />
          ) : (
            <div className='flex flex-col gap-4'>
              {allClasses.map(item => (
                <div
                  key={item.id}
                  className='border-b border-gray-200 pb-4 last:border-b-0'
                >
                  <ClassListItemComponent
                    classInfo={{
                      id: item.id,
                      title: item.title,
                      level: item.level,
                      category: item.category,
                      maxParticipants: item.maxParticipants,
                      pricePerSession: item.pricePerSession,
                      thumbnailUrl: item.thumbnailUrl,
                    }}
                    instructor={{
                      name: item.instructor.name,
                      experienceTotalYears:
                        item.instructor.experienceTotalYears,
                    }}
                    studio={{
                      nearestStation: item.studio.nearestStation ?? '',
                    }}
                  />
                </div>
              ))}

              {/* 더 로딩 인디케이터 - 스켈레톤 UI */}
              {isFetchingNextPage && (
                <div className='flex flex-col gap-4'>
                  <ClassListItemSkeleton />
                  <ClassListItemSkeleton />
                  <ClassListItemSkeleton />
                </div>
              )}
            </div>
          )}
        </section>
        <KakaoInquiry />
      </div>
      <Footer />
    </>
  );
}
