'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import {
  validateMemberRegister,
  type MemberRegisterRequest,
} from '@/schemas/member';

export function MemberRegisterForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirm, setShowPasswordConfirm] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState<MemberRegisterRequest>({
    email: '',
    password: '',
    passwordConfirm: '',
    nickname: '',
  });

  const handleInputChange = (
    field: keyof MemberRegisterRequest,
    value: string
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear errors when user types
    if (error) setError(null);
    if (success) setSuccess(null);
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isLoading) return;

    // 클라이언트 사이드 validation
    const validationResult = validateMemberRegister(formData);
    if (!validationResult.success) {
      const errors: Record<string, string> = {};
      validationResult.error.issues.forEach(err => {
        const field = err.path[0] as string;
        if (!errors[field]) {
          errors[field] = err.message;
        }
      });
      setFieldErrors(errors);

      // 첫 번째 에러를 메인 에러로 표시
      const firstError = validationResult.error.issues[0];
      setError(firstError.message);
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setFieldErrors({});

    try {
      const response = await fetch('/api/auth/member/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        // 회원가입 성공
        setSuccess('회원가입이 완료되었습니다! 로그인 페이지로 이동합니다...');

        // 2초 후 로그인 페이지로 이동
        setTimeout(() => {
          router.push('/login');
        }, 2000);
      } else {
        // 회원가입 실패
        if (data.errors && Array.isArray(data.errors)) {
          // 필드별 에러 처리
          const errors: Record<string, string> = {};
          data.errors.forEach((err: { field: string; message: string }) => {
            errors[err.field] = err.message;
          });
          setFieldErrors(errors);
        }

        setError(data.message || '회원가입에 실패했습니다.');
      }
    } catch (err) {
      console.error('회원가입 요청 오류:', err);
      setError('네트워크 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='w-full max-w-3xl space-y-6'>
      {/* 헤더 */}
      <div className='space-y-2 text-center'>
        <h2 className='text-2xl font-bold text-gray-900'>임시 계정 생성</h2>
        <p className='text-sm text-gray-600'>
          개발자/심사자 전용 임시 회원가입입니다
        </p>
      </div>

      {/* 성공 메시지 */}
      {success && (
        <Alert className='border-green-200 bg-green-50'>
          <CheckCircle className='h-4 w-4 text-green-600' />
          <AlertDescription className='text-green-700'>
            {success}
          </AlertDescription>
        </Alert>
      )}

      {/* 에러 메시지 */}
      {error && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 회원가입 폼 */}
      <form onSubmit={handleSubmit} className='space-y-4'>
        {/* 이메일 */}
        <div className='space-y-2'>
          <Label htmlFor='email'>이메일</Label>
          <Input
            id='email'
            type='email'
            placeholder='<EMAIL>'
            value={formData.email}
            onChange={e => handleInputChange('email', e.target.value)}
            disabled={isLoading || !!success}
            required
            className={fieldErrors.email ? 'border-red-500' : ''}
          />
          {fieldErrors.email && (
            <p className='text-sm text-red-600'>{fieldErrors.email}</p>
          )}
        </div>

        {/* 닉네임 */}
        <div className='space-y-2'>
          <Label htmlFor='nickname'>닉네임</Label>
          <Input
            id='nickname'
            type='text'
            placeholder='심사자123'
            value={formData.nickname}
            onChange={e => handleInputChange('nickname', e.target.value)}
            disabled={isLoading || !!success}
            required
            className={fieldErrors.nickname ? 'border-red-500' : ''}
          />
          {fieldErrors.nickname && (
            <p className='text-sm text-red-600'>{fieldErrors.nickname}</p>
          )}
          <p className='text-xs text-gray-500'>한글, 영문, 숫자 2-20자</p>
        </div>

        {/* 비밀번호 */}
        <div className='space-y-2'>
          <Label htmlFor='password'>비밀번호</Label>
          <div className='relative'>
            <Input
              id='password'
              type={showPassword ? 'text' : 'password'}
              placeholder='비밀번호를 입력하세요'
              value={formData.password}
              onChange={e => handleInputChange('password', e.target.value)}
              disabled={isLoading || !!success}
              required
              className={fieldErrors.password ? 'border-red-500' : ''}
            />
            <Button
              type='button'
              variant='ghost'
              size='sm'
              className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading || !!success}
            >
              {showPassword ? (
                <EyeOff className='h-4 w-4 text-gray-400' />
              ) : (
                <Eye className='h-4 w-4 text-gray-400' />
              )}
            </Button>
          </div>
          {fieldErrors.password && (
            <p className='text-sm text-red-600'>{fieldErrors.password}</p>
          )}
          <p className='text-xs text-gray-500'>
            8자 이상, 영문+숫자+특수문자 포함
          </p>
        </div>

        {/* 비밀번호 확인 */}
        <div className='space-y-2'>
          <Label htmlFor='passwordConfirm'>비밀번호 확인</Label>
          <div className='relative'>
            <Input
              id='passwordConfirm'
              type={showPasswordConfirm ? 'text' : 'password'}
              placeholder='비밀번호를 다시 입력하세요'
              value={formData.passwordConfirm}
              onChange={e =>
                handleInputChange('passwordConfirm', e.target.value)
              }
              disabled={isLoading || !!success}
              required
              className={fieldErrors.passwordConfirm ? 'border-red-500' : ''}
            />
            <Button
              type='button'
              variant='ghost'
              size='sm'
              className='absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent'
              onClick={() => setShowPasswordConfirm(!showPasswordConfirm)}
              disabled={isLoading || !!success}
            >
              {showPasswordConfirm ? (
                <EyeOff className='h-4 w-4 text-gray-400' />
              ) : (
                <Eye className='h-4 w-4 text-gray-400' />
              )}
            </Button>
          </div>
          {fieldErrors.passwordConfirm && (
            <p className='text-sm text-red-600'>
              {fieldErrors.passwordConfirm}
            </p>
          )}
        </div>

        {/* 회원가입 버튼 */}
        <Button
          type='submit'
          className='w-full'
          disabled={isLoading || !!success}
        >
          {isLoading ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              계정 생성 중...
            </>
          ) : success ? (
            '계정 생성 완료'
          ) : (
            '계정 생성'
          )}
        </Button>
      </form>

      {/* 로그인 링크 */}
      {!success && (
        <div className='text-center text-sm'>
          <span className='text-gray-600'>이미 계정이 있으신가요? </span>
          <Link
            href='/login'
            className='font-medium text-blue-600 transition-colors hover:text-blue-500'
          >
            로그인
          </Link>
        </div>
      )}

      {/* 개발 안내 */}
      <div className='border-t pt-4'>
        <p className='text-center text-xs text-gray-500'>
          ⚠️ 임시 기능: 일반 사용자는 카카오 로그인을 이용해주세요
        </p>
      </div>
    </div>
  );
}
