'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Loader2, AlertCircle } from 'lucide-react';
import { validateMemberLogin, type MemberLoginRequest } from '@/schemas/member';

interface MemberLoginFormProps {
  redirectTo?: string;
  onSuccess?: () => void;
}

export function MemberLoginForm({ redirectTo = '/', onSuccess }: MemberLoginFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<MemberLoginRequest>({
    email: '',
    password: '',
  });

  const handleInputChange = (field: keyof MemberLoginRequest, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(null); // Clear error when user types
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLoading) return;

    // 클라이언트 사이드 validation
    const validationResult = validateMemberLogin(formData);
    if (!validationResult.success) {
      const firstError = validationResult.error.issues[0];
      setError(firstError.message);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/member/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        // 로그인 성공
        if (onSuccess) {
          onSuccess();
        } else {
          // 페이지 리디렉션
          router.push(redirectTo);
          router.refresh(); // 서버 컴포넌트 새로고침
        }
      } else {
        // 로그인 실패
        setError(data.message || '로그인에 실패했습니다.');
      }
    } catch (err) {
      console.error('로그인 요청 오류:', err);
      setError('네트워크 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full space-y-4">{/* 헤더 제거 - 부모 컴포넌트에서 표시 */}

      {/* 에러 메시지 */}
      {error && (
        <Alert variant="destructive" className="text-sm">
          <AlertCircle className="h-3 w-3" />
          <AlertDescription className="text-xs">{error}</AlertDescription>
        </Alert>
      )}

      {/* 로그인 폼 */}
      <form onSubmit={handleSubmit} className="space-y-3">
        {/* 이메일 */}
        <div className="space-y-1">
          <Label htmlFor="member-email" className="text-xs">이메일</Label>
          <Input
            id="member-email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            disabled={isLoading}
            required
            className="h-8 text-sm"
          />
        </div>

        {/* 비밀번호 */}
        <div className="space-y-1">
          <Label htmlFor="member-password" className="text-xs">비밀번호</Label>
          <div className="relative">
            <Input
              id="member-password"
              type={showPassword ? 'text' : 'password'}
              placeholder="비밀번호를 입력하세요"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              disabled={isLoading}
              required
              className="h-8 text-sm pr-8"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-8 w-8 p-0 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? (
                <EyeOff className="h-3 w-3 text-gray-400" />
              ) : (
                <Eye className="h-3 w-3 text-gray-400" />
              )}
            </Button>
          </div>
        </div>

        {/* 로그인 버튼 */}
        <Button
          type="submit"
          className="w-full h-8 text-sm"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
              로그인 중...
            </>
          ) : (
            '로그인'
          )}
        </Button>
      </form>

      {/* 회원가입 링크 */}
      <div className="text-center">
        <span className="text-xs text-gray-600">계정이 없으신가요? </span>
        <Link 
          href="/register"
          className="text-xs text-blue-600 hover:text-blue-500 font-medium transition-colors"
        >
          회원가입
        </Link>
      </div>
    </div>
  );
}