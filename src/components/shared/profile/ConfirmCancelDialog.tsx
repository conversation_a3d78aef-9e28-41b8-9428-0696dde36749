import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface ConfirmCancelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isSubmitting?: boolean;
  error?: string | null;
  onConfirm: () => void;
}

export function ConfirmCancelDialog({
  open,
  onOpenChange,
  isSubmitting = false,
  error,
  onConfirm,
}: ConfirmCancelDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>예약을 취소하시겠어요?</AlertDialogTitle>
          <AlertDialogDescription>
            취소 시 결제는 환불 처리되며, 진행 중인 예약은 더 이상 유지되지
            않습니다.
          </AlertDialogDescription>
        </AlertDialogHeader>
        {error ? <p className='text-sm text-red-600'>{error}</p> : null}
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isSubmitting}>닫기</AlertDialogCancel>
          <AlertDialogAction onClick={onConfirm} disabled={isSubmitting}>
            {isSubmitting ? '취소 중...' : '예, 취소합니다'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
