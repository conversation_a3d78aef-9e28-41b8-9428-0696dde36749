import { serverApiFetch } from '@/lib/api/server-fetch.server';
import type { UserStats as UserStatsType } from '@/schemas/profile';
import { UserStats } from './UserStats';

export default async function UserStatsServer() {
  const profile = await serverApiFetch<UserStatsType>('/api/profile');
  const userForStats = {
    nickname: profile.nickname,
    totalClasses: profile.totalClassDays,
    attendedClasses: profile.attendedDays,
    nextClasses: profile.nextClasses.map(nc => ({
      title: nc.title,
      date: nc.date,
      time: nc.time,
    })),
  };

  return <UserStats stats={userForStats} />;
}
