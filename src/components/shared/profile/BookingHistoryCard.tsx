import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { MapPin, Star, Users } from 'lucide-react';

import { mapLevelToLabel } from '@/lib/utils/format';
import type { Booking } from '@/schemas/profile';

interface BookingHistoryCardProps {
  booking: Booking;
  onPaymentClick?: (booking: Booking) => void;
  onCancelBooking?: (bookingId: string) => void;
  onViewDetails?: (bookingId: string) => void;
  className?: string;
  optimisticCancelled?: boolean;
}

export function BookingHistoryCard({
  booking,
  onCancelBooking,
  onViewDetails,
  optimisticCancelled,
}: BookingHistoryCardProps) {
  // 결제 관련 버튼은 현재 요구 사항에서 제외되어 사용하지 않습니다.

  const renderStatusBadge = (status: Booking['status']) => {
    const commonClass = 'rounded-full px-7 text-white font-semibold';
    switch (status) {
      case 'payment_pending':
        return (
          <Badge
            variant='default'
            className={cn(
              commonClass,
              'bg-primary text-primary-foreground hover:bg-primary/90'
            )}
          >
            결제 대기
          </Badge>
        );
      case 'recruiting':
        return (
          <Badge
            variant='secondary'
            className={cn(commonClass, 'bg-[#A87EE3]')}
          >
            모집중
          </Badge>
        );
      case 'reserved':
        return (
          <Badge
            className={cn(
              commonClass,
              'border-green-200 bg-green-100 text-green-800'
            )}
          >
            예약 완료
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge className={cn(commonClass, 'bg-primary-dark')}>진행 중</Badge>
        );
      case 'completed':
        return <Badge className={cn(commonClass, 'bg-black')}>완료</Badge>;
      case 'cancelled':
        return (
          <Badge
            className={cn(
              commonClass,
              'border-red-200 bg-red-100 text-red-800'
            )}
          >
            취소
          </Badge>
        );
      default:
        return null;
    }
  };

  const renderActionButtons = (
    status: Booking['status'],
    isOptimisticallyCancelled: boolean
  ) => {
    if (isOptimisticallyCancelled) {
      return (
        <div className='mt-2 flex gap-3'>
          <Button variant='outline' className='flex-1 bg-white' disabled>
            예약 취소
          </Button>
          <Button className='bg-primary-dark flex-1 text-white' disabled>
            수업 상세
          </Button>
        </div>
      );
    }

    if (status === 'recruiting') {
      return (
        <div className='mt-2 flex gap-3'>
          <Button
            variant='outline'
            className='flex-1 bg-white'
            onClick={() => {
              if (optimisticCancelled) return;
              onCancelBooking?.(booking.id);
            }}
          >
            예약 취소
          </Button>
          <Button
            className='bg-primary-dark flex-1 text-white hover:opacity-90'
            onClick={() => {
              if (optimisticCancelled) return;
              onViewDetails?.(booking.id);
            }}
          >
            수업 상세
          </Button>
        </div>
      );
    }
    if (status === 'in_progress' || status === 'completed') {
      return (
        <div className='mt-2 flex'>
          <Button
            className='bg-primary-dark flex-1 text-white hover:opacity-90'
            disabled={!!optimisticCancelled}
            onClick={() => {
              if (optimisticCancelled) return;
              onViewDetails?.(booking.id);
            }}
          >
            수업 상세
          </Button>
        </div>
      );
    }
    return null;
  };

  return (
    <div className='flex flex-col gap-4 rounded-lg border p-4'>
      <div className='flex items-start justify-between'>
        <div>
          <h3 className='text-lg font-bold'>{booking.classTitle}</h3>
          <p className='text-sm font-semibold text-gray-500'>
            {booking.instructorName} 코치님
          </p>
        </div>

        <div className='flex items-center gap-2'>
          {optimisticCancelled ? (
            <Badge
              className={cn(
                'rounded-full px-4 font-semibold text-white',
                'border-red-200 bg-red-500 text-white'
              )}
            >
              예약 취소됨
            </Badge>
          ) : (
            renderStatusBadge(booking.status)
          )}
        </div>
      </div>

      <div className='grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-900'>
        {/* 가까운 역 (nearestStation) */}
        {booking.location && (
          <div className='flex items-center gap-2'>
            <MapPin className='h-3 w-3' />
            <span>{booking.location}</span>
          </div>
        )}

        {/* 클래스 난이도 (level) */}
        {booking.level && (
          <div className='flex items-center gap-2'>
            <Star className='h-3 w-3' />
            <span className='text-primary'>
              {mapLevelToLabel(booking.level)} 클래스
            </span>
          </div>
        )}

        {/* 클래스 정원 (capacity) */}
        {booking.maxParticipants && (
          <div className='flex items-center gap-2'>
            <Users className='h-3 w-3' />
            <span>{booking.maxParticipants}인 그룹 수업</span>
          </div>
        )}

        {/* 스케줄 목록 (schedules) */}
        {/* <div className='col-span-2 flex items-start gap-2'>
          <Clock className='mt-0.5 h-4 w-4 flex-shrink-0' />
          <div className='flex flex-wrap gap-2'>
            {Array.isArray(
              (booking as Partial<BookingWithSchedules>).schedules
            ) &&
            ((booking as Partial<BookingWithSchedules>).schedules?.length ??
              0) > 0 ? (
              // booking.schedules가 서버에서 주입될 경우 (권장)
              (
                (booking as Partial<BookingWithSchedules>)
                  .schedules as BookingScheduleItem[]
              ).map((s, idx) => (
                <Badge key={idx} variant='secondary'>
                  {getDayLabel(s.dayOfWeek)} {s.startTime}~{s.endTime}
                </Badge>
              ))
            ) : booking.schedule ? (
              // 간략 요약 문자열이 제공되는 경우
              <span>{booking.schedule}</span>
            ) : booking.date && booking.time ? (
              // 단일 날짜/시간이 제공되는 경우의 대체 표시
              <span>
                {booking.date} {booking.time}
              </span>
            ) : (
              <span className='text-gray-400'>스케줄 정보가 없습니다</span>
            )}
          </div>
        </div> */}

        {/* 구현 안내 주석:
             - schedules(요일/시간 목록)는 서버 컴포넌트에서 `/api/classes/{classId}/schedules` 응답의 scheduleGroups[?].schedules를 이용해
               현재 `booking.scheduleGroupId`에 해당하는 그룹의 스케줄 배열을 조회 후, BookingTabs.server.tsx에서 `booking`에 병합해 전달하는 것을 권장합니다.
             - 예시: bookingWithSchedules = { ...booking, schedules: [{ dayOfWeek, startTime, endTime }, ...] }
             - 정원(capacity)은 booking.groupSize("현재인원/정원")에서 정원을 파싱하여 표기합니다. 별도의 최대 정원 필드가 추가되면 해당 필드를 직접 사용하세요.
          */}
      </div>
      <div className='w-full border-t pt-2'>
        {renderActionButtons(booking.status, !!optimisticCancelled)}
      </div>
    </div>
  );
}
