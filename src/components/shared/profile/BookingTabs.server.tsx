import { getUserBookings } from '@/lib/api/profile/profile.server';
import { BookingTabs } from './BookingTabs';

export default async function BookingTabsServer() {
  const bookingHistory = await getUserBookings();

  const bookingsForTabs = {
    reserved: bookingHistory.applied,
    inProgress: bookingHistory.inProgress,
    completed: bookingHistory.completed,
  };

  return <BookingTabs bookings={bookingsForTabs} />;
}
