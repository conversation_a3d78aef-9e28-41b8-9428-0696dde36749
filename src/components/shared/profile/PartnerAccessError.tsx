'use client';

import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export default function PartnerAccessError() {
  const router = useRouter();

  const handleGoToPartnerDashboard = () => {
    router.push('/partner/dashboard');
  };

  return (
    <Dialog open={true}>
      <DialogContent 
        className="sm:max-w-md" 
        showCloseButton={false}
      >
        <DialogHeader>
          <DialogTitle className="text-center">접근 제한</DialogTitle>
          <DialogDescription className="text-center">
            파트너 회원은 일반 회원 프로필 페이지에 접근할 수 없습니다.
            <br />
            파트너 대시보드를 이용해 주세요.
          </DialogDescription>
        </DialogHeader>
        
        <DialogFooter className="sm:justify-center">
          <Button 
            onClick={handleGoToPartnerDashboard}
            className="w-full sm:w-auto"
          >
            파트너 대시보드로 이동
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}