'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, Target } from 'lucide-react';
import Link from 'next/link';

interface UserStatsProps {
  stats: {
    nickname: string;
    totalClasses: number;
    attendedClasses: number;
    nextClasses: {
      title: string;
      date: string;
      time: string;
    }[];
  };
  attendancePoints?: number;
}

export function UserStats({ stats, attendancePoints }: UserStatsProps) {
  const { nickname, totalClasses, attendedClasses, nextClasses } = stats;
  return (
    <div className='space-y-6 bg-gray-50 p-4'>
      <div className='flex items-center justify-between'>
        <Link href='/profile/settings'>
          <h1 className='hover:text-primary text-2xl font-bold transition-colors'>
            {nickname}님
          </h1>
        </Link>
        {/* <div className='flex gap-2'>
          <Button
            size='sm'
            className='rounded-lg text-xs'
            onClick={() => window.open(KAKAO_CAHT, '_blank')}
          >
            <Edit className='h-4 w-4' /> 쉘위에 문의하기
          </Button>
        </div> */}
      </div>

      <div className='flex items-center gap-2'>
        <Card className='flex-1'>
          <CardContent className='flex flex-col items-center justify-center px-4 py-6'>
            <p className='text-3xl font-bold text-gray-900'>
              {totalClasses.toLocaleString()}
            </p>
            <p className='text-sm text-gray-600'>총 수업 일수</p>
          </CardContent>
        </Card>
        <Card className='flex-1'>
          <CardContent className='flex flex-col items-center justify-center px-4 py-6'>
            <p className='text-3xl font-bold text-gray-900'>
              {attendedClasses.toLocaleString()}
            </p>
            <p className='text-sm text-gray-600'>출석 일수</p>
          </CardContent>
        </Card>
      </div>

      {nextClasses.length > 0 && (
        <Card>
          <CardContent className='p-4'>
            <div className='mb-4 flex items-center gap-2'>
              <Target className='text-primary h-6 w-6' />
              <h3 className='text-lg font-bold'>다음 수업 일정</h3>
            </div>
            <div className='space-y-2'>
              <div className='flex items-center gap-2 rounded-md bg-gray-100 p-3'>
                <Clock className='h-5 w-5 text-gray-500' />
                <div className='text-sm text-gray-800'>
                  <p className='font-medium'>{nextClasses[0].title}</p>
                  <p>
                    {nextClasses[0].date} {nextClasses[0].time}
                  </p>
                </div>
              </div>
            </div>
            {attendancePoints && (
              <Button className='mt-4 w-full' size='lg'>
                오늘 수업 출석체크하고 500 포인트 받기!
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
