'use client';

import { useMemo, useState } from 'react';
import { BookingHistoryCard } from '@/components/shared/profile/BookingHistoryCard';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BookingProvider, useBooking } from '@/contexts/BookingContext';
import type { Booking } from '@/schemas/profile';
import { ConfirmCancelDialog } from '@/components/shared/profile/ConfirmCancelDialog';
import { cancelPaymentClient } from '@/lib/api/payment-client';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

interface BookingTabsProps {
  bookings: {
    reserved: Booking[];
    inProgress: Booking[];
    completed: Booking[];
  };
}

function BookingTabsContent({ bookings }: BookingTabsProps) {
  const [activeTab, setActiveTab] = useState('reserved');
  const { openPaymentModal, handleViewDetails } = useBooking();
  const router = useRouter();

  const [confirmOpen, setConfirmOpen] = useState(false);
  const [targetBooking, setTargetBooking] = useState<Booking | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [optimisticCancelledIds, setOptimisticCancelledIds] = useState<
    Set<string>
  >(new Set());

  const allBookings = useMemo(
    () => [...bookings.reserved, ...bookings.inProgress, ...bookings.completed],
    [bookings]
  );

  const handlePaymentClick = (booking: Booking) => {
    openPaymentModal(booking);
  };

  const handleCancelClick = (bookingId: string) => {
    // Optimistic guard: if already marked as cancelled, ignore
    // This prevents re-opening the dialog while in-flight
    if (optimisticCancelledIds.has(bookingId)) return;
    const found = allBookings.find(b => b.id === bookingId) || null;
    setTargetBooking(found);
    setError(null);
    setConfirmOpen(true);
  };

  const handleConfirmCancel = async () => {
    if (!targetBooking) return;
    const paymentKey = targetBooking.payment?.paymentKey;
    if (!paymentKey) {
      setError('결제 정보가 없어 취소할 수 없습니다.');
      return;
    }
    mutate({ paymentKey });
  };

  const { mutate } = useMutation({
    mutationFn: cancelPaymentClient,
    onMutate: () => {
      setIsSubmitting(true);
      setError(null);
      if (targetBooking) {
        setOptimisticCancelledIds(prev => {
          const next = new Set(prev);
          next.add(targetBooking.id);
          return next;
        });
      }
    },
    onSuccess: () => {
      toast.success('예약이 취소되었습니다. 환불이 처리됩니다.');
      setConfirmOpen(false);
      setTargetBooking(null);
      router.refresh();
    },
    onError: (e: unknown) => {
      toast.error(
        e instanceof Error ? e.message : '취소 처리 중 오류가 발생했습니다.'
      );
      setError(e instanceof Error ? e.message : '취소 처리에 실패했습니다');
      if (targetBooking) {
        setOptimisticCancelledIds(prev => {
          const next = new Set(prev);
          next.delete(targetBooking.id);
          return next;
        });
      }
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  return (
    <>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='reserved'>
            예약 수업 {bookings.reserved.length}
          </TabsTrigger>
          <TabsTrigger value='inProgress'>
            진행중 수업 {bookings.inProgress.length}
          </TabsTrigger>
          <TabsTrigger value='completed'>
            완료 수업 {bookings.completed.length}
          </TabsTrigger>
        </TabsList>

        <TabsContent value='reserved' className='mt-6'>
          <div className='space-y-4'>
            {bookings.reserved.length > 0 ? (
              bookings.reserved.map(booking => (
                <BookingHistoryCard
                  key={booking.id}
                  booking={booking}
                  onPaymentClick={handlePaymentClick}
                  onCancelBooking={handleCancelClick}
                  onViewDetails={handleViewDetails}
                  optimisticCancelled={optimisticCancelledIds.has(booking.id)}
                />
              ))
            ) : (
              <div className='py-8 text-center text-gray-500'>
                예약된 수업이 없습니다
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value='inProgress' className='mt-6'>
          <div className='space-y-4'>
            {bookings.inProgress.length > 0 ? (
              bookings.inProgress.map(booking => (
                <BookingHistoryCard
                  key={booking.id}
                  booking={booking}
                  onViewDetails={handleViewDetails}
                />
              ))
            ) : (
              <div className='py-8 text-center text-gray-500'>
                진행 중인 수업이 없습니다
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value='completed' className='mt-6'>
          <div className='space-y-4'>
            {bookings.completed.length > 0 ? (
              bookings.completed.map(booking => (
                <BookingHistoryCard
                  key={booking.id}
                  booking={booking}
                  onViewDetails={handleViewDetails}
                />
              ))
            ) : (
              <div className='py-8 text-center text-gray-500'>
                완료된 수업이 없습니다
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      <ConfirmCancelDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        isSubmitting={isSubmitting}
        error={error}
        onConfirm={handleConfirmCancel}
      />
    </>
  );
}

export function BookingTabs({ bookings: initialBookings }: BookingTabsProps) {
  return (
    <BookingProvider>
      <BookingTabsContent bookings={initialBookings} />
    </BookingProvider>
  );
}
