import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getFilterOptions } from '@/lib/actions/filter-options';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose,
} from '@/components/ui/drawer';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { FilterIcon, X } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ClassFilters {
  level?: string[];
  gender?: string[];
  nearestStation?: string[];
}

interface ClassFilterProps {
  filters: ClassFilters;
  onFiltersChange: (filters: ClassFilters) => void;
}

export default function ClassFilter({
  filters,
  onFiltersChange,
}: ClassFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('level');

  const {
    data: filterOptions = { levels: [], nearestStations: [], genders: [] },
    isLoading: isLoadingOptions,
  } = useQuery({
    queryKey: ['filterOptions'],
    queryFn: async () => {
      const response = await getFilterOptions();
      return response.success
        ? response.data
        : { levels: [], nearestStations: [], genders: [] };
    },
  });

  const handleFilterToggle = (key: keyof ClassFilters, value: string) => {
    const currentValues = filters[key] || [];
    const isSelected = currentValues.includes(value);

    let newValues: string[];
    if (isSelected) {
      newValues = currentValues.filter(v => v !== value);
    } else {
      newValues = [...currentValues, value];
    }

    const newFilters = { ...filters };
    if (newValues.length === 0) {
      delete newFilters[key];
    } else {
      newFilters[key] = newValues;
    }

    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    onFiltersChange({});
  };

  const getActiveFiltersCount = () => {
    return Object.values(filters).reduce((count, filterArray) => {
      return count + (filterArray?.length || 0);
    }, 0);
  };

  const getFilterCount = (key: keyof ClassFilters) => {
    return filters[key]?.length || 0;
  };

  const FilterButton = ({
    label,
    count,
    onClick,
  }: {
    label: string;
    count: number;
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className='flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm transition-colors hover:bg-gray-50'
      disabled={isLoadingOptions}
    >
      <span>{label}</span>
      {count > 0 && (
        <span className='bg-primary rounded-full px-1.5 py-0.5 text-xs text-white'>
          {count}
        </span>
      )}
    </button>
  );

  const FilterOptionButton = ({
    label,
    isSelected,
    onClick,
  }: {
    label: string;
    isSelected: boolean;
    onClick: () => void;
  }) => (
    <button
      onClick={onClick}
      className={cn(
        'rounded-lg border px-3 py-2 text-sm transition-colors',
        isSelected
          ? 'bg-primary text-primary-foreground border-primary'
          : 'border-gray-200 bg-white text-gray-700 hover:bg-gray-50'
      )}
    >
      {label}
    </button>
  );

  return (
    <div className='pt-2 pb-5'>
      {/* 필터별 Drawer Triggers */}
      <div className='flex flex-wrap gap-2'>
        <FilterButton
          label='난이도'
          count={getFilterCount('level')}
          onClick={() => {
            setActiveTab('level');
            setIsOpen(true);
          }}
        />
        <FilterButton
          label='대상'
          count={getFilterCount('gender')}
          onClick={() => {
            setActiveTab('gender');
            setIsOpen(true);
          }}
        />
        <FilterButton
          label='근처 역'
          count={getFilterCount('nearestStation')}
          onClick={() => {
            setActiveTab('nearestStation');
            setIsOpen(true);
          }}
        />

        {/* 전체 필터 버튼 */}
        {/* <button
          onClick={() => setIsOpen(true)}
          className='flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm transition-colors hover:bg-gray-50'
          disabled={isLoadingOptions}
        >
          <FilterIcon className='h-4 w-4' />
          <span>전체 필터</span>
          {getActiveFiltersCount() > 0 && (
            <span className='bg-primary rounded-full px-1.5 py-0.5 text-xs text-white'>
              {getActiveFiltersCount()}
            </span>
          )}
        </button> */}
      </div>

      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerContent className='mx-auto max-w-3xl'>
          <DrawerHeader className='flex flex-row items-center justify-between'>
            <DrawerTitle>필터 설정</DrawerTitle>
            <DrawerClose asChild>
              <button className='rounded-full p-1.5 hover:bg-gray-100'>
                <X className='h-4 w-4' />
              </button>
            </DrawerClose>
          </DrawerHeader>

          <div className='px-4 pb-6'>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className='w-full'>
                <TabsTrigger value='level' className='flex-1'>
                  레벨
                  {getFilterCount('level') > 0 && (
                    <span className='bg-primary ml-1 rounded-full px-1.5 py-0.5 text-xs text-white'>
                      {getFilterCount('level')}
                    </span>
                  )}
                </TabsTrigger>
                <TabsTrigger value='gender' className='flex-1'>
                  성별
                  {getFilterCount('gender') > 0 && (
                    <span className='bg-primary ml-1 rounded-full px-1.5 py-0.5 text-xs text-white'>
                      {getFilterCount('gender')}
                    </span>
                  )}
                </TabsTrigger>
                <TabsTrigger value='nearestStation' className='flex-1'>
                  역
                  {getFilterCount('nearestStation') > 0 && (
                    <span className='bg-primary ml-1 rounded-full px-1.5 py-0.5 text-xs text-white'>
                      {getFilterCount('nearestStation')}
                    </span>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value='level' className='mt-4'>
                <div className='space-y-3'>
                  <h3 className='text-sm font-medium'>레벨 선택</h3>
                  <div className='flex flex-wrap gap-2'>
                    {filterOptions.levels.map(level => (
                      <FilterOptionButton
                        key={level.value}
                        label={level.label}
                        isSelected={
                          filters.level?.includes(level.value) || false
                        }
                        onClick={() => handleFilterToggle('level', level.value)}
                      />
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value='gender' className='mt-4'>
                <div className='space-y-3'>
                  <h3 className='text-sm font-medium'>성별 선택</h3>
                  <div className='flex flex-wrap gap-2'>
                    {filterOptions.genders.map(gender => (
                      <FilterOptionButton
                        key={gender.value}
                        label={gender.label}
                        isSelected={
                          filters.gender?.includes(gender.value) || false
                        }
                        onClick={() =>
                          handleFilterToggle('gender', gender.value)
                        }
                      />
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value='nearestStation' className='mt-4'>
                <div className='space-y-3'>
                  <h3 className='text-sm font-medium'>역 선택</h3>
                  <div className='max-h-60 overflow-y-auto'>
                    <div className='flex flex-wrap gap-2'>
                      {filterOptions.nearestStations.map(station => (
                        <FilterOptionButton
                          key={station.value}
                          label={station.label}
                          isSelected={
                            filters.nearestStation?.includes(station.value) ||
                            false
                          }
                          onClick={() =>
                            handleFilterToggle('nearestStation', station.value)
                          }
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* 전체 초기화 버튼 */}
            {getActiveFiltersCount() > 0 && (
              <button
                onClick={clearAllFilters}
                className='mt-4 w-full rounded-lg border border-gray-200 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-50'
              >
                모든 필터 초기화
              </button>
            )}
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}
