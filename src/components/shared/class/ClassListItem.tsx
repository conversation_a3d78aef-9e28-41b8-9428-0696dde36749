import { Badge } from '@/components/ui/badge';
import { mapLevelToLabel, mapSpecialtyToLabel } from '@/lib/utils/format';
import { MapPin, Users } from 'lucide-react';
import Link from 'next/link';

interface ClassListItemProps {
  classInfo: {
    id: string;
    title: string;
    level: string;
    category: string;
    maxParticipants: number;
    pricePerSession: number;
    thumbnailUrl?: string | null;
  };
  instructor: {
    name: string;
    experienceTotalYears: number;
  };
  studio: {
    nearestStation: string | null;
  };
}

export default function ClassListItem({
  classInfo,
  instructor,
  studio,
}: ClassListItemProps) {
  const formatPrice = (price: number) => {
    return price.toLocaleString('ko-KR');
  };

  return (
    <Link href={`/classes/${classInfo.id}`}>
      <div className='flex gap-5'>
        {/* Image Section - 130x130 고정, 1:1 비율 유지, 컨텐츠와 겹치지 않도록 고정 폭 */}
        <div className='relative h-[130px] w-[130px] flex-shrink-0 overflow-hidden rounded-md'>
          <img
            src={classInfo.thumbnailUrl || '/mock.jpg'}
            alt={classInfo.title}
            className='h-full w-full object-cover'
          />
          {/* Category Badge */}
          <Badge
            variant='secondary'
            className='bg-primary text-primary-foreground absolute top-0 left-0 w-fit rounded-md px-2'
          >
            {mapLevelToLabel(classInfo.level)}
          </Badge>
        </div>

        {/* Content Section */}
        <div className='flex min-w-0 flex-1 flex-col justify-between'>
          {/* Class Type, Location, Group Size */}
          <div className='flex flex-col gap-1'>
            <div className='flex flex-wrap items-center gap-2'>
              <div className='bg-neutral-200 px-1 text-xs font-medium whitespace-nowrap'>
                {mapSpecialtyToLabel(classInfo.category)}
              </div>
              <div className='flex items-center gap-1 text-xs whitespace-nowrap text-gray-600'>
                <MapPin className='h-3 w-3' />
                <span>{studio.nearestStation ?? ''}</span>
              </div>
              <div className='flex items-center gap-1 text-xs whitespace-nowrap text-gray-600'>
                <Users className='h-3 w-3' />
                <span>{classInfo.maxParticipants}인 그룹</span>
              </div>
            </div>

            {/* Title */}
            <h3 className='text-foreground mb-1 line-clamp-2 leading-tight font-bold md:text-xl'>
              {classInfo.title}
            </h3>

            {/* Instructor Info */}
            <div className='text-muted-foreground text-xs whitespace-nowrap'>
              {instructor.name} 강사 / {instructor.experienceTotalYears}년차
            </div>
          </div>

          {/* Bottom Section */}
          <div className='flex items-end justify-end gap-2 whitespace-nowrap'>
            <span className='text-primary text-sm'>회당</span>
            <span className='text-primary text-2xl font-bold'>
              {formatPrice(classInfo.pricePerSession)}원
            </span>
          </div>
        </div>
      </div>
    </Link>
  );
}
