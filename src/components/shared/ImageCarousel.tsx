'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from '@/components/ui/carousel';
import { cn } from '@/lib/utils';

interface ImageCarouselProps {
  images: string[];
  className?: string;
  aspectRatio?: 'video' | 'square' | 'auto';
  showIndicators?: boolean;
  showControls?: boolean;
  onImageClick?: (index: number) => void;
}

export default function ImageCarousel({
  images,
  className,
  aspectRatio = 'video',
  showIndicators = true,
  showControls = true,
  onImageClick,
}: ImageCarouselProps) {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on('select', () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  const scrollPrev = useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollNext = useCallback(() => {
    api?.scrollNext();
  }, [api]);

  const handleImageClick = useCallback(
    (index: number) => {
      onImageClick?.(index);
    },
    [onImageClick]
  );

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        scrollPrev();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        scrollNext();
      }
    },
    [scrollPrev, scrollNext]
  );

  if (!images || images.length === 0) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-gray-200',
          aspectRatio === 'video' && 'aspect-video',
          aspectRatio === 'square' && 'aspect-square',
          className
        )}
      >
        <span className='text-sm text-gray-500'>이미지가 없습니다</span>
      </div>
    );
  }

  // Single image - no carousel needed
  if (images.length === 1) {
    return (
      <div
        className={cn(
          'relative overflow-hidden bg-gray-200',
          aspectRatio === 'video' && 'aspect-video',
          aspectRatio === 'square' && 'aspect-square',
          className
        )}
      >
        <img
          src={images[0]}
          alt='이미지'
          className={cn(
            'h-full w-full object-cover transition-transform hover:scale-105',
            onImageClick && 'cursor-pointer'
          )}
          onClick={() => handleImageClick(0)}
          onKeyDown={handleKeyDown}
          tabIndex={onImageClick ? 0 : -1}
        />
      </div>
    );
  }

  return (
    <div
      className={cn('group relative', className)}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      <Carousel
        setApi={setApi}
        className='w-full'
        opts={{
          align: 'start',
          loop: true,
        }}
      >
        <CarouselContent>
          {images.map((image, index) => (
            <CarouselItem key={index}>
              <div
                className={cn(
                  'relative overflow-hidden bg-gray-200',
                  aspectRatio === 'video' && 'aspect-video',
                  aspectRatio === 'square' && 'aspect-square'
                )}
              >
                <img
                  src={image}
                  alt={`이미지 ${index + 1}`}
                  className={cn(
                    'h-full w-full object-cover transition-transform hover:scale-105',
                    onImageClick && 'cursor-pointer'
                  )}
                  onClick={() => handleImageClick(index)}
                  loading='lazy'
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      {showControls && images.length > 1 && (
        <div className='absolute top-1/2 right-0 left-0 flex -translate-y-1/2 justify-between px-5'>
          <Button
            variant='outline'
            size='icon'
            className={cn(
              'border-0 bg-white/90 shadow-lg hover:bg-white',
              'opacity-0 transition-opacity duration-200 group-hover:opacity-100',
              'z-10 h-8 w-8'
            )}
            onClick={scrollPrev}
            aria-label='이전 이미지'
          >
            <ChevronLeft className='h-4 w-4' />
          </Button>
          <Button
            variant='outline'
            size='icon'
            className={cn(
              'border-0 bg-white/90 shadow-lg hover:bg-white',
              'opacity-0 transition-opacity duration-200 group-hover:opacity-100',
              'z-10 h-8 w-8'
            )}
            onClick={scrollNext}
            aria-label='다음 이미지'
          >
            <ChevronRight className='h-4 w-4' />
          </Button>
        </div>
      )}
      {/* Image Indicators */}
      {showIndicators && images.length > 1 && (
        <div className='absolute bottom-3 left-1/2 z-10 flex -translate-x-1/2 gap-2'>
          {images.map((_, index) => (
            <button
              key={index}
              className={cn(
                'h-2 w-2 rounded-full transition-all duration-200',
                current === index + 1
                  ? 'bg-white shadow-lg'
                  : 'bg-white/50 hover:bg-white/75'
              )}
              onClick={() => api?.scrollTo(index)}
              aria-label={`이미지 ${index + 1}로 이동`}
            />
          ))}
        </div>
      )}

      {/* Image Counter */}
      {showIndicators && images.length > 1 && (
        <div className='absolute right-3 bottom-3 rounded-full bg-black/50 px-2 py-1 text-xs text-white'>
          {current} / {count}
        </div>
      )}
    </div>
  );
}
