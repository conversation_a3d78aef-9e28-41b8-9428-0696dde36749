import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/card';
import { cn, sortTime } from '@/lib/utils';
import { CheckCircle, Clock, Users } from 'lucide-react';

interface StudentDemographics {
  ageRange: string;
  gender: string;
  count: number;
}

interface CurrentEnrollment {
  memberId: string;
  name: string | null;
  gender: string | null;
}

interface ScheduleData {
  id: string;
  title: string;
  frequency: string;
  times: Array<{
    day: string;
    time: string;
  }>;
  spotsLeft: number;
  studentDemographics?: StudentDemographics;
  isSelected?: boolean;
  maxParticipants: number;
  currentEnrollments?: CurrentEnrollment[];
}

function formatEnrollmentInfo(enrollments: CurrentEnrollment[]): string {
  if (enrollments.length === 0) return '';

  const genderCounts = enrollments.reduce(
    (acc, enrollment) => {
      if (enrollment.gender) {
        acc[enrollment.gender] = (acc[enrollment.gender] || 0) + 1;
      }
      return acc;
    },
    {} as Record<string, number>
  );

  const genderLabels = Object.entries(genderCounts)
    .map(([gender, count]) => `${gender} ${count}명`)
    .join(', ');

  return `현재 수업 신청자: ${genderLabels}`;
}

interface BookingCardProps {
  schedule: ScheduleData;
  onSelect: (scheduleId: string) => void;
  index: number;
  className?: string;
}

export function BookingCard({
  schedule,
  onSelect,
  index,
  className,
}: BookingCardProps) {
  const { id, title, times, isSelected, maxParticipants, currentEnrollments } =
    schedule;

  const sortedTimes = sortTime(times);
  return (
    <Card
      className={cn(
        'min-w-[320px] cursor-pointer p-3 transition-all hover:shadow-md sm:min-w-0',
        isSelected && 'border-primary ring-primary bg-primary/5 ring-2',
        className
      )}
      onClick={() => onSelect(id)}
    >
      <CardHeader className='px-2'>
        <CardTitle className='text-primary font-bold'>
          {index + 1}. {title}
        </CardTitle>

        {isSelected ? (
          <CardAction>
            <CheckCircle className='text-primary' />
          </CardAction>
        ) : (
          <CardAction>
            <CheckCircle className='text-black/20' />
          </CardAction>
        )}
      </CardHeader>

      <CardContent className='space-y-2 p-2'>
        <div className='space-y-2'>
          {sortedTimes.map((timeSlot, index) => (
            <div key={index} className='flex items-center gap-2 text-sm'>
              <Clock className='h-4 w-4 text-gray-500' />
              <span className='text-gray-700'>
                {timeSlot.day} {timeSlot.time}
              </span>
            </div>
          ))}
        </div>
        <div className='flex items-center gap-2'>
          <Users className='h-4 w-4 text-gray-700' />
          <span className='text-sm text-gray-700'>
            {currentEnrollments?.length}/{maxParticipants}명
          </span>
        </div>
      </CardContent>

      {/* 수업 신청자 정보 */}
      <CardFooter className='bg-primary/10 rounded-md py-2'>
        <div className='w-full'>
          <div
            className={cn(
              'text-center text-sm',
              currentEnrollments && currentEnrollments.length > 0
                ? 'text-gray-700'
                : 'text-primary'
            )}
          >
            {currentEnrollments && currentEnrollments.length > 0
              ? formatEnrollmentInfo(currentEnrollments)
              : 'NEW! 원하는 시간에 먼저 예약해보세요!'}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
