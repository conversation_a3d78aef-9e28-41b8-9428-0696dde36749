import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { CreditCard, Info } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { createEnrollment } from '@/lib/api/enrollments/enrollments.api';
import {
  paymentStoreActions,
  type PaymentInfo,
} from '@/contexts/payment.store';
import { ClassSchedulesResponse } from '@/app/api/classes/schema';
import { useUserStore } from '@/contexts/user.store';
import { getCurrentUser } from '@/lib/supabase/auth';

type BookingPaymentModalProps = {
  isOpen: boolean;
  onClose: () => void;
  classData: ClassSchedulesResponse;
  selectedScheduleGroupId: string;
};

export function BookingPaymentModal({
  isOpen,
  onClose,
  classData,
  selectedScheduleGroupId,
}: BookingPaymentModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const userId = useUserStore(state => state.id);

  const computed = useMemo(() => {
    // 임시 계산: 총 금액(월/기간 기준)과 예약금 15%
    const totalAmount =
      classData.class.pricePerSession *
      classData.class.sessionsPerWeek *
      classData.class.durationWeeks;
    const depositRate = 15;
    const depositAmount = Math.round(totalAmount * (depositRate / 100));
    return { depositRate, depositAmount, totalAmount };
  }, [classData]);

  const getCurrentUrl = () => {
    const currentPath = pathname;
    const params = searchParams.toString();
    return params ? `${currentPath}?${params}` : currentPath;
  };

  const handlePayment = async () => {
    setIsProcessing(true);

    try {
      // 1. 인증 상태 확인 (userStore와 Supabase 직접 확인)
      // userStore에 user가 없으면 Supabase에서 직접 확인

      if (!userId) {
        const supabaseUser = await getCurrentUser();

        // Supabase에 사용자가 있으면 userStore 업데이트
        if (supabaseUser) {
          useUserStore.getState().setUserField('id', supabaseUser.id);
        }

        if (!supabaseUser) {
          setIsProcessing(false);
          const currentUrl = getCurrentUrl();
          router.push(`/login?redirectTo=${encodeURIComponent(currentUrl)}`);
          return;
        }
      }

      // 2. 수강신청 생성 API 호출
      const enrollmentResponse = await createEnrollment({
        classId: classData.class.id,
        scheduleGroupId: parseInt(selectedScheduleGroupId),
      });

      // 3. 결제 정보 스토어 저장
      const paymentWidget = enrollmentResponse.paymentWidget;

      const nextInfo: PaymentInfo = {
        classData,
        selectedScheduleGroupId,
        enrollment: {
          id: enrollmentResponse.enrollment.id,
          status: enrollmentResponse.enrollment.status,
          totalAmount: enrollmentResponse.enrollment.totalAmount,
          depositAmount: enrollmentResponse.enrollment.depositAmount,
        },
        paymentWidget: {
          clientKey: paymentWidget.clientKey,
          amount: paymentWidget.amount,
          orderId: paymentWidget.orderId,
          orderName: paymentWidget.orderName,
          customerName: paymentWidget.customerName,
          successUrl: paymentWidget.successUrl,
          failUrl: paymentWidget.failUrl,
        },
      };

      paymentStoreActions.setPaymentInfo(nextInfo);

      // 4. 모달 닫기 후 결제 페이지로 이동 (쿼리스트링 제거)
      onClose();
      router.push(`/payment/${paymentWidget.orderId}`);
    } catch (error) {
      setIsProcessing(false);
      console.error('수강신청 생성 실패:', error);

      // 사용자에게 에러 메시지 표시
      const errorMessage =
        error instanceof Error
          ? error.message
          : '수강신청에 실패했습니다. 다시 시도해주세요.';
      alert(errorMessage);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='sm:max-w-[400px]'>
        <DialogHeader className='mb-4'>
          <div className='flex items-center justify-between'>
            <DialogTitle className='text-xl font-semibold'>
              수업 예약 확정하기
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className='space-y-4'>
          {/* <Card className='bg-gray-50'>
            <CardContent className='space-y-3 p-4'>
              <h3 className='line-clamp-1 min-w-0 font-medium text-black'>
                {classData.class.title}
              </h3>
              <div className='space-y-1 text-sm text-gray-600'>
                <p>{classData.class.instructor.name}</p>
                <p>{classData.class.studio.address}</p>
                <div>
                  {(
                    classData.scheduleGroups.find(
                      g => g.id === selectedScheduleGroupId
                    )?.schedules || []
                  ).map((s, index) => (
                    <div key={index}>
                      {DAYS_MAP[s.dayOfWeek] || s.dayOfWeek}{' '}
                      {s.startTime.slice(0, 5)} ~ {s.endTime.slice(0, 5)}
                    </div>
                  ))}
                </div>
              </div>
              <p className='bg-primary/10 w-full rounded-md p-2 text-center'>
                운동 초급 45-54 여성 클래스
              </p>
            </CardContent>
          </Card> */}

          <div className='rounded-md bg-neutral-200/50 p-3'>
            <div className='text-primary mb-3 flex items-center gap-2'>
              <Info className='fill-primary text-white' />
              <span className='text-lg font-bold text-black'>
                예약금 결제 안내
              </span>
            </div>

            <div className='mb-3 flex flex-col gap-2'>
              <div className='flex items-center justify-between rounded-md bg-white p-3 text-sm'>
                <span className='text-black'>총 수업료</span>
                <span className='font-medium'>
                  {computed.totalAmount.toLocaleString()}원
                </span>
              </div>

              <div className='bg-primary flex items-center justify-between rounded-md p-3 text-sm text-white'>
                <span className=''>
                  예약금 결제 (수업료 {computed.depositRate}%)
                </span>
                <span className='font-bold text-white'>
                  {computed.depositAmount.toLocaleString()}원
                </span>
              </div>
            </div>

            <div className='flex flex-col gap-1 px-2 text-[11px] text-gray-500'>
              <div className='flex items-start gap-2'>
                예약금 15%를 제외한 잔액 수업료는 센터에서 결제해주시면 됩니다.
              </div>
              <div className='flex items-start gap-2'>
                첫월 수업료 수업의 취소는 전액 2주 이내로 완불되어야 합니다.
              </div>
              <div className='flex items-start gap-2'>
                수업 확정 후 취소 시 완불 정책에 따라 처리됩니다.
              </div>
            </div>
          </div>

          <div className='flex gap-3 pt-4'>
            <Button
              size='lg'
              variant='outline'
              onClick={onClose}
              className='flex-1'
              disabled={isProcessing}
            >
              취소
            </Button>
            <Button
              size='lg'
              onClick={handlePayment}
              className='flex-1'
              disabled={isProcessing}
            >
              {isProcessing ? (
                '결제 중...'
              ) : (
                <>
                  <CreditCard className='mr-2 h-4 w-4' />
                  예약금 결제하기
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
