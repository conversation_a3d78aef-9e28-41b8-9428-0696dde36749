import React, { useCallback, useEffect } from 'react';
import { X, ZoomIn, ZoomOut } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogPortal,
  DialogOverlay,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ImageCarousel from './ImageCarousel';
import { cn } from '@/lib/utils';

interface ImageViewerProps {
  images: string[];
  initialIndex?: number;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
}

export default function ImageViewer({
  images,
  initialIndex = 0,
  isOpen,
  onClose,
  title = '이미지 보기',
}: ImageViewerProps) {
  const [currentIndex, setCurrentIndex] = React.useState(initialIndex);
  const [isZoomed, setIsZoomed] = React.useState(false);

  // Update current index when initialIndex changes
  useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex]);

  // Reset zoom when image changes
  useEffect(() => {
    setIsZoomed(false);
  }, [currentIndex]);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
        case 'ArrowLeft':
          event.preventDefault();
          setCurrentIndex(prev => (prev > 0 ? prev - 1 : images.length - 1));
          break;
        case 'ArrowRight':
          event.preventDefault();
          setCurrentIndex(prev => (prev < images.length - 1 ? prev + 1 : 0));
          break;
        case 'z':
        case 'Z':
          event.preventDefault();
          setIsZoomed(!isZoomed);
          break;
      }
    },
    [isOpen, onClose, images.length, isZoomed]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  const handleImageClick = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  const toggleZoom = useCallback(() => {
    setIsZoomed(!isZoomed);
  }, [isZoomed]);

  if (!images || images.length === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogPortal>
        <DialogOverlay className='bg-black/90 backdrop-blur-sm' />
        <DialogContent
          className={cn(
            'h-full max-h-[95vh] w-full max-w-[95vw] border-0 bg-transparent p-0',
            'overflow-hidden focus:outline-none'
          )}
          aria-describedby='image-viewer-description'
        >
          <div className='relative flex h-full w-full flex-col'>
            {/* Header */}
            <div className='absolute top-4 right-0 left-0 z-20 flex items-center justify-between px-4'>
              <div className='rounded-lg bg-black/50 px-3 py-2 text-sm text-white backdrop-blur-sm'>
                {title} ({currentIndex + 1} / {images.length})
              </div>

              <div className='flex items-center gap-2'>
                <Button
                  variant='outline'
                  size='icon'
                  className='border-white/20 bg-black/50 text-white backdrop-blur-sm hover:bg-black/70'
                  onClick={toggleZoom}
                  aria-label={isZoomed ? '축소' : '확대'}
                >
                  {isZoomed ? (
                    <ZoomOut className='h-4 w-4' />
                  ) : (
                    <ZoomIn className='h-4 w-4' />
                  )}
                </Button>

                <DialogClose asChild>
                  <Button
                    variant='outline'
                    size='icon'
                    className='border-white/20 bg-black/50 text-white backdrop-blur-sm hover:bg-black/70'
                    aria-label='닫기'
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </DialogClose>
              </div>
            </div>

            {/* Main Content */}
            <div className='flex flex-1 items-center justify-center p-4 pt-20'>
              {images.length === 1 ? (
                // Single image with zoom capability
                <div className='relative max-h-full max-w-full'>
                  <img
                    src={images[0]}
                    alt={`${title} 이미지`}
                    className={cn(
                      'max-h-full max-w-full cursor-pointer object-contain transition-transform duration-300',
                      isZoomed ? 'scale-150' : 'scale-100'
                    )}
                    onClick={toggleZoom}
                  />
                </div>
              ) : (
                // Multiple images carousel
                <div className='w-full max-w-4xl'>
                  <ImageCarousel
                    images={images}
                    className='w-full'
                    aspectRatio='auto'
                    showIndicators={true}
                    showControls={true}
                    onImageClick={handleImageClick}
                  />
                </div>
              )}
            </div>

            {/* Mobile Navigation Hints */}
            <div className='absolute bottom-4 left-1/2 z-20 -translate-x-1/2 md:hidden'>
              <div className='rounded-lg bg-black/50 px-3 py-2 text-xs text-white backdrop-blur-sm'>
                좌우로 스와이프하여 이동
              </div>
            </div>

            {/* Desktop Navigation Hints */}
            <div className='absolute bottom-4 left-1/2 z-20 hidden -translate-x-1/2 md:block'>
              <div className='rounded-lg bg-black/50 px-3 py-2 text-xs text-white backdrop-blur-sm'>
                ← → 키로 이동 • Z키로 확대/축소 • ESC로 닫기
              </div>
            </div>
          </div>

          {/* Screen reader description */}
          <div id='image-viewer-description' className='sr-only'>
            전체화면 이미지 뷰어입니다. 화살표 키로 이미지를 탐색하고, Z키로
            확대/축소하며, ESC키로 닫을 수 있습니다.
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
