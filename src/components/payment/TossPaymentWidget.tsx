'use client';

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { loadTossPayments } from '@tosspayments/tosspayments-sdk';
import getEnv from '@/lib/config/get-env';
import { preparePaymentClient } from '@/lib/api/payment-client';

interface TossPaymentWidgetProps {
  customerKey: string;
  amount: number;
  enrollmentId: string;
  orderId: string;
  orderName: string;
  successUrl: string;
  failUrl: string;
  customerEmail?: string;
  customerName?: string;
  onPaymentReady?: () => void;
  onPaymentMethodSelect?: (paymentMethod: any) => void;
  isPaymentMethodSelected?: boolean;
  onPaymentMethodSelectionChange?: (isSelected: boolean) => void;
  hidePayButton?: boolean;
  onError?: (error: { title: string; description: string }) => void;
}

export interface TossPaymentWidgetHandle {
  requestPayment: () => Promise<void>;
}

function TossPaymentWidgetInternal(
  {
    customerKey,
    amount,
    enrollmentId,
    orderId,
    orderName,
    successUrl,
    failUrl,
    customerEmail,
    customerName,
    onPaymentReady,
    onPaymentMethodSelect,
    isPaymentMethodSelected = false,
    onPaymentMethodSelectionChange,
    hidePayButton = false,
    onError,
  }: TossPaymentWidgetProps,
  ref: React.Ref<TossPaymentWidgetHandle>
) {
  const paymentMethodRef = useRef<HTMLDivElement>(null);
  const widgetsRef = useRef<any | null>(null);
  const paymentMethodsRef = useRef<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const amountUpdatePromiseRef = useRef<Promise<void> | null>(null);
  const requestInFlightRef = useRef(false);
  const initVersionRef = useRef(0);

  useEffect(() => {
    const initializeTossPayments = async () => {
      const myInitVersion = ++initVersionRef.current;
      try {
        const clientKey = getEnv('NEXT_PUBLIC_TOSS_WIDGET_CLIENT_KEY');

        if (!clientKey) {
          throw new Error(
            '토스페이먼츠 결제위젯 클라이언트 키가 설정되지 않았습니다. NEXT_PUBLIC_TOSS_WIDGET_CLIENT_KEY 환경변수를 확인해주세요.'
          );
        }

        // 기존 UI가 있다면 먼저 정리 (StrictMode 더블 마운트/재초기화 대비)
        try {
          paymentMethodsRef.current?.destroy?.();
        } catch {}
        paymentMethodsRef.current = null;

        // 토스페이먼츠 SDK 로드
        const tossPayments = await loadTossPayments(clientKey);
        if (initVersionRef.current !== myInitVersion) return;

        // 결제위젯 초기화
        const widgetsInstance = tossPayments.widgets({ customerKey });
        widgetsRef.current = widgetsInstance;

        // 결제 금액 설정
        amountUpdatePromiseRef.current = widgetsInstance.setAmount({
          currency: 'KRW',
          value: amount,
        });
        try {
          await amountUpdatePromiseRef.current;
        } finally {
          amountUpdatePromiseRef.current = null;
        }
        if (initVersionRef.current !== myInitVersion) return;

        // 결제 UI 렌더링
        const paymentMethodInstance =
          await widgetsInstance.renderPaymentMethods({
            selector: '#payment-method',
            variantKey: 'DEFAULT',
          });
        if (initVersionRef.current !== myInitVersion) {
          try {
            paymentMethodInstance.destroy?.();
          } catch {}
          return;
        }
        paymentMethodsRef.current = paymentMethodInstance;

        // 결제수단 선택 이벤트 구독
        paymentMethodInstance.on(
          'paymentMethodSelect',
          (selectedPaymentMethod: any) => {
            onPaymentMethodSelect?.(selectedPaymentMethod);
            onPaymentMethodSelectionChange?.(true);
          }
        );

        setIsLoading(false);
        onPaymentReady?.();
      } catch (err) {
        console.error('토스페이먼츠 초기화 실패:', err);

        // 키 타입 관련 오류인지 확인
        const errorMessage =
          err instanceof Error ? err.message : '알 수 없는 오류';
        if (errorMessage.includes('결제위젯 연동 키')) {
          setError(
            '토스페이먼츠 키 설정 오류: 결제위젯 연동 키를 사용해야 합니다. API 개별 연동 키는 지원되지 않습니다.'
          );
        } else {
          setError(
            errorMessage.includes('클라이언트 키')
              ? errorMessage
              : '결제 위젯 로드에 실패했습니다.'
          );
        }
        setIsLoading(false);
      }
    };

    initializeTossPayments();

    // 컴포넌트 언마운트 시 정리
    return () => {
      ++initVersionRef.current; // 이후 비동기 진행 중단 신호
      paymentMethodsRef.current?.destroy?.();
      paymentMethodsRef.current = null;
      widgetsRef.current = null;
    };
  }, []);

  // 금액이 변경될 때 업데이트
  useEffect(() => {
    if (widgetsRef.current && !isLoading) {
      (async () => {
        try {
          amountUpdatePromiseRef.current = widgetsRef.current.setAmount({
            currency: 'KRW',
            value: amount,
          });
          await amountUpdatePromiseRef.current;
        } catch (e) {
          console.error('결제 금액 업데이트 실패:', e);
        } finally {
          amountUpdatePromiseRef.current = null;
        }
      })();
    }
  }, [amount, isLoading]);

  const handlePayment = async () => {
    if (!widgetsRef.current) {
      alert('결제 위젯이 준비되지 않았습니다.');
      return;
    }

    try {
      if (requestInFlightRef.current) {
        return; // 중복 요청 방지
      }
      // setAmount 등 선행 작업이 끝날 때까지 대기
      if (amountUpdatePromiseRef.current) {
        try {
          await amountUpdatePromiseRef.current;
        } catch {}
      }
      requestInFlightRef.current = true;

      // 결제 준비 단계: 서버에 orderId, amount 저장 (보안 검증용)
      try {
        await preparePaymentClient({
          enrollmentId,
          orderId,
          amount,
        });
      } catch (e) {
        console.error('결제 준비 실패:', e);
        onError?.({
          title: '결제 준비 실패',
          description:
            e instanceof Error
              ? e.message
              : '결제 준비에 실패했습니다. 잠시 후 다시 시도해주세요.',
        });
        if (!onError) {
          alert('결제 준비에 실패했습니다. 잠시 후 다시 시도해주세요.');
        }
        requestInFlightRef.current = false;
        return;
      }

      await widgetsRef.current.requestPayment({
        orderId,
        orderName,
        successUrl,
        failUrl,
        customerEmail,
        customerName,
      });
    } catch (error) {
      console.error('결제 요청 실패:', error);
      onError?.({
        title: '결제 요청 실패',
        description: '결제 요청에 실패했습니다. 다시 시도해주세요.',
      });
      if (!onError) {
        alert('결제 요청에 실패했습니다. 다시 시도해주세요.');
      }
    } finally {
      requestInFlightRef.current = false;
    }
  };

  useImperativeHandle(ref, () => ({
    requestPayment: handlePayment,
  }));

  if (error) {
    return (
      <div className='rounded-lg border border-red-200 bg-red-50 p-4'>
        <div className='flex'>
          <div className='ml-3'>
            <h3 className='text-sm font-medium text-red-800'>
              결제 위젯 로드 오류
            </h3>
            <div className='mt-2 text-sm text-red-700'>
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* 결제수단 선택 영역 */}
      <div>
        <h3 className='mb-3 text-xl font-semibold text-black'>결제수단</h3>
        <div
          id='payment-method'
          ref={paymentMethodRef}
          className={`min-h-[200px] rounded-lg border ${
            isLoading ? 'animate-pulse bg-gray-100' : 'bg-white'
          }`}
        >
          {isLoading && (
            <div className='flex h-[200px] items-center justify-center'>
              <div className='text-center'>
                <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-t-transparent'></div>
                <p className='text-sm text-gray-600'>
                  결제 위젯을 로드하는 중...
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 약관 동의 UI 제거됨 */}

      {/* 결제 버튼 (외부에서 처리하는 경우 숨김) */}
      {!hidePayButton && (
        <button
          onClick={handlePayment}
          disabled={isLoading || !isPaymentMethodSelected}
          className='bg-primary hover:bg-primary/90 w-full rounded-lg px-4 py-3 font-semibold text-white transition-colors disabled:cursor-not-allowed disabled:opacity-50'
        >
          {isLoading
            ? '로딩 중...'
            : !isPaymentMethodSelected
              ? '결제수단을 선택해주세요'
              : `${amount.toLocaleString()}원 결제하기`}
        </button>
      )}
    </div>
  );
}

const TossPaymentWidget = forwardRef<
  TossPaymentWidgetHandle,
  TossPaymentWidgetProps
>(TossPaymentWidgetInternal);

export default TossPaymentWidget;
