import { createSupabaseClient } from '@/lib/supabase/server';
import UserStoreHydrator from './UserStoreHydrator';

export default async function UserStoreProvider() {
  const supabase = await createSupabaseClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  return (
    <UserStoreHydrator
      initialUserData={{
        id: user?.id || null,
        email: user?.email || null,
      }}
    />
  );
}
