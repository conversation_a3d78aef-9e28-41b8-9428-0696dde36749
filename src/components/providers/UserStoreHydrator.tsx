'use client';

import { useEffect } from 'react';
import { userStoreActions } from '@/contexts/user.store';
import type { UserState } from '@/contexts/user.store';
import { setUserId } from '@/lib/ga';

interface UserStoreHydratorProps {
  initialUserData: UserState | null;
}

/**
 * 서버에서 받은 초기 사용자 데이터로 클라이언트 스토어를 하이드레이션
 */
export default function UserStoreHydrator({
  initialUserData,
}: UserStoreHydratorProps) {
  useEffect(() => {
    userStoreActions.setUser(initialUserData || { id: null, email: null });

    if (initialUserData && initialUserData.id) {
      setUserId(initialUserData.id);
    }

    // 개발 환경에서 하이드레이션 로그
    if (process.env.NODE_ENV === 'development') {
      console.log('🏠 UserStore hydrated:', {
        id: initialUserData?.id || 'not authenticated',
        email: initialUserData?.email || 'not authenticated',
      });
    }
  }, [initialUserData]);

  return null;
}
