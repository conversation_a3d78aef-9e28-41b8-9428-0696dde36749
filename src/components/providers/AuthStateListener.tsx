'use client';

import { useEffect } from 'react';
import { userStoreActions } from '@/contexts/user.store';
import { onAuthStateChange } from '@/lib/supabase/auth';
import { User } from '@supabase/supabase-js';

/**
 * 실시간 인증 상태 변경 감지 컴포넌트
 * 초기 하이드레이션은 UserStoreHydrator가 담당하고,
 * 이 컴포넌트는 오직 상태 변경만 감지
 */
export default function AuthStateListener() {
  useEffect(() => {
    const {
      data: { subscription },
    } = onAuthStateChange(async (user: User | null) => {
      try {
        if (user) {
          userStoreActions.setUserField('id', user.id);
          if (process.env.NODE_ENV === 'development') {
            console.log('🔄 Auth state changed: User signed in', user.email);
          }
        } else {
          userStoreActions.reset();
          if (process.env.NODE_ENV === 'development') {
            console.log('🔄 Auth state changed: User signed out');
          }
        }
      } catch (error) {
        console.error('AuthStateListener error:', error);
      }
    });

    // 컴포넌트 언마운트 시 구독 해제
    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 렌더링하지 않음
  return null;
}
