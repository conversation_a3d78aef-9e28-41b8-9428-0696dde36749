'use client';

import { useEffect, useMemo, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowLeft, User } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

interface RouteTitleConfig {
  title: string;
  exact?: boolean;
  hideProfile?: boolean;
  hideBack?: boolean;
  hideAppBar?: boolean; // full suppression
}

type RouteConfigEntry =
  | RouteTitleConfig
  | ((pathname: string) => RouteTitleConfig);

const ROUTE_TITLES: Record<string, RouteConfigEntry> = {
  '/': { title: 'SHALLWE', exact: true, hideBack: true },
  '/profile': { title: '마이페이지', exact: true, hideProfile: true },
  '/profile/settings': { title: '설정', exact: true },
  '/profile/settings/notifications': { title: '알림 설정', exact: true },
  '/classes/': (pathname: string) => {
    if (pathname.includes('/schedules')) {
      return { title: '예약하기' };
    }
    return { title: 'SHALLWE' };
  },
  '/instructor': { title: '강사 페이지' },
  '/onboarding': { title: '온보딩' },
  '/payment': { title: '결제' },
  '/docs/terms': { title: '이용약관', hideProfile: true },
  '/docs/privacy-policy': { title: '개인정보처리방침', hideProfile: true },
} as const;

function getRouteConfig(pathname: string): RouteTitleConfig {
  const exactMatch = ROUTE_TITLES[pathname];
  if (exactMatch) {
    if (typeof exactMatch === 'function') return exactMatch(pathname);
    return exactMatch;
  }
  for (const [pattern, config] of Object.entries(ROUTE_TITLES)) {
    if (pattern.endsWith('/') && pathname.startsWith(pattern)) {
      if (typeof config === 'function') return config(pathname);
      return config;
    }
  }
  return { title: 'SHALLWE', hideBack: true };
}

function isClassDetailPath(pathname: string): boolean {
  // '/classes/[id]' 형태만 매칭 (하위 경로는 제외)
  return /^\/classes\/[^/]+$/.test(pathname);
}

interface AppBarProps {
  className?: string;
}

export function AppBar({ className }: AppBarProps) {
  const router = useRouter();
  const pathname = usePathname();

  const routeConfig = useMemo(() => getRouteConfig(pathname), [pathname]);
  const title = routeConfig.title;

  // Hide-on-scroll without re-renders (perf): toggle data attributes on header
  const headerRef = useRef<HTMLElement | null>(null);
  const lastScrollYRef = useRef(0);
  const tickingRef = useRef(false);
  const hiddenRef = useRef(false);

  useEffect(() => {
    // 클래스 상세 페이지에서는 이벤트 리스너 불필요
    if (isClassDetailPath(pathname)) return;

    const tolerance = 8; // 작은 스크롤 변화 무시 (iOS 바운스 대비 소폭 증가)
    const hideThreshold = 56; // 임계치 상향으로 iOS 주소창 변동 노이즈 완화

    const clampScrollY = () => {
      const doc = document.scrollingElement || document.documentElement;
      const rawY = window.scrollY ?? doc.scrollTop ?? 0;
      const maxY = Math.max((doc.scrollHeight || 0) - window.innerHeight, 0);
      // iOS 바운스: 음수 및 최대 초과값 모두 클램프
      return Math.min(Math.max(rawY, 0), maxY);
    };

    const applyState = (currentY: number, delta: number) => {
      const header = headerRef.current;
      if (!header) return;

      // data-scrolled: 상단에서 벗어났는지 여부
      const scrolled = currentY > 4;
      if (header.dataset.scrolled !== String(scrolled)) {
        header.dataset.scrolled = String(scrolled);
      }

      // 방향 기반 노출/비노출
      const isScrollingDown = delta > 0;
      if (isScrollingDown && currentY > hideThreshold) {
        if (!hiddenRef.current) {
          hiddenRef.current = true;
          header.dataset.hidden = 'true';
        }
      } else {
        if (hiddenRef.current) {
          hiddenRef.current = false;
          header.dataset.hidden = 'false';
        }
      }
    };

    const onScroll = () => {
      if (tickingRef.current) return;
      tickingRef.current = true;
      window.requestAnimationFrame(() => {
        const currentY = clampScrollY();
        const delta = currentY - lastScrollYRef.current;
        if (Math.abs(delta) >= tolerance || currentY <= 0) {
          // 맨 위 근처(iOS 바운스 포함)에서는 항상 표시
          if (currentY <= 0 && hiddenRef.current) {
            hiddenRef.current = false;
            if (headerRef.current) headerRef.current.dataset.hidden = 'false';
          } else {
            applyState(currentY, delta);
          }
          lastScrollYRef.current = currentY;
        }
        tickingRef.current = false;
      });
    };

    // 초기 상태 동기화
    lastScrollYRef.current = clampScrollY();
    if (headerRef.current) {
      headerRef.current.dataset.scrolled = String(lastScrollYRef.current > 4);
      headerRef.current.dataset.hidden = 'false';
      hiddenRef.current = false;
    }

    window.addEventListener('scroll', onScroll, { passive: true });
    window.addEventListener('orientationchange', onScroll, { passive: true });
    window.addEventListener('resize', onScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', onScroll);
      window.removeEventListener('orientationchange', onScroll);
      window.removeEventListener('resize', onScroll);
    };
  }, [pathname]);

  // '/classes/[id]' 페이지 또는 hideAppBar 설정된 경우 AppBar 비노출
  if (isClassDetailPath(pathname) || routeConfig.hideAppBar) {
    return null;
  }

  const isRootPath = pathname === '/';
  const isProfilePath = pathname === '/profile';
  // const isAuthenticated = Boolean(user);
  const isBrandTitle = title === 'SHALLWE';
  const showBackButton = !routeConfig.hideBack && !isRootPath; // root overrides
  // const showHomeButton = !isRootPath;
  const showProfileButton = !routeConfig.hideProfile && !isProfilePath;

  const handleBackClick = () => {
    router.back();
  };

  const handleProfileClick = () => {
    router.push('/profile');
  };

  const handleTitleClick = () => {
    router.push('/');
  };

  return (
    <header
      ref={headerRef}
      className={cn(
        'sticky top-0 z-[49] h-14 w-full border-b border-gray-200 bg-white',
        'flex items-center justify-between px-4',
        'transition-transform duration-300 ease-out will-change-transform',
        // 데이터 속성으로 제어 (렌더 없이 토글)
        'data-[hidden=false]:translate-y-0 data-[hidden=true]:-translate-y-full',
        'data-[scrolled=true]:shadow-sm',
        className
      )}
    >
      <div className='flex items-center gap-3'>
        {showBackButton && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBackClick}
            className='h-8 w-8 p-2'
          >
            <ArrowLeft className='h-4 w-4' />
            <span className='sr-only'>뒤로가기</span>
          </Button>
        )}
      </div>

      <h1
        className={cn(
          'logo absolute left-1/2 -translate-x-1/2 transform cursor-pointer text-lg font-bold',
          !isBrandTitle && 'text-black'
        )}
        onClick={handleTitleClick}
      >
        {title}
      </h1>

      <div className='flex items-center gap-2'>
        {/* {showHomeButton && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleTitleClick}
            className='h-8 w-8 p-2'
          >
            <Home className='h-4 w-4' />
            <span className='sr-only'>홈</span>
          </Button>
        )} */}
        {showProfileButton && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleProfileClick}
            className='h-8 w-8 p-2'
          >
            <User className='h-4 w-4' />
            <span className='sr-only'>프로필</span>
          </Button>
        )}
      </div>
    </header>
  );
}
