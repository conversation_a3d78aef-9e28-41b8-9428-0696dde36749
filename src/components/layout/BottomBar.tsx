'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Calendar, CreditCard } from 'lucide-react';

interface BottomBarAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline';
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface BottomBarProps {
  actions: BottomBarAction[];
  className?: string;
}

export function BottomBar({ actions, className }: BottomBarProps) {
  if (actions.length === 0) return null;
  return (
    <>
      <div
        className={cn(
          'fixed right-0 bottom-0 left-0 z-[50]',
          'mx-auto w-full max-w-3xl py-2',
          'border-t border-[#eeeeee] bg-white px-3',
          className
        )}
      >
        <div className='flex h-full items-center'>
          {actions.length === 1 ? (
            <Button
              onClick={actions[0].onClick}
              className='h-12 w-full text-base font-medium'
              variant={actions[0].variant || 'default'}
              disabled={actions[0].disabled}
            >
              {actions[0].icon}
              {actions[0].label}
            </Button>
          ) : (
            <div className='flex w-full gap-3'>
              {actions.map((action, index) => (
                <Button
                  key={index}
                  onClick={action.onClick}
                  className={cn(
                    'h-12 flex-1 text-base font-medium',
                    'flex items-center justify-center gap-2'
                  )}
                  variant={
                    action.variant ||
                    (index === actions.length - 1 ? 'default' : 'outline')
                  }
                  disabled={action.disabled}
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}

// Predefined action configs for common use cases
export const bottomBarConfigs = {
  classDetail: (onViewSchedule: () => void) => [
    {
      label: '수업일정 보기',
      onClick: onViewSchedule,
      variant: 'default' as const,
      icon: <Calendar className='h-4 w-4' />,
    },
  ],

  booking: (onBook: () => void) => [
    {
      label: '예약하기',
      onClick: onBook,
      variant: 'default' as const,
      icon: <CreditCard className='h-4 w-4' />,
    },
  ],
};
