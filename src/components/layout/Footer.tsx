import { configManager } from '@/lib/config/config-manager';
import { PRIVACY_POLICY_URL, TERMS_OF_SERVICE_URL } from '@/lib/constants/urls';
import Link from 'next/link';

export default function Footer() {
  const email = configManager.getValue('client.email');
  return (
    <footer className='flex h-[250px] flex-col gap-2 bg-neutral-50 px-3 pt-4 pb-5'>
      <h1 className='logo text-lg font-bold'>SHELLWE</h1>
      <div className='flex flex-1 flex-col justify-between text-sm text-gray-500'>
        <div className='flex flex-col gap-4'>
          <div className='flex items-center gap-2'>
            <span className='font-bold'>고객센터</span>
            <span>010-2765-6005</span>
          </div>
          <div className='flex flex-col gap-1'>
            <div className='font-bold'>(주)팀오버플로잉 | 대표 이동녘</div>
            <div className='flex items-center gap-2'>
              <span className='font-bold'>사업자 등록번호</span>
              <span>270-81-02769</span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-bold'>주소</span>
              <span>
                서울 강남구 영동대로 602, 6층 22호 (삼성동, 미켈란107)
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <span className='font-bold'>메일 문의</span>
              <a href={`mailto:${email}`}>{email}</a>
            </div>
          </div>
        </div>
        <div className='flex items-center gap-2'>
          <Link href={TERMS_OF_SERVICE_URL}>이용약관</Link>
          <Link href={PRIVACY_POLICY_URL}>개인정보 처리방침</Link>
        </div>
      </div>
    </footer>
  );
}
