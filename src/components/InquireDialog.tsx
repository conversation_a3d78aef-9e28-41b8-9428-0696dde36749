'use client';

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { inquiriesApi } from '@/lib/api/inquiries';
import { CreateInquirySchema } from '@/schemas/inquiry';
import { useUIStore } from '@/contexts/ui.store';

export function InquireDialog() {
  const [content, setContent] = useState('');
  const [error, setError] = useState<string | null>(null);
  const open = useUIStore(state => state.openMap.InquireDialog);
  const setOpen = useUIStore(state => state.setOpen);

  const mutation = useMutation({
    mutationFn: (body: string) => inquiriesApi.create(body),
    onSuccess: response => {
      if (response.success) {
        resetForm();
        setOpen('InquireDialog', false);
      } else {
        setError(response.error || '문의 전송에 실패했습니다.');
      }
    },
    onError: err => {
      console.error('문의 전송 오류:', err);
      setError('문의 전송 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.');
    },
  });

  // 내용 검증
  const isContentValid = content.trim().length > 0;
  const isSubmitDisabled = !isContentValid || mutation.isPending;

  // 폼 리셋
  const resetForm = () => {
    setContent('');
    setError(null);
  };

  // 다이얼로그 닫기
  const handleClose = () => {
    if (!mutation.isPending) {
      resetForm();
      setOpen('InquireDialog', false);
    }
  };

  // 문의 제출
  const handleSubmit = async () => {
    if (isSubmitDisabled) return;

    // 클라이언트 사이드 검증
    const validation = CreateInquirySchema.safeParse({
      content: content.trim(),
    });
    if (!validation.success) {
      setError(
        validation.error.issues[0]?.message || '유효하지 않은 입력입니다.'
      );
      return;
    }

    setError(null);
    await mutation.mutateAsync(content.trim());
  };

  // 엔터 키 처리 (Shift+Enter는 줄바꿈, Enter는 전송)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={nextOpen => {
        if (nextOpen) {
          setOpen('InquireDialog', true);
        } else {
          handleClose();
        }
      }}
    >
      <DialogContent className='max-w-3xl'>
        <DialogHeader>
          <DialogTitle className='text-lg font-semibold'>
            쉘위에 문의하기
          </DialogTitle>
          <DialogDescription className='text-sm text-gray-600'>
            회원님이 남겨주신 문의는 빠르게 확인 후 카카오톡 또는 문자 메시지로
            답변 드릴게요.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-4'>
          {/* 문의 내용 입력 */}
          <div className='space-y-2'>
            <Textarea
              value={content}
              onChange={e => setContent(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder='문의하실 내용을 입력해주세요...'
              className='min-h-[120px] resize-none'
              maxLength={5000}
              disabled={mutation.isPending}
            />
            <div className='flex justify-between text-xs text-gray-500'>
              <span>{content.length}/5000</span>
              <span>Shift+Enter: 줄바꿈, Enter: 전송</span>
            </div>
          </div>

          {/* 에러 메시지 */}
          {error && (
            <div className='rounded-md bg-red-50 p-3 text-sm text-red-600'>
              {error}
            </div>
          )}

          {/* 문의 보내기 버튼 */}
          <Button
            onClick={handleSubmit}
            disabled={isSubmitDisabled}
            className='w-full'
            size='lg'
          >
            {mutation.isPending ? (
              <>
                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                전송 중...
              </>
            ) : (
              '문의 보내기'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
