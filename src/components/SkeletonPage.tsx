import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

type SkeletonKind =
  | 'profile'
  | 'class-list'
  | 'class-detail'
  | 'dashboard'
  | 'generic';

type SkeletonPageProps = {
  page: SkeletonKind;
  className?: string;
};

function ProfileSkeleton() {
  return (
    <div className='mx-auto w-full space-y-4 p-4'>
      <div className='rounded-xl border p-4'>
        <div className='mb-4 flex items-center gap-3'>
          <Skeleton className='h-12 w-12 rounded-full' />
          <div className='flex-1 space-y-2'>
            <Skeleton className='h-4 w-1/3' />
            <Skeleton className='h-3 w-1/4' />
          </div>
        </div>
        <div className='grid grid-cols-3 gap-3'>
          <Skeleton className='h-16 rounded-lg' />
          <Skeleton className='h-16 rounded-lg' />
          <Skeleton className='h-16 rounded-lg' />
        </div>
      </div>
      <div className='rounded-xl border p-4'>
        <Skeleton className='mb-3 h-4 w-24' />
        <div className='space-y-3'>
          <Skeleton className='h-20 rounded-lg' />
          <Skeleton className='h-20 rounded-lg' />
          <Skeleton className='h-20 rounded-lg' />
        </div>
      </div>
    </div>
  );
}

function ClassListSkeleton() {
  return (
    <div className='mx-auto w-full max-w-3xl space-y-4 p-4'>
      <Skeleton className='h-8 w-40' />
      <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className='space-y-3 rounded-xl border p-3'>
            <Skeleton className='h-32 w-full rounded-lg' />
            <Skeleton className='h-4 w-3/4' />
            <Skeleton className='h-3 w-1/2' />
          </div>
        ))}
      </div>
    </div>
  );
}

function ClassDetailSkeleton() {
  return (
    <div className='mx-auto w-full max-w-3xl space-y-6 p-4'>
      <Skeleton className='h-56 w-full rounded-xl' />
      <div className='space-y-3'>
        <Skeleton className='h-6 w-1/3' />
        <Skeleton className='h-4 w-2/3' />
      </div>
      <div className='grid grid-cols-3 gap-3'>
        <Skeleton className='h-10 rounded-lg' />
        <Skeleton className='h-10 rounded-lg' />
        <Skeleton className='h-10 rounded-lg' />
      </div>
      <div className='space-y-2'>
        {Array.from({ length: 5 }).map((_, i) => (
          <Skeleton key={i} className='h-4 w-full' />
        ))}
      </div>
    </div>
  );
}

function DashboardSkeleton() {
  return (
    <div className='mx-auto w-full max-w-5xl space-y-6 p-4'>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
        <Skeleton className='h-24 rounded-xl' />
        <Skeleton className='h-24 rounded-xl' />
        <Skeleton className='h-24 rounded-xl' />
      </div>
      <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
        <Skeleton className='h-64 rounded-xl' />
        <Skeleton className='h-64 rounded-xl' />
      </div>
    </div>
  );
}

function GenericSkeleton() {
  return (
    <div className='mx-auto w-full max-w-2xl space-y-4 p-4'>
      <Skeleton className='h-6 w-2/5' />
      <Skeleton className='h-4 w-3/5' />
      <Skeleton className='h-24 w-full rounded-xl' />
      <div className='grid grid-cols-3 gap-3'>
        <Skeleton className='h-12 rounded-lg' />
        <Skeleton className='h-12 rounded-lg' />
        <Skeleton className='h-12 rounded-lg' />
      </div>
    </div>
  );
}

export default function SkeletonPage({ page, className }: SkeletonPageProps) {
  return (
    <div className={cn('w-full', className)}>
      {page === 'profile' && <ProfileSkeleton />}
      {page === 'class-list' && <ClassListSkeleton />}
      {page === 'class-detail' && <ClassDetailSkeleton />}
      {page === 'dashboard' && <DashboardSkeleton />}
      {page === 'generic' && <GenericSkeleton />}
    </div>
  );
}
