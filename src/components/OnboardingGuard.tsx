'use client';

import { useEffect, useMemo, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { STORAGE_KEYS } from '@/lib/constants/onboarding';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export default function OnboardingGuard({ children }: OnboardingGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isChecking, setIsChecking] = useState(true);

  // 온보딩, 인증 등 특정 경로는 가드 제외
  const excludedPaths = useMemo(
    () => [
      '/onboarding',
      '/onboarding/complete',
      '/partner',
      '/login',
      '/signup',
      '/auth',
    ],
    []
  );

  // 쿼리 파라미터 의존성을 최소화하기 위한 파생 값
  const onboardingParam = searchParams.get('onboarding');

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      const forceOnboarding = onboardingParam === 'true';
      if (
        forceOnboarding &&
        !pathname.startsWith('/onboarding') &&
        pathname === '/'
      ) {
        router.replace('/onboarding');
        return;
      }

      // 온보딩 관련 페이지나 파트너 페이지는 체크 제외
      if (
        excludedPaths.some(path => pathname.startsWith(path)) ||
        pathname !== '/'
      ) {
        setIsChecking(false);
        return;
      }

      try {
        // 로컬스토리지에서 온보딩 완료 상태를 엄격 비교로 확인
        const onboardingCompletedValue = localStorage.getItem(
          STORAGE_KEYS.ONBOARDING_COMPLETED
        );
        const isOnboardingCompleted = onboardingCompletedValue === 'true';

        if (!isOnboardingCompleted) {
          // 온보딩 미완료 시 온보딩 페이지로 리다이렉트
          router.replace('/onboarding');
          return;
        }
      } catch (error) {
        console.error('온보딩 상태 확인 오류:', error);
        // 오류 시에도 진행 허용 (네트워크 오류 등)
      } finally {
        setIsChecking(false);
      }
    };

    // if (pathname! == '/') {
    //   return;
    // }

    checkOnboardingStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, onboardingParam, excludedPaths]);

  // 온보딩 완료 시 URL에서 onboarding 파라미터 제거
  useEffect(() => {
    if (pathname === '/onboarding/complete') {
      const currentParams = new URLSearchParams(searchParams.toString());
      if (currentParams.has('onboarding')) {
        currentParams.delete('onboarding');
        const newUrl = currentParams.toString()
          ? `${pathname}?${currentParams.toString()}`
          : pathname;
        router.replace(newUrl);
      }
    }
  }, [pathname, searchParams, router]);

  return (
    <>
      {children}
      {isChecking && (
        <div className='fixed inset-0 z-[60] bg-white/40 backdrop-blur-sm' />
      )}
    </>
  );
}
