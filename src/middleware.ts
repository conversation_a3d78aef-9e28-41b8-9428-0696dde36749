import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createSupabaseClient } from './lib/supabase/server';

export async function middleware(request: NextRequest) {
  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const pathname = request.nextUrl.pathname;

  // 정적 파일과 API는 미들웨어 건너뛰기
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/auth') ||
    pathname === '/favicon.ico' ||
    pathname.endsWith('.jpg') ||
    pathname.endsWith('.jpeg') ||
    pathname.endsWith('.png') ||
    pathname.endsWith('.gif') ||
    pathname.endsWith('.svg') ||
    pathname.endsWith('.webp')
  ) {
    return response;
  }

  // 공개 경로들 - 인증 불필요
  const publicPaths = [
    '/',
    '/login',
    '/register',
    '/signup',
    '/onboarding',
    '/partner/login',
    '/partner/register',
    '/partner/register/complete',
    '/partner/suspended',
    '/unauthorized',
    '/terms',
    '/privacy',
    '/docs/terms',
    '/docs/privacy-policy',
  ];

  // 공개 경로는 인증 체크 없이 통과
  if (publicPaths.includes(pathname)) {
    return response;
  }

  // 클래스 관련 페이지는 모두 공개 (상세, booking 포함)
  if (pathname.startsWith('/classes/')) {
    return response;
  }

  const supabase = await createSupabaseClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (process.env.NODE_ENV === 'development') {
      console.log('supabase.user::', user);
    }

    // 인증되지 않은 사용자 처리
    if (error || !user) {
      // Partner 경로는 partner 로그인으로
      if (pathname.startsWith('/partner')) {
        return NextResponse.redirect(new URL('/partner/login', request.url));
      }
      // 일반 경로는 일반 로그인으로
      const loginUrl = new URL('/login', request.url);
      // 기존 쿼리까지 보존하여 로그인 후 정확한 위치로 복귀
      const originalSearch = request.nextUrl.search || '';
      const destination = `${pathname}${originalSearch}`;
      loginUrl.searchParams.set('redirectTo', destination);
      return NextResponse.redirect(loginUrl);
    }

    // 회원 필수 정보 확인 비활성화 (임시)
    // if (!pathname.startsWith('/partner') && pathname !== '/signup/required') {
    //   // 파트너 계정인지 먼저 확인
    //   const { data: partner } = await supabase
    //     .from('partners')
    //     .select('id')
    //     .eq('user_id', user.id)
    //     .single();

    //   // 파트너가 아닌 경우에만 회원 필수 정보 확인
    //   if (!partner) {
    //     const { data: member } = await supabase
    //       .from('members')
    //       .select('name, phone, birth_date, gender, agreements')
    //       .eq('id', user.id)
    //       .single();

    //     // 필수 정보가 없으면 정보 입력 페이지로
    //     if (
    //       !member?.name ||
    //       !member?.phone ||
    //       !member?.birth_date ||
    //       !member?.gender ||
    //       !member?.agreements
    //     ) {
    //       return NextResponse.redirect(
    //         new URL('/signup/required', request.url)
    //       );
    //     }
    //   }
    // }

    // Partner 경로 권한 확인
    if (pathname.startsWith('/partner')) {
      const { data: partner } = await supabase
        .from('partners')
        .select('id, status')
        .eq('user_id', user.id)
        .single();

      if (!partner) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }

      // 파트너 상태별 리다이렉트 처리
      if (partner.status === 'PENDING') {
        // PENDING 상태는 register/complete 페이지로
        if (pathname !== '/partner/register/complete') {
          return NextResponse.redirect(
            new URL('/partner/register/complete', request.url)
          );
        }
      } else if (
        partner.status === 'SUSPENDED' ||
        partner.status === 'REJECTED'
      ) {
        // SUSPENDED, REJECTED 상태는 suspended 페이지로
        if (pathname !== '/partner/suspended') {
          return NextResponse.redirect(
            new URL('/partner/suspended', request.url)
          );
        }
      } else if (partner.status === 'ACTIVE') {
        // ACTIVE 상태만 다른 파트너 경로 접근 가능
        // 상태 페이지에 접근하려 하면 대시보드로 리다이렉트
        if (
          pathname === '/partner/register/complete' ||
          pathname === '/partner/suspended'
        ) {
          return NextResponse.redirect(
            new URL('/partner/dashboard', request.url)
          );
        }
      } else {
        // 알 수 없는 상태는 suspended 페이지로
        return NextResponse.redirect(
          new URL('/partner/suspended', request.url)
        );
      }
    }

    return response;
  } catch (error) {
    console.error('미들웨어 오류:', error);
    return response;
  }
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'],
};
