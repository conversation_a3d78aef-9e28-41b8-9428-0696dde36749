import { createSupabaseClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const token_hash = requestUrl.searchParams.get('token_hash');
  const type = requestUrl.searchParams.get('type');
  const error = requestUrl.searchParams.get('error');
  const errorDescription = requestUrl.searchParams.get('error_description');
  const redirectTo = requestUrl.searchParams.get('redirect_to') || '/';

  if (error) {
    return NextResponse.redirect(
      new URL(
        `/login?error=${error}&description=${encodeURIComponent(errorDescription || '')}`,
        requestUrl.origin
      )
    );
  }

  const supabase = await createSupabaseClient();
  let authData: any = null;

  // 1. 이메일 확인 링크 처리 (token_hash + type)
  if (token_hash && type) {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        token_hash,
        type: type as any,
      });

      if (error) {
        return NextResponse.redirect(
          new URL(
            `/login?error=email_verification_failed&details=${encodeURIComponent(error.message)}`,
            requestUrl.origin
          )
        );
      }

      authData = data;
    } catch (error) {
      return NextResponse.redirect(
        new URL('/login?error=email_verification_failed', requestUrl.origin)
      );
    }
  }
  // 2. OAuth 코드 교환 처리 (code) - PKCE 오류 시 이메일 인증으로 fallback
  else if (code) {
    try {
      // 먼저 OAuth PKCE 코드 교환 시도
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        // PKCE 관련 오류인 경우 이메일 인증으로 판단하고 세션 확인 시도
        if (
          error.message.includes('code verifier') ||
          error.message.includes(
            'both auth code and code verifier should be non-empty'
          ) ||
          error.code === 'invalid_request'
        ) {
          // 1차: 현재 세션 확인 시도
          const { data: sessionData, error: sessionError } =
            await supabase.auth.getSession();

          if (!sessionError && sessionData.session) {
            authData = {
              user: sessionData.session.user,
              session: sessionData.session,
            };
          } else {
            // 2차: 현재 사용자 정보 확인 시도 (세션 없이도 사용자 정보가 있을 수 있음)
            const { data: userData, error: userError } =
              await supabase.auth.getUser();

            if (!userError && userData.user) {
              // 세션은 없지만 사용자가 있는 경우 (이메일 인증 완료 상태)
              authData = { user: userData.user };
            } else {
              // 3차: 직접 인증 시도 (코드를 통한 수동 처리)
              try {
                // URL에서 추가 파라미터 확인
                const token = requestUrl.searchParams.get('token');
                const access_token =
                  requestUrl.searchParams.get('access_token');
                const refresh_token =
                  requestUrl.searchParams.get('refresh_token');

                // 이메일 인증이 성공했지만 세션이 없는 경우, 파트너 회원가입 완료로 간주
                if (redirectTo.includes('/partner/register/complete')) {
                  return NextResponse.redirect(
                    new URL('/partner/register/complete', requestUrl.origin)
                  );
                }
              } catch (directAuthError) {}

              return NextResponse.redirect(
                new URL(
                  `/login?error=email_verification_failed&details=${encodeURIComponent('이메일 인증이 완료되지 않았습니다. 다시 시도해주세요.')}`,
                  requestUrl.origin
                )
              );
            }
          }
        } else {
          // 다른 OAuth 오류인 경우
          return NextResponse.redirect(
            new URL(
              `/login?error=auth_failed&details=${encodeURIComponent(error.message)}`,
              requestUrl.origin
            )
          );
        }
      } else {
        // OAuth 로그인 성공
        authData = data;
      }
    } catch (error) {
      return NextResponse.redirect(
        new URL('/login?error=code_processing_failed', requestUrl.origin)
      );
    }
  }

  // 3. 인증 데이터가 있으면 사용자 처리
  if (authData?.user) {
    const user = authData.user;
    try {
      // 메타데이터에서 역할 확인 (파트너 이메일 가입용)
      const userRole = user.user_metadata?.role;

      if (userRole === 'partner') {
        return NextResponse.redirect(
          new URL('/partner/register/complete', requestUrl.origin)
        );
      }

      // 파트너 테이블에서 확인 (기존 파트너 또는 카카오 로그인)
      const { data: partner } = await supabase
        .from('partners')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (partner) {
        // 파트너인 경우 파트너 대시보드로 리다이렉트
        return NextResponse.redirect(
          new URL('/partner/dashboard', requestUrl.origin)
        );
      } else {
        // 회원인 경우에만 필수 정보 확인
        let { data: member, error: memberError } = await supabase
          .from('members')
          .select('name, phone, birth_date, gender, agreements')
          .eq('id', user.id)
          .single();

        console.error('auth/callback: member query error:', memberError);

        // 회원 데이터가 없으면 기본 레코드 생성
        // References: https://docs.postgrest.org/en/stable/references/errors.html#postgrest-error-codes

        if (memberError && memberError.code === 'PGRST116') {
          const kakaoUserInfo = user.user_metadata;
          const { data: newMember, error: insertError } = await supabase
            .from('members')
            .insert({
              id: user.id,
              nickname: kakaoUserInfo?.name || kakaoUserInfo?.full_name || null,
              // TODO: Check `role` field
              role: 'STUDENT',
              status: 'ACTIVE',
            })
            .select('name, phone, birth_date, gender, agreements')
            .single();

          if (insertError) {
            return NextResponse.redirect(
              new URL('/login?error=member_creation_failed', requestUrl.origin)
            );
          }

          member = newMember;
        } else if (memberError) {
          return NextResponse.redirect(
            new URL('/login?error=member_query_failed', requestUrl.origin)
          );
        }

        // 필수 정보가 없으면 정보 입력 페이지로
        if (
          !member?.name ||
          !member?.phone ||
          !member?.birth_date ||
          !member?.gender ||
          !member?.agreements
        ) {
          return NextResponse.redirect(
            new URL('/signup/required', requestUrl.origin)
          );
        }
      }

      // 마지막 로그인 시간 업데이트
      await supabase.auth.updateUser({
        data: {
          last_login: new Date().toISOString(),
        },
      });

      // 성공적으로 로그인 후 지정된 경로로 리다이렉트
      return NextResponse.redirect(new URL(redirectTo, requestUrl.origin));
    } catch (error) {
      return NextResponse.redirect(
        new URL('/login?error=callback_failed', requestUrl.origin)
      );
    }
  }

  // 코드나 토큰이 없거나 처리 실패 시 로그인 페이지로 리다이렉트
  return NextResponse.redirect(
    new URL('/login?error=no_code_or_token', requestUrl.origin)
  );
}
