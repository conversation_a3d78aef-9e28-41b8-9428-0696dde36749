@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.5rem;
  --background: #fafafa;
  --foreground: #09090b;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #09090b;
  --primary: #6114cc;
  --primary-dark: #5000d0;
  --primary-foreground: #ffffff;
  --secondary: #f4f4f5;
  --secondary-foreground: #18181b;
  --muted: #f1f5f9;
  --muted-foreground: #000000;
  --accent: #f4f4f5;
  --accent-foreground: #272735;
  --destructive: #e7000b;
  --border: #e4e4e7;
  --input: #e4e4e7;
  --ring: #6114cc;
  --chart-1: #f54900;
  --chart-2: #009689;
  --chart-3: #104e64;
  --chart-4: #ffb900;
  --chart-5: #fe9a00;
  --sidebar: #ffffff;
  --sidebar-foreground: #09090b;
  --sidebar-primary: #6114cc;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f4f4f5;
  --sidebar-accent-foreground: #18181b;
  --sidebar-border: #e4e4e7;
  --sidebar-ring: #6114cc;

  --shadow-color: #000000;
  --shadow-2xs: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05);
  --shadow-xs: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.05);
  --shadow-sm:
    0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1),
    0 1px 2px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow:
    0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1),
    0 1px 2px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-md:
    0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1),
    0 2px 4px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-lg:
    0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1),
    0 4px 6px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-xl:
    0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1),
    0 8px 10px -1px rgb(from var(--shadow-color) r g b / 0.1);
  --shadow-2xl: 0 1px 3px 0px rgb(from var(--shadow-color) r g b / 0.1);

  /* Custom */
  --kakao: #f9df4a;
}

.dark {
  --background: #18181b;
  --foreground: #fafafa;
  --card: #09090b;
  --card-foreground: #fafafa;
  --popover: #18181b;
  --popover-foreground: #fafafa;
  --primary: #6114cc;
  --primary-foreground: #ffa6ac;
  --secondary: #27272a;
  --secondary-foreground: #fafafa;
  --muted: #27272a;
  --muted-foreground: #9f9fa9;
  --accent: #27272a;
  --accent-foreground: #fafafa;
  --destructive: #ff6467;
  --border: #ffffff1a;
  --input: #ffffff26;
  --ring: #6114cc;
  --chart-1: #1447e6;
  --chart-2: #00bc7d;
  --chart-3: #fe9a00;
  --chart-4: #ad46ff;
  --chart-5: #ff2056;
  --sidebar: #09090b;
  --sidebar-foreground: #fafafa;
  --sidebar-primary: #6114cc;
  --sidebar-primary-foreground: #ffa6ac;
  --sidebar-accent: #27272a;
  --sidebar-accent-foreground: #fafafa;
  --sidebar-border: #ffffff1a;
  --sidebar-ring: #6114cc;

  --shadow-color: #000000;
}

@theme inline {
  --font-sans:
    system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  --font-mono:
    'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  --font-serif: Georgia, 'Times New Roman', Times, serif;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  /* Custom */
  --color-kakao: var(--kakao);
  --color-placeholder: #737373;
  --bottom-nav-height: 50px;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .logo {
    @apply bg-gradient-to-r from-[#5530FF] to-[#9D43FF] bg-clip-text text-transparent;
  }

  .gradient-bg {
    @apply bg-gradient-to-r from-[#5000D0] to-[#8645EF] transition-colors hover:from-[#5000D0]/80 hover:to-[#8645EF]/80;
  }
}

.prose {
  color: #374151; /* gray-700 */
  max-width: 65ch;
  line-height: 1.75;
  font-size: 1rem;
}

/* 헤딩 */
.prose h1 {
  font-size: 2.25rem; /* text-4xl */
  line-height: 2.5rem;
  font-weight: 700;
  margin: 2em 0 1em;
}
.prose h2 {
  font-size: 1.5rem; /* text-2xl */
  line-height: 2rem;
  font-weight: 600;
  margin: 1.5em 0 0.75em;
}
.prose h3 {
  font-size: 1.25rem; /* text-xl */
  font-weight: 600;
  margin: 1.25em 0 0.5em;
}
.prose h4 {
  font-size: 1.125rem; /* text-lg */
  font-weight: 600;
  margin: 1em 0 0.5em;
}

/* 본문 */
.prose p {
  margin: 1em 0;
}

/* 링크 */
.prose a {
  color: #2563eb; /* blue-600 */
  text-decoration: underline;
}
.prose a:hover {
  color: #1d4ed8; /* blue-700 */
}

/* 목록 */
.prose ul,
.prose ol {
  margin: 1em 0;
  padding-left: 1.5em;
}
.prose ul li {
  list-style-type: disc;
  margin: 0.25em 0;
}
.prose ol li {
  list-style-type: decimal;
  margin: 0.25em 0;
}

/* 인용구 */
.prose blockquote {
  border-left: 4px solid #d1d5db; /* gray-300 */
  padding-left: 1em;
  color: #4b5563; /* gray-600 */
  font-style: italic;
  margin: 1em 0;
}

/* 코드 */
.prose code {
  background-color: #f3f4f6; /* gray-100 */
  padding: 0.25em 0.5em;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}
.prose pre {
  background-color: #1f2937; /* gray-800 */
  color: #f9fafb; /* gray-50 */
  padding: 1em;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1em 0;
}

/* 이미지 */
.prose img {
  border-radius: 0.5rem;
  margin: 1em 0;
  max-width: 100%;
  height: auto;
}

/* 테이블 */
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}
.prose th,
.prose td {
  border: 1px solid #d1d5db; /* gray-300 */
  padding: 0.5em 0.75em;
  text-align: left;
}
.prose th {
  background-color: #f9fafb; /* gray-50 */
  font-weight: 600;
}
