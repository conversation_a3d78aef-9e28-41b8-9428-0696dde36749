import { NextRequest, NextResponse } from 'next/server';
import { classService } from '@/lib/services/class.service';
import { ClassDetailResponseSchema } from '@/app/api/classes/schema';
import { z } from 'zod';

/**
 * 클래스 상세 조회 API
 * GET /api/classes/{id}
 *
 * @description
 * - 특정 클래스의 상세 정보를 조회
 * - 스튜디오, 강사 정보를 포함하여 반환
 * - 활성화되고 공개된 클래스만 조회 가능
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Validate UUID
    const uuidSchema = z.uuid();
    const validationResult = uuidSchema.safeParse(id);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid class ID format' },
        { status: 400 }
      );
    }

    // Get class detail
    const classDetail = await classService.getClassDetail(id);

    if (!classDetail) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 });
    }

    // Validate response
    const validatedResponse = ClassDetailResponseSchema.parse(classDetail);

    return NextResponse.json(validatedResponse);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Validation error:', error.issues);
      return NextResponse.json(
        { error: 'Invalid response data', details: error.issues },
        { status: 500 }
      );
    }

    console.error('Class detail fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
