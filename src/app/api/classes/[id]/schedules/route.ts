import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classes, class_schedule_groups, class_schedules, studios, instructors, enrollments, members } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * 클래스 스케줄 조회 API
 * GET /api/classes/{classId}/schedules
 * 
 * @description
 * - 특정 클래스의 스케줄 목록을 조회
 * - 인증 불필요 (비로그인 사용자도 조회 가능)
 * - 활성화되고 공개된 클래스만 조회 가능
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: classId } = await params;

    // 1. 클래스 상세 정보 및 스튜디오/강사 정보 조회
    const classWithDetails = await db
      .select({
        // 클래스 정보
        id: classes.id,
        title: classes.title,
        description: classes.description,
        category: classes.category,
        level: classes.level,
        target: classes.target,
        pricePerSession: classes.price_per_session,
        maxParticipants: classes.max_participants,
        sessionDurationMinutes: classes.session_duration_minutes,
        durationWeeks: classes.duration_weeks,
        sessionsPerWeek: classes.sessions_per_week,
        images: classes.images,
        // 스튜디오 정보
        studioId: studios.id,
        studioName: studios.name,
        studioAddress: studios.address,
        studioPhone: studios.phone,
        // 강사 정보
        instructorId: instructors.id,
        instructorName: instructors.name,
        instructorProfileImages: instructors.profileImages,
      })
      .from(classes)
      .innerJoin(studios, eq(classes.studio_id, studios.id))
      .innerJoin(instructors, eq(classes.instructor_id, instructors.id))
      .where(and(
        eq(classes.id, classId),
        eq(classes.status, 'active'),
        eq(classes.visible, true)
      ))
      .limit(1);

    if (!classWithDetails.length) {
      return NextResponse.json(
        { error: 'Class not found' },
        { status: 404 }
      );
    }

    const classData = classWithDetails[0];

    // 2. 스케줄 그룹과 스케줄 목록 조회
    const scheduleGroupsWithSchedules = await db
      .select({
        groupId: class_schedule_groups.id,
        groupStatus: class_schedule_groups.status,
        scheduleId: class_schedules.id,
        dayOfWeek: class_schedules.day_of_week,
        startTime: class_schedules.start_time,
        endTime: class_schedules.end_time,
      })
      .from(class_schedule_groups)
      .innerJoin(class_schedules, eq(class_schedule_groups.id, class_schedules.schedule_group_id))
      .where(eq(class_schedule_groups.class_id, classId))
      .orderBy(class_schedules.start_time, class_schedules.day_of_week);

    // 3. 현재 등록자 조회 (status가 'paid'인 경우만)
    const currentEnrollmentsData = await db
      .select({
        scheduleGroupId: enrollments.scheduleGroupId,
        memberId: members.id,
        memberName: members.name,
        memberGender: members.gender,
      })
      .from(enrollments)
      .innerJoin(members, eq(enrollments.memberId, members.id))
      .where(and(
        eq(enrollments.classId, classId),
        eq(enrollments.status, 'paid')
      ));

    // 4. 그룹별로 데이터 정리
    const groupsMap = new Map();
    const enrollmentsMap = new Map();
    
    // 등록자 데이터를 그룹별로 정리
    currentEnrollmentsData.forEach(enrollment => {
      if (!enrollmentsMap.has(enrollment.scheduleGroupId)) {
        enrollmentsMap.set(enrollment.scheduleGroupId, []);
      }
      enrollmentsMap.get(enrollment.scheduleGroupId).push({
        memberId: enrollment.memberId,
        name: enrollment.memberName,
        gender: enrollment.memberGender,
      });
    });
    
    scheduleGroupsWithSchedules.forEach(row => {
      if (!groupsMap.has(row.groupId)) {
        groupsMap.set(row.groupId, {
          id: row.groupId,
          status: row.groupStatus,
          schedules: [],
          currentEnrollments: enrollmentsMap.get(row.groupId) || []
        });
      }
      
      groupsMap.get(row.groupId).schedules.push({
        id: row.scheduleId,
        dayOfWeek: row.dayOfWeek,
        startTime: row.startTime,
        endTime: row.endTime,
      });
    });

    // 5. 응답 데이터 구성
    const response = {
      class: {
        id: classData.id,
        title: classData.title,
        description: classData.description,
        category: classData.category,
        level: classData.level,
        target: classData.target,
        pricePerSession: classData.pricePerSession,
        maxParticipants: classData.maxParticipants,
        sessionDurationMinutes: classData.sessionDurationMinutes,
        durationWeeks: classData.durationWeeks,
        sessionsPerWeek: classData.sessionsPerWeek,
        images: classData.images,
        studio: {
          id: classData.studioId,
          name: classData.studioName,
          address: classData.studioAddress,
          phone: classData.studioPhone,
        },
        instructor: {
          id: classData.instructorId,
          name: classData.instructorName,
          profileImages: classData.instructorProfileImages,
        },
      },
      scheduleGroups: Array.from(groupsMap.values())
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Schedule fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}