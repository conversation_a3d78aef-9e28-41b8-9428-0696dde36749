import { z } from 'zod';
import { AmenitiesSchema } from '@/lib/schemas/studio';

// Query parameters for class list
export const ClassListQuerySchema = z.object({
  nearestStation: z.union([z.string(), z.array(z.string())]).optional(),
  category: z.union([z.string(), z.array(z.string())]).optional(),
  level: z.union([z.string(), z.array(z.string())]).optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  sort: z.enum(['popular', 'latest']).default('popular'),
});

// Class list item response
export const ClassListItemSchema = z.object({
  id: z.uuid(),
  title: z.string(),
  description: z.string(),
  category: z.string(),
  level: z.string(),
  target: z.string(),
  maxParticipants: z.number(),
  pricePerSession: z.number(),
  sessionsPerWeek: z.number(),
  createdAt: z.string(),
  enrollmentCount: z.number(), // 신청자 수

  // Studio info
  studio: z.object({
    id: z.uuid(),
    name: z.string(),
    nearestStation: z.string().nullable(),
    stationDistance: z.number().nullable(),
  }),

  // Instructor info
  instructor: z.object({
    id: z.uuid(),
    name: z.string(),
    experienceTotalYears: z.number(),
  }),

  // Thumbnail
  thumbnailUrl: z.string().nullable(),
});

// Pagination info
export const PaginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
});

// Class list response
export const ClassListResponseSchema = z.object({
  classes: z.array(ClassListItemSchema),
  pagination: PaginationSchema,
});

export type ClassListQuery = z.infer<typeof ClassListQuerySchema>;
export type ClassListItem = z.infer<typeof ClassListItemSchema>;
export type ClassListResponse = z.infer<typeof ClassListResponseSchema>;

// Class detail response schema
export const ClassDetailResponseSchema = z.object({
  id: z.uuid(),
  title: z.string(),
  description: z.string(),
  category: z.string(),
  level: z.string(),
  target: z.string(),
  recommendedFor: z.array(z.string()), // 레벨별 추천 대상 목록
  maxParticipants: z.number(),
  pricePerSession: z.number(),
  sessionDurationMinutes: z.number(),
  durationWeeks: z.number(),
  sessionsPerWeek: z.number(),
  images: z
    .array(
      z.object({
        url: z.string(),
      })
    )
    .nullable(),

  // Studio info
  studio: z.object({
    id: z.uuid(),
    name: z.string(),
    nearestStation: z.string().nullable(),
    stationDistance: z.number().nullable(),
    images: z
      .array(
        z.object({
          url: z.string(),
          type: z.string().optional(),
          alt_text: z.string().optional(),
        })
      )
      .nullable(),
    links: z
      .object({
        website: z.string().optional(),
        sns: z.string().optional(),
      })
      .nullable(),
    description: z.string().nullable(),
    amenities: AmenitiesSchema.nullable(),
    address: z.string(),
    addressDetail: z.string().nullable(),
    phone: z.string(),
    latitude: z.string().nullable(),
    longitude: z.string().nullable(),
  }),

  // Instructor info
  instructor: z.object({
    id: z.uuid(),
    name: z.string(),
    experienceTotalYears: z.number(),
    description: z.string(),
    profileImage: z.string().nullable(),
    certificates: z
      .array(
        z.object({
          name: z.string(),
          issuing_organization: z.string().optional(),
          issue_date: z.string().optional(),
          expiry_date: z.string().optional(),
          certificate_number: z.string().optional(),
        })
      )
      .nullable(),
  }),

  createdAt: z.string().datetime(),
});

export type ClassDetailResponse = z.infer<typeof ClassDetailResponseSchema>;

const InstructorSpecialtySchema = z.object({
  specialty: z.string(),
  experienceYears: z.number(),
});

const InstructorCertificateSchema = z.object({
  certificateName: z.string(),
  issuingOrganization: z.string().optional(),
});

const ScheduleSchema = z.object({
  scheduleGroupId: z.string(),
  dayOfWeek: z.string(),
  startTime: z.string(),
  endTime: z.string(),
});

const ScheduleGroupSchema = z.object({
  id: z.string(),
  groupName: z.string(),
  maxParticipants: z.number(),
  pricePerSession: z.number().optional(),
  schedules: z.array(ScheduleSchema),
  currentEnrollments: z.number(),
  availableSpots: z.number(),
});

const EnrollmentInfoSchema = z.object({
  isAvailable: z.boolean(),
  reasonCode: z.enum([
    'RECRUITMENT_ENDED',
    'NOT_STARTED',
    'ONGOING',
    'COMPLETED',
    'CANCELLED',
    'AVAILABLE',
  ]),
  closeAt: z.string().optional(),
});

// Class List Item
export const classListItemSchema = z.object({
  classTemplate: z.object({
    id: z.string(),
    title: z.string(),
    category: z.string(),
    specialty: z.string(),
    level: z.string(),
    durationInMinutes: z.number(),
    pricePerSession: z.number(),
    maxCapacity: z.number(),
    status: z.string(),
  }),
  studio: z.object({
    id: z.string(),
    name: z.string(),
    nearestStation: z.string().nullish(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    shortBio: z.string().optional(),
    specialties: z.array(InstructorSpecialtySchema),
  }),
});

export type ClassListItemType = z.infer<typeof classListItemSchema>;
export type ClassTemplateType = ClassListItemType['classTemplate'];
export type StudioType = ClassListItemType['studio'];
export type InstructorType = ClassListItemType['instructor'];

// Class Detail
export const classDetailSchema = z.object({
  classTemplate: z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    specialty: z.string(),
    level: z.string(),
    durationMinutes: z.number(),
    pricePerSession: z.number(),
    curriculum: z.record(z.string(), z.any()).optional(),
    recruitmentEndDate: z.string(),
    classStartDate: z.string(),
    classEndDate: z.string(),
    status: z.string(),
    maxCapacity: z.number(),
  }),
  instructor: z.object({
    id: z.string(),
    name: z.string(),
    shortBio: z.string().optional(),
    specialties: z.array(InstructorSpecialtySchema),
    certificates: z.array(InstructorCertificateSchema),
  }),
  studio: z.object({
    id: z.string(),
    name: z.string(),
    address: z.string(),
    nearestStation: z.string().optional(),
    amenities: AmenitiesSchema,
    latitude: z.string(),
    longitude: z.string(),
    description: z.string(),
  }),
  scheduleGroups: z.array(ScheduleGroupSchema),
  enrollmentInfo: EnrollmentInfoSchema,
});

export type ClassDetailType = z.infer<typeof classDetailSchema>;
export type ClassDetailTemplateType = ClassDetailType['classTemplate'];
export type ClassDetailInstructorType = ClassDetailType['instructor'];
export type ClassDetailStudioType = ClassDetailType['studio'];
export type ScheduleGroupType = ClassDetailType['scheduleGroups'][0];
export type EnrollmentInfoType = ClassDetailType['enrollmentInfo'];

// Class schedules API response schema
export const ClassScheduleItemSchema = z.object({
  id: z.union([z.string(), z.number()]).transform(String),
  dayOfWeek: z.string(),
  startTime: z.string(),
  endTime: z.string(),
});

export const ClassScheduleGroupSchema = z.object({
  id: z.union([z.string(), z.number()]).transform(String),
  status: z.string(),
  schedules: z.array(ClassScheduleItemSchema),
  currentEnrollments: z.array(
    z.object({
      memberId: z.string(),
      name: z.string().nullable(),
      gender: z.string().nullable(),
    })
  ),
});

export const ClassSchedulesResponseSchema = z.object({
  class: z.object({
    id: z.union([z.string(), z.number()]).transform(String),
    title: z.string(),
    description: z.string(),
    category: z.string(),
    level: z.string(),
    target: z.string(),
    pricePerSession: z.number(),
    maxParticipants: z.number(),
    sessionDurationMinutes: z.number(),
    durationWeeks: z.number(),
    sessionsPerWeek: z.number(),
    images: z
      .array(
        z.object({
          url: z.string(),
        })
      )
      .nullable(),
    studio: z.object({
      id: z.union([z.string(), z.number()]).transform(String),
      name: z.string(),
      address: z.string(),
      phone: z.string(),
    }),
    instructor: z.object({
      id: z.union([z.string(), z.number()]).transform(String),
      name: z.string(),
      profileImages: z
        .array(
          z.object({
            url: z.string(),
          })
        )
        .nullable(),
    }),
  }),
  scheduleGroups: z.array(ClassScheduleGroupSchema),
});

export type ClassScheduleItem = z.infer<typeof ClassScheduleItemSchema>;
export type ClassScheduleGroup = z.infer<typeof ClassScheduleGroupSchema>;
export type ClassSchedulesResponse = z.infer<
  typeof ClassSchedulesResponseSchema
>;
