import { NextRequest, NextResponse } from 'next/server';
import { ClassListQuerySchema, ClassListResponseSchema } from './schema';
import { classService } from '@/lib/services/class.service';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

/**
 * 클래스 목록 조회 API
 * GET /api/classes
 *
 * @description
 * - 공개된 클래스 목록을 조회합니다
 * - 필터링, 페이지네이션, 정렬 기능을 지원합니다
 *
 * @queryParams
 * - nearestStation: 가까운 지하철역으로 필터링
 * - category: 운동 카테고리로 필터링 (fitness, pilates, yoga 등)
 * - level: 난이도로 필터링 (beginner, intermediate, advanced)
 * - page: 페이지 번호 (기본값: 1)
 * - limit: 페이지당 항목 수 (기본값: 20, 최대: 100)
 * - sort: 정렬 기준 (created_at, price, title) (기본값: created_at)
 * - order: 정렬 순서 (asc, desc) (기본값: desc)
 */
export async function GET(request: NextRequest) {
  try {
    // Parse and validate query parameters with support for multiple values
    const searchParams = request.nextUrl.searchParams;
    const parsedParams: Record<string, string | string[]> = {};

    // Handle filter parameters that can have multiple values
    const multiValueParams = ['level', 'category', 'nearestStation'];

    for (const [key, value] of searchParams.entries()) {
      if (multiValueParams.includes(key)) {
        const allValues = searchParams.getAll(key);
        parsedParams[key] = allValues.length === 1 ? allValues[0] : allValues;
      } else {
        parsedParams[key] = value;
      }
    }

    const validatedQuery = ClassListQuerySchema.parse(parsedParams);

    // Call service to get class list
    const result = await classService.getClassList(validatedQuery);

    // Validate response
    const validatedResponse = ClassListResponseSchema.parse(result);

    return NextResponse.json(validatedResponse);
  } catch (error) {
    console.error('Classes list fetch error:', error);

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid request parameters', details: error },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
