import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { inquiries } from '@/lib/db/schema/inquiries';
import {
  CreateInquirySchema,
  type CreateInquiryResponse,
} from '@/schemas/inquiry';
import { getMemberIdFromRequest } from '@/lib/auth/member.server';

/**
 * 문의 생성 API
 * POST /api/inquiries
 *
 * @description
 * - 회원/비회원 모두 문의 가능
 * - 로그인한 경우 member_id 자동 연결
 * - 비로그인인 경우 member_id는 null
 */
export async function POST(
  request: NextRequest
): Promise<NextResponse<CreateInquiryResponse>> {
  try {
    // 요청 데이터 파싱
    const body = await request.json();

    // 유효성 검사
    const validationResult = CreateInquirySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: '입력 데이터가 올바르지 않습니다',
          // zod v4: flatten() 대신 issues 사용
          error:
            validationResult.error.issues.find(iss =>
              iss.path.includes('content')
            )?.message || '유효하지 않은 요청',
        },
        { status: 400 }
      );
    }

    const { content } = validationResult.data;

    // 현재 사용자 확인 (선택적)
    const memberId = await getMemberIdFromRequest();

    // 문의 생성
    const [inquiry] = await db
      .insert(inquiries)
      .values({
        content,
        member_id: memberId,
      })
      .returning();

    return NextResponse.json(
      {
        success: true,
        message: '문의가 성공적으로 접수되었습니다.',
        data: {
          id: inquiry.id,
          created_at:
            inquiry.created_at?.toISOString() || new Date().toISOString(),
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('문의 생성 오류:', error);
    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        error:
          process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
