import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { z } from 'zod';
import { PartnerMeResponseSchema } from './me.schema';

// 에러 응답 스키마
const ErrorResponseSchema = z.object({
  message: z.string(),
  error: z.string().optional(),
});

// 타입 추출
type PartnerMeResponse = z.infer<typeof PartnerMeResponseSchema>;
type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

/**
 * 파트너 본인 정보 조회 API
 *
 * @description 현재 로그인한 파트너의 본인 정보를 조회합니다.
 * @route GET /api/partner/me
 * @auth 파트너 인증 필요 (ACTIVE 상태)
 *
 * @returns {200} 파트너 정보 조회 성공
 * @returns {401} 인증 실패 - 로그인이 필요하거나 인증 토큰이 유효하지 않음
 * @returns {403} 권한 없음 - 파트너 계정이 아니거나 비활성 상태
 * @returns {500} 서버 오류
 */
export async function GET(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);

    if (errorResponse || !partner) {
      return errorResponse || new Response('인증 실패', { status: 401 });
    }

    // 응답 데이터 구성
    const partnerData: PartnerMeResponse = {
      id: partner.id,
      name: partner.contactName,
      email: partner.email,
      phone: partner.contactPhone,
      status: partner.status,
    };

    // Zod 스키마로 검증
    const validatedData = PartnerMeResponseSchema.parse(partnerData);

    // 성공 응답
    return NextResponse.json<PartnerMeResponse>(validatedData, { status: 200 });
  } catch (error) {
    console.error('파트너 본인 정보 API 오류:', error);

    const errorResponse = ErrorResponseSchema.parse({
      message: '서버 오류가 발생했습니다.',
      error: 'INTERNAL_SERVER_ERROR',
    });
    return NextResponse.json<ErrorResponse>(errorResponse, { status: 500 });
  }
}
