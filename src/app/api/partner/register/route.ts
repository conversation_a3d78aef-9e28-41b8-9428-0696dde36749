import { createSupabaseClient } from '@/lib/supabase/server';
import {
  formatZodErrors,
  PartnerRegisterSchema,
  type PartnerRegisterResponse,
} from '@/schemas/partner';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 파트너 회원가입 API 엔드포인트
 *
 * @description
 * - POST: 파트너 회원가입 (이메일/비밀번호 + 연락처 정보)
 */

// TypeScript 타입은 schemas/partner.ts에서 import

/**
 * 파트너 회원가입 API
 * POST /api/partner/register
 */
export async function POST(
  request: NextRequest
): Promise<NextResponse<PartnerRegisterResponse>> {
  try {
    // 요청 데이터 파싱 (안전한 JSON 파싱)
    let requestData;
    try {
      const body = await request.text();
      if (!body || body.trim() === '') {
        return NextResponse.json(
          {
            success: false,
            message: '요청 본문이 비어있습니다.',
          },
          { status: 400 }
        );
      }
      requestData = JSON.parse(body);
    } catch (parseError) {
      console.error('JSON 파싱 오류:', parseError);
      return NextResponse.json(
        {
          success: false,
          message: '잘못된 JSON 형식입니다.',
          error:
            process.env.NODE_ENV === 'development'
              ? String(parseError)
              : undefined,
        },
        { status: 400 }
      );
    }

    // Zod 스키마로 유효성 검사
    const validationResult = PartnerRegisterSchema.safeParse(requestData);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: '입력 데이터가 올바르지 않습니다.',
          errors: formatZodErrors(validationResult.error),
        },
        { status: 400 }
      );
    }

    const { email, password, contactInfo, terms_agreements } =
      validationResult.data;

    const supabase = await createSupabaseClient();

    // 1. partners 테이블에서 이메일 중복 검사 (최우선)
    const { data: existingPartner, error: partnerCheckError } = await supabase
      .from('partners')
      .select('email')
      .eq('email', email)
      .single();

    if (partnerCheckError && partnerCheckError.code !== 'PGRST116') {
      console.error('파트너 이메일 중복 검사 오류:', partnerCheckError);
      return NextResponse.json(
        {
          success: false,
          message: '이메일 검증 중 오류가 발생했습니다.',
          error: partnerCheckError.message,
        },
        { status: 500 }
      );
    }

    if (existingPartner) {
      return NextResponse.json(
        {
          success: false,
          message: '이미 등록된 이메일 주소입니다.',
          error: '이미 등록된 이메일입니다',
        },
        { status: 409 }
      );
    }

    // 2. Supabase Auth 회원가입 (메타데이터에 contact 정보 포함)
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/auth/callback?redirect_to=/partner/register/complete`,
        data: {
          role: 'partner', // 사용자 메타데이터에 역할 저장
          contact_name: contactInfo.name.trim(),
          contact_phone: contactInfo.phone,
        },
      },
    });

    if (authError) {
      console.error('Supabase Auth 오류:', {
        message: authError.message,
        code: authError.code,
        status: authError.status,
        name: authError.name,
      });

      // 1. 이메일 중복 관련 에러 처리
      if (
        authError.message.includes('User already registered') ||
        authError.message.includes('already been registered') ||
        authError.message.includes('duplicate') ||
        authError.message.includes('email address is already registered') ||
        authError.code === '23505' || // PostgreSQL unique constraint violation
        authError.status === 422
      ) {
        // Unprocessable Entity (Supabase Auth)
        return NextResponse.json(
          {
            success: false,
            message: '이미 등록된 이메일 주소입니다.',
            error: authError.message,
          },
          { status: 409 }
        );
      }

      // 2. 비밀번호 정책 위반
      if (
        authError.message.includes('Password') ||
        authError.message.includes('password') ||
        authError.code === 'weak_password'
      ) {
        return NextResponse.json(
          {
            success: false,
            message:
              '비밀번호가 정책에 맞지 않습니다. 더 강한 비밀번호를 사용해주세요.',
            error: authError.message,
          },
          { status: 400 }
        );
      }

      // 3. 이메일 형식 오류
      if (
        authError.message.includes('Invalid email') ||
        authError.message.includes('email') ||
        authError.code === 'invalid_email'
      ) {
        return NextResponse.json(
          {
            success: false,
            message: '올바른 이메일 형식을 입력해주세요.',
            error: authError.message,
          },
          { status: 400 }
        );
      }

      // 4. Rate limiting
      if (
        authError.message.includes('rate limit') ||
        authError.message.includes('too many') ||
        authError.status === 429
      ) {
        return NextResponse.json(
          {
            success: false,
            message:
              '너무 많은 요청이 발생했습니다. 잠시 후 다시 시도해주세요.',
            error: authError.message,
          },
          { status: 429 }
        );
      }

      // 5. 기타 Auth 오류
      return NextResponse.json(
        {
          success: false,
          message:
            '회원가입 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
          error:
            process.env.NODE_ENV === 'development'
              ? authError.message
              : '회원가입 실패',
        },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        {
          success: false,
          message: '사용자 생성에 실패했습니다.',
        },
        { status: 500 }
      );
    }

    // 3. partners 테이블에 레코드 직접 생성
    const { data: partnerData, error: partnerInsertError } = await supabase
      .from('partners')
      .insert({
        user_id: authData.user.id,
        email: authData.user.email!,
        contact_name: contactInfo.name.trim(),
        contact_phone: contactInfo.phone,
        terms_agreements: terms_agreements,
        status: 'PENDING',
      })
      .select()
      .single();

    if (partnerInsertError) {
      console.error('파트너 레코드 생성 오류:', partnerInsertError);

      // partners 테이블 insert 실패 시 auth 사용자도 정리 (선택사항)
      // 현재는 에러만 로깅하고 진행
      return NextResponse.json(
        {
          success: false,
          message: '파트너 정보 저장 중 오류가 발생했습니다.',
          error: partnerInsertError.message,
        },
        { status: 500 }
      );
    }

    // 4. 성공 응답
    return NextResponse.json(
      {
        success: true,
        message: '회원가입이 완료되었습니다. 이메일 인증을 확인해주세요.',
        data: {
          partnerId: partnerData.id,
          status: 'PENDING' as const,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('파트너 회원가입 API 오류:', error);

    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        error:
          process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
