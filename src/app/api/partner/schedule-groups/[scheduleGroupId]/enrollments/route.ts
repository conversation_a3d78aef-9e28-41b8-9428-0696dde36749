import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { enrollmentQueries } from '@/lib/queries/enrollments.queries';
import { 
  enrollmentQuerySchema,
  enrollmentListSuccessResponseSchema,
  type EnrollmentListSuccessResponse,
  type EnrollmentErrorResponse 
} from '@/lib/validations/enrollments.validation';

/**
 * 스케줄 그룹의 수강 신청 목록 조회 API
 * GET /api/partner/schedule-groups/[scheduleGroupId]/enrollments
 */

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ scheduleGroupId: string }> }
) {
  try {
    // 1. 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 파라미터 파싱
    const { scheduleGroupId: scheduleGroupIdParam } = await params;
    const scheduleGroupId = parseInt(scheduleGroupIdParam);

    if (isNaN(scheduleGroupId)) {
      const errorResponse: EnrollmentErrorResponse = {
        success: false,
        error: 'INVALID_PARAMS',
        message: '올바른 스케줄 그룹 ID가 아닙니다',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 3. 쿼리 파라미터 검증
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = enrollmentQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      const errorResponse: EnrollmentErrorResponse = {
        success: false,
        error: 'INVALID_PARAMS',
        message: '잘못된 요청 파라미터입니다',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const filters = validationResult.data;

    // 4. 파트너 소유권 확인
    const isOwner = await enrollmentQueries.verifyPartnerOwnership(
      scheduleGroupId,
      partner!.id
    );

    if (!isOwner) {
      const errorResponse: EnrollmentErrorResponse = {
        success: false,
        error: 'FORBIDDEN',
        message: '해당 스케줄 그룹에 대한 접근 권한이 없습니다',
      };
      return NextResponse.json(errorResponse, { status: 403 });
    }

    // 5. 수강 신청 목록 조회
    const enrollments = await enrollmentQueries.findEnrollmentsByScheduleGroup(
      scheduleGroupId,
      {
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      }
    );

    // 6. 응답 데이터 구성 (존재하는 필드만)
    const responseData = enrollments.map(enrollment => ({
      id: enrollment.id,
      memberId: enrollment.memberId,
      nickname: enrollment.nickname || '',
      email: enrollment.email || '',
      status: enrollment.status,
      enrollmentOrder: enrollment.enrollmentOrder,
      createdAt: enrollment.createdAt?.toISOString() || '',
    }));

    const response: EnrollmentListSuccessResponse = enrollmentListSuccessResponseSchema.parse({
      success: true,
      data: {
        scheduleGroupId,
        enrollments: responseData,
      },
    });

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Enrollments fetch error:', error);

    const errorResponse: EnrollmentErrorResponse = {
      success: false,
      error: 'INTERNAL_ERROR',
      message: '수강 신청 목록을 불러오는 중 오류가 발생했습니다',
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}