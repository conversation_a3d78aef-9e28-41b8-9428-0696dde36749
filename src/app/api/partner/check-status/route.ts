import { createSupabaseClient } from '@/lib/supabase/server';
import { PartnerCheckStatusResponse } from '@/types/partner';
import { NextResponse } from 'next/server';

/**
 * 파트너 상태 확인 API 엔드포인트
 *
 * @description
 * - GET: 인증된 파트너의 현재 상태 조회
 * - 파트너 상태: PENDING, ACTIVE, SUSPENDED, REJECTED
 */

/**
 * 파트너 상태 확인 API
 * GET /api/partner/check-status
 */
export async function GET(): Promise<NextResponse<PartnerCheckStatusResponse>> {
  try {
    const supabase = await createSupabaseClient();

    // 1. 사용자 세션 확인 (이메일 미인증 사용자도 허용)
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session || !session.user) {
      return NextResponse.json(
        {
          success: false,
          message: '인증이 필요합니다.',
        },
        { status: 401 }
      );
    }

    const user = session.user;

    // 2. 파트너 정보 조회
    const { data: partner, error: partnerError } = await supabase
      .from('partners')
      .select(
        'id, email, contact_name, contact_phone, status, created_at, updated_at'
      )
      .eq('user_id', user.id)
      .single();

    if (partnerError) {
      console.error('파트너 정보 조회 오류:', partnerError);

      // 파트너 정보가 없는 경우
      if (partnerError.code === 'PGRST116') {
        return NextResponse.json(
          {
            success: false,
            message: '파트너 정보를 찾을 수 없습니다.',
          },
          { status: 404 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          message: '파트너 상태 조회 중 오류가 발생했습니다.',
        },
        { status: 500 }
      );
    }

    if (!partner) {
      return NextResponse.json(
        {
          success: false,
          message: '파트너 정보를 찾을 수 없습니다.',
        },
        { status: 404 }
      );
    }

    // 3. 이메일 인증 상태 확인
    const isEmailVerified = !!user.email_confirmed_at;

    // 4. 사업자등록증 제출 상태 판단
    const isDocumentSubmitted =
      partner.status === 'ACTIVE' ||
      (partner.status === 'PENDING' && isEmailVerified);

    // 5. 성공 응답 (스테퍼용 데이터 포함)
    return NextResponse.json(
      {
        success: true,
        data: {
          partnerId: partner.id,
          email: partner.email,
          contactName: partner.contact_name,
          status: partner.status,
          emailConfirmed: isEmailVerified,
          businessDocumentSubmitted:
            isDocumentSubmitted && partner.status === 'PENDING',
          createdAt: partner.created_at,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('파트너 상태 확인 API 오류:', error);

    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
      },
      { status: 500 }
    );
  }
}
