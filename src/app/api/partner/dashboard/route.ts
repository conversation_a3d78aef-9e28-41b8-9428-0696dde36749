import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { 
  DashboardResponseSchema,
  ErrorResponseSchema,
  type DashboardResponse,
  type ErrorResponse
} from './schema';
import {
  getClassStats,
  getTodayClasses,
  getWeeklyClasses,
  getRecruitingClasses,
  getOngoingClasses
} from './queries';

/**
 * 파트너 대시보드 데이터 조회 API
 * 
 * @description 파트너의 대시보드에 표시할 모든 데이터를 조회합니다.
 * @route GET /api/partner/dashboard
 * @auth 파트너 인증 필요 (ACTIVE 상태)
 * 
 * @returns {200} 대시보드 데이터 조회 성공
 * @returns {401} 인증 실패 - 로그인이 필요하거나 인증 토큰이 유효하지 않음
 * @returns {403} 권한 없음 - 파트너 계정이 아니거나 비활성 상태
 * @returns {500} 서버 오류
 * 
 * @example
 * // 성공 응답 (200)
 * {
 *   "partner": {
 *     "id": "uuid",
 *     "name": "파트너명"
 *   },
 *   "classStats": {
 *     "recruiting": 3,
 *     "ongoing": 5,
 *     "upcoming": 2
 *   },
 *   "todayClasses": [
 *     {
 *       "id": "class-1-2024-01-15",
 *       "classId": "uuid",
 *       "title": "요가 초급반",
 *       "date": "2024-01-15",
 *       "startTime": "10:00:00",
 *       "endTime": "11:00:00",
 *       "instructor": "김강사",
 *       "students": 8,
 *       "maxStudents": 12,
 *       "status": "confirmed",
 *       "studioName": "강남 스튜디오"
 *     }
 *   ],
 *   "thisWeekClasses": [...],
 *   "nextWeekClasses": [...],
 *   "notifications": []
 * }
 * 
 * @example
 * // 에러 응답 (401)
 * {
 *   "message": "인증이 필요합니다.",
 *   "error": "UNAUTHORIZED"
 * }
 */
export async function GET(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    
    if (errorResponse || !partner) {
      return errorResponse || new Response('인증 실패', { status: 401 });
    }

    // 병렬로 모든 데이터 조회
    const [
      classStats,
      todayClasses,
      thisWeekClasses,
      nextWeekClasses,
      recruitingClasses,
      ongoingClasses
    ] = await Promise.all([
      getClassStats(partner.id),
      getTodayClasses(partner.id),
      getWeeklyClasses(partner.id, 0), // 이번주
      getWeeklyClasses(partner.id, 1),  // 다음주
      getRecruitingClasses(partner.id),
      getOngoingClasses(partner.id)
    ]);

    // 응답 데이터 구성
    const dashboardData: DashboardResponse = {
      partner: {
        id: partner.id,
        name: partner.contactName
      },
      classStats,
      todayClasses,
      thisWeekClasses,
      nextWeekClasses,
      recruitingClasses,
      ongoingClasses,
      notifications: [] // 초기에는 빈 배열
    };

    // Zod 스키마로 검증
    const validatedData = DashboardResponseSchema.parse(dashboardData);

    // 성공 응답
    return NextResponse.json<DashboardResponse>(validatedData, { status: 200 });

  } catch (error) {
    console.error('파트너 대시보드 API 오류:', error);
    
    const errorResponse = ErrorResponseSchema.parse({
      message: '서버 오류가 발생했습니다.',
      error: 'INTERNAL_SERVER_ERROR'
    });
    return NextResponse.json<ErrorResponse>(errorResponse, { status: 500 });
  }
}
