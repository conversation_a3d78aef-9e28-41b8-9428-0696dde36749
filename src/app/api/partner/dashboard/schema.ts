import { z } from 'zod';

// 수업 정보 스키마
export const ClassOccurrenceSchema = z.object({
  id: z.string(),
  classId: z.string(),
  title: z.string(),
  date: z.string(), // YYYY-MM-DD
  startTime: z.string(), // HH:MM:SS
  endTime: z.string(), // HH:MM:SS
  instructor: z.string(),
  students: z.number(),
  maxStudents: z.number(),
  status: z.literal('confirmed'),
  studioName: z.string(),
  groupName: z.string().optional(),
  notes: z.string().optional()
});

// 스케줄 그룹 스키마
export const ScheduleGroupSchema = z.object({
  id: z.number(),
  classId: z.string(),
  className: z.string(),
  instructor: z.object({
    name: z.string()
  }),
  schedules: z.array(z.object({
    dayOfWeek: z.string(),
    startTime: z.string(),
    endTime: z.string()
  })),
  maxParticipants: z.number(),
  confirmedCount: z.number(),
  applicantCount: z.number(),
  status: z.enum(['recruiting', 'ongoing', 'ended']),
  startDate: z.string().nullable(),
  endDate: z.string().nullable()
});

// 파트너 정보 스키마
export const PartnerInfoSchema = z.object({
  id: z.string(),
  name: z.string()
});

// 클래스 통계 스키마
export const ClassStatsSchema = z.object({
  recruiting: z.number(),
  ongoing: z.number(),
  upcoming: z.number()
});


// 대시보드 응답 스키마
export const DashboardResponseSchema = z.object({
  partner: PartnerInfoSchema,
  classStats: ClassStatsSchema,
  todayClasses: z.array(ClassOccurrenceSchema),
  thisWeekClasses: z.array(ClassOccurrenceSchema),
  nextWeekClasses: z.array(ClassOccurrenceSchema),
  recruitingClasses: z.array(ScheduleGroupSchema),
  ongoingClasses: z.array(ScheduleGroupSchema),
  notifications: z.array(z.any()) // 초기에는 빈 배열
});

// 에러 응답 스키마
export const ErrorResponseSchema = z.object({
  message: z.string(),
  error: z.string().optional()
});

// 타입 추출
export type DashboardResponse = z.infer<typeof DashboardResponseSchema>;
export type ClassOccurrence = z.infer<typeof ClassOccurrenceSchema>;
export type ScheduleGroup = z.infer<typeof ScheduleGroupSchema>;
export type ClassStats = z.infer<typeof ClassStatsSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;