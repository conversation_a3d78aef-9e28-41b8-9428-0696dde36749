import { db } from '@/lib/db';
import { 
  partners, 
  classes, 
  class_schedule_groups, 
  class_schedules,
  enrollments,
  instructors,
  studios,
  ClassScheduleStatusText
} from '@/lib/db/schema';
// enrollment status 상수
const ENROLLMENT_STATUS = {
  PAID: 'paid' as const,
  CONFIRMED: 'confirmed' as const
} as const;
import { eq, sql, and, isNotNull, ne, inArray } from 'drizzle-orm';
import { 
  getKoreanToday, 
  getWeekRange, 
  getDayOfWeek, 
  formatDate, 
  hasClassOnDate,
  getDatesBetween 
} from './utils';
import { 
  ClassStats, 
  ClassOccurrence,
  ScheduleGroup 
} from './schema';

/**
 * 클래스 상태별 통계 조회
 */
export async function getClassStats(partnerId: string): Promise<ClassStats> {
  const result = await db
    .select({
      classId: classes.id,
      durationWeeks: classes.duration_weeks,
      startDate: class_schedule_groups.start_date,
      enrollmentCount: sql<number>`COUNT(CASE WHEN ${enrollments.status} IN (${ENROLLMENT_STATUS.PAID}, ${ENROLLMENT_STATUS.CONFIRMED}) THEN 1 END)::int`
    })
    .from(classes)
    .innerJoin(
      class_schedule_groups,
      eq(classes.id, class_schedule_groups.class_id)
    )
    .leftJoin(
      enrollments,
      eq(enrollments.classId, classes.id)
    )
    .where(and(
      eq(classes.partner_id, partnerId),
      eq(classes.status, 'active'),
      ne(class_schedule_groups.status, ClassScheduleStatusText.CANCELLED)
    ))
    .groupBy(classes.id, classes.duration_weeks, class_schedule_groups.start_date);

  const today = getKoreanToday();
  const stats = {
    recruiting: 0,
    ongoing: 0,
    upcoming: 0
  };

  // 각 클래스별로 상태 판단
  const classStatusMap = new Map<string, string>();
  
  result.forEach(row => {
    if (!row.startDate) {
      // start_date가 없는 경우
      if (row.enrollmentCount > 0) {
        classStatusMap.set(row.classId, 'upcoming'); // 수강신청자 있음 → 개설 확정, 날짜 미정
      } else {
        classStatusMap.set(row.classId, 'recruiting'); // 수강신청자 없음 → 모집 중
      }
    } else {
      // start_date가 있는 경우
      const endDate = new Date(row.startDate);
      endDate.setDate(endDate.getDate() + (row.durationWeeks * 7));
      
      // 종료된 클래스는 통계에서 제외, 진행 중인 클래스만 포함
      if (today < endDate) {
        classStatusMap.set(row.classId, 'ongoing');
      }
      // 종료된 클래스 (today >= endDate)는 아무것도 하지 않음 (통계에서 제외)
    }
  });

  // 중복 제거하여 카운트
  classStatusMap.forEach(status => {
    stats[status as keyof ClassStats]++;
  });

  return stats;
}


/**
 * 특정 날짜의 수업 목록 조회
 */
export async function getTodayClasses(partnerId: string): Promise<ClassOccurrence[]> {
  const today = getKoreanToday();
  const todayDayOfWeek = getDayOfWeek(today);
  
  // 오늘 진행되는 수업 조회
  const result = await db
    .select({
      classId: classes.id,
      title: classes.title,
      maxParticipants: classes.max_participants,
      startDate: class_schedule_groups.start_date,
      durationWeeks: classes.duration_weeks,
      scheduleGroupId: class_schedule_groups.id,
      dayOfWeek: class_schedules.day_of_week,
      startTime: class_schedules.start_time,
      endTime: class_schedules.end_time,
      instructorName: instructors.name,
      studioName: studios.name
    })
    .from(classes)
    .innerJoin(
      class_schedule_groups,
      eq(classes.id, class_schedule_groups.class_id)
    )
    .innerJoin(
      class_schedules,
      eq(class_schedule_groups.id, class_schedules.schedule_group_id)
    )
    .innerJoin(
      instructors,
      eq(classes.instructor_id, instructors.id)
    )
    .innerJoin(
      studios,
      eq(classes.studio_id, studios.id)
    )
    .where(and(
      eq(classes.partner_id, partnerId),
      eq(classes.status, 'active'),
      isNotNull(class_schedule_groups.start_date),
      eq(class_schedules.day_of_week, todayDayOfWeek),
      ne(class_schedule_groups.status, ClassScheduleStatusText.CANCELLED)
    ));

  // 오늘 수업인지 확인하고 변환
  const todayClasses: ClassOccurrence[] = [];
  
  for (const row of result) {
    if (row.startDate && hasClassOnDate(row.startDate, row.durationWeeks, today, row.dayOfWeek)) {
      // 수강생 수 조회
      const enrollmentCount = await db
        .select({ count: sql<number>`COUNT(*)::int` })
        .from(enrollments)
        .where(and(
          eq(enrollments.classId, row.classId),
          eq(enrollments.scheduleGroupId, row.scheduleGroupId),
          inArray(enrollments.status, [ENROLLMENT_STATUS.PAID, ENROLLMENT_STATUS.CONFIRMED])
        ));

      todayClasses.push({
        id: `${row.classId}-${formatDate(today)}`,
        classId: row.classId,
        title: row.title,
        date: formatDate(today),
        startTime: row.startTime,
        endTime: row.endTime,
        instructor: row.instructorName,
        students: enrollmentCount[0]?.count || 0,
        maxStudents: row.maxParticipants,
        status: 'confirmed',
        studioName: row.studioName
      });
    }
  }

  return todayClasses;
}

/**
 * 주간 수업 목록 조회
 * @param weekOffset 0: 이번주, 1: 다음주
 */
export async function getWeeklyClasses(
  partnerId: string, 
  weekOffset: number
): Promise<ClassOccurrence[]> {
  const { start, end } = getWeekRange(weekOffset);
  
  // 해당 주의 모든 수업 조회
  const result = await db
    .select({
      classId: classes.id,
      title: classes.title,
      maxParticipants: classes.max_participants,
      startDate: class_schedule_groups.start_date,
      durationWeeks: classes.duration_weeks,
      scheduleGroupId: class_schedule_groups.id,
      dayOfWeek: class_schedules.day_of_week,
      startTime: class_schedules.start_time,
      endTime: class_schedules.end_time,
      instructorName: instructors.name,
      studioName: studios.name
    })
    .from(classes)
    .innerJoin(
      class_schedule_groups,
      eq(classes.id, class_schedule_groups.class_id)
    )
    .innerJoin(
      class_schedules,
      eq(class_schedule_groups.id, class_schedules.schedule_group_id)
    )
    .innerJoin(
      instructors,
      eq(classes.instructor_id, instructors.id)
    )
    .innerJoin(
      studios,
      eq(classes.studio_id, studios.id)
    )
    .where(and(
      eq(classes.partner_id, partnerId),
      eq(classes.status, 'active'),
      isNotNull(class_schedule_groups.start_date),
      ne(class_schedule_groups.status, ClassScheduleStatusText.CANCELLED)
    ));

  // 주간 수업 목록 생성
  const weeklyClasses: ClassOccurrence[] = [];
  const dates = getDatesBetween(start, end);
  
  for (const date of dates) {
    const dayOfWeek = getDayOfWeek(date);
    
    for (const row of result) {
      if (row.startDate && 
          row.dayOfWeek === dayOfWeek &&
          hasClassOnDate(row.startDate, row.durationWeeks, date, row.dayOfWeek)) {
        
        // 수강생 수 조회
        const enrollmentCount = await db
          .select({ count: sql<number>`COUNT(*)::int` })
          .from(enrollments)
          .where(and(
            eq(enrollments.classId, row.classId),
            eq(enrollments.scheduleGroupId, row.scheduleGroupId),
            inArray(enrollments.status, [ENROLLMENT_STATUS.PAID, ENROLLMENT_STATUS.CONFIRMED])
          ));

        weeklyClasses.push({
          id: `${row.classId}-${formatDate(date)}`,
          classId: row.classId,
          title: row.title,
          date: formatDate(date),
          startTime: row.startTime,
          endTime: row.endTime,
          instructor: row.instructorName,
          students: enrollmentCount[0]?.count || 0,
          maxStudents: row.maxParticipants,
          status: 'confirmed',
          studioName: row.studioName
        });
      }
    }
  }

  // 날짜순 정렬
  return weeklyClasses.sort((a, b) => {
    const dateCompare = a.date.localeCompare(b.date);
    if (dateCompare !== 0) return dateCompare;
    return a.startTime.localeCompare(b.startTime);
  });
}

/**
 * 모집중 클래스 조회
 */
export async function getRecruitingClasses(partnerId: string): Promise<ScheduleGroup[]> {
  const result = await db
    .select({
      id: class_schedule_groups.id,
      classId: classes.id,
      className: classes.title,
      instructorName: instructors.name,
      maxParticipants: classes.max_participants,
      status: class_schedule_groups.status,
      startDate: class_schedule_groups.start_date,
      durationWeeks: classes.duration_weeks
    })
    .from(classes)
    .innerJoin(
      class_schedule_groups,
      eq(classes.id, class_schedule_groups.class_id)
    )
    .innerJoin(
      instructors,
      eq(classes.instructor_id, instructors.id)
    )
    .where(and(
      eq(classes.partner_id, partnerId),
      eq(classes.status, 'active'),
      eq(class_schedule_groups.status, 'pending')
    ));

  const scheduleGroups: ScheduleGroup[] = [];

  for (const row of result) {
    // 스케줄 정보 조회
    const schedules = await db
      .select({
        dayOfWeek: class_schedules.day_of_week,
        startTime: class_schedules.start_time,
        endTime: class_schedules.end_time
      })
      .from(class_schedules)
      .where(eq(class_schedules.schedule_group_id, row.id));

    // 신청자 수 조회
    const applicantCount = await db
      .select({ count: sql<number>`COUNT(*)::int` })
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, row.classId),
        eq(enrollments.scheduleGroupId, row.id),
        eq(enrollments.status, ENROLLMENT_STATUS.PAID)
      ));

    // 날짜 변환 (Drizzle timestamp는 문자열로 반환됨)
    const startDateStr = row.startDate 
      ? new Date(row.startDate).toISOString().split('T')[0]
      : null;

    // 종료일 계산
    const endDate = row.startDate && row.durationWeeks 
      ? new Date(new Date(row.startDate).getTime() + (row.durationWeeks * 7 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]
      : null;

    scheduleGroups.push({
      id: row.id,
      classId: row.classId,
      className: row.className,
      instructor: { name: row.instructorName },
      schedules: schedules.map(s => ({
        dayOfWeek: s.dayOfWeek,
        startTime: s.startTime,
        endTime: s.endTime
      })),
      maxParticipants: row.maxParticipants,
      confirmedCount: 0,
      applicantCount: applicantCount[0]?.count || 0,
      status: 'recruiting',
      startDate: startDateStr,
      endDate
    });
  }

  return scheduleGroups;
}

/**
 * 진행중 클래스 조회
 */
export async function getOngoingClasses(partnerId: string): Promise<ScheduleGroup[]> {
  const result = await db
    .select({
      id: class_schedule_groups.id,
      classId: classes.id,
      className: classes.title,
      instructorName: instructors.name,
      maxParticipants: classes.max_participants,
      status: class_schedule_groups.status,
      startDate: class_schedule_groups.start_date,
      durationWeeks: classes.duration_weeks
    })
    .from(classes)
    .innerJoin(
      class_schedule_groups,
      eq(classes.id, class_schedule_groups.class_id)
    )
    .innerJoin(
      instructors,
      eq(classes.instructor_id, instructors.id)
    )
    .where(and(
      eq(classes.partner_id, partnerId),
      eq(classes.status, 'active'),
      eq(class_schedule_groups.status, 'confirmed'),
      isNotNull(class_schedule_groups.start_date)
    ));

  const scheduleGroups: ScheduleGroup[] = [];

  for (const row of result) {
    // 스케줄 정보 조회
    const schedules = await db
      .select({
        dayOfWeek: class_schedules.day_of_week,
        startTime: class_schedules.start_time,
        endTime: class_schedules.end_time
      })
      .from(class_schedules)
      .where(eq(class_schedules.schedule_group_id, row.id));

    // 확정된 수강생 수 조회
    const confirmedCount = await db
      .select({ count: sql<number>`COUNT(*)::int` })
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, row.classId),
        eq(enrollments.scheduleGroupId, row.id),
        eq(enrollments.status, ENROLLMENT_STATUS.CONFIRMED)
      ));

    // 날짜 변환 (Drizzle timestamp는 문자열로 반환됨)
    const startDateStr = row.startDate 
      ? new Date(row.startDate).toISOString().split('T')[0]
      : null;

    // 종료일 계산
    const endDate = row.startDate && row.durationWeeks 
      ? new Date(new Date(row.startDate).getTime() + (row.durationWeeks * 7 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]
      : null;

    scheduleGroups.push({
      id: row.id,
      classId: row.classId,
      className: row.className,
      instructor: { name: row.instructorName },
      schedules: schedules.map(s => ({
        dayOfWeek: s.dayOfWeek,
        startTime: s.startTime,
        endTime: s.endTime
      })),
      maxParticipants: row.maxParticipants,
      confirmedCount: confirmedCount[0]?.count || 0,
      applicantCount: 0,
      status: 'ongoing',
      startDate: startDateStr,
      endDate
    });
  }

  return scheduleGroups;
}