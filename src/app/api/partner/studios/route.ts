import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import { StudioService } from '@/lib/services/studio.service';
import { CreateStudioSchema } from '@/lib/schemas/studio';
import { requirePartnerAuth } from '@/lib/auth/partner.server';

/**
 * 파트너 스튜디오 생성
 *
 * @description 파트너가 새로운 스튜디오를 등록합니다.
 * partnerId는 인증 정보에서 자동으로 설정되므로 요청 body에 포함하지 않습니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 *
 * @returns {201} 스튜디오 생성 성공
 * @returns {400} 잘못된 요청 데이터
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {422} 잘못된 데이터 형식
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * POST /api/partner/studios
 * Content-Type: application/json
 *
 * // 최소 필수 정보만 포함한 예시
 * {
 *   "name": "헬퍼 피트니스 센터 강남점",        // Required
 *   "phone": "02-1234-5678",               // Required
 *   "address": "서울시 강남구 테헤란로 123",   // Required
 *   "latitude": 37.5665,                  // Required
 *   "longitude": 126.9780                 // Required
 * }
 *
 * // 모든 선택 필드를 포함한 전체 예시
 * {
 *   "name": "헬퍼 피트니스 센터 강남점",        // Required
 *   "phone": "02-1234-5678",               // Required
 *   "address": "서울시 강남구 테헤란로 123",   // Required
 *   "latitude": 37.5665,                  // Required
 *   "longitude": 126.9780,                // Required
 *   "description": "최고의 운동 시설을 제공합니다",  // Optional
 *   "addressDetail": "1층",                // Optional
 *   "postalCode": "06234",                 // Optional
 *   "nearestStation": "강남역",             // Optional
 *   "stationDistance": 200,                // Optional
 *   "amenities": {                         // Optional
 *     "parking": [{"type": "free", "description": "무료 주차장 이용 가능"}],
 *     "shower": [{"available": true, "description": "샤워실 완비"}],
 *     "locker": [{"type": "paid", "price": 1000, "description": "유료 라커 이용"}]
 *   },
 *   "operatingHours": {                    // Optional
 *     "monday": { "open": "06:00", "close": "23:00" },
 *     "tuesday": { "open": "06:00", "close": "23:00" }
 *   },
 *   "images": [                            // Optional (최대 10장, JSONB 구조)
 *     {
 *       "path": "studios/partner-id/studio-id/featured-12345.jpg",
 *       "url": "https://example.com/featured.jpg"
 *     },
 *     {
 *       "path": "studios/partner-id/studio-id/gallery-12346.jpg",
 *       "url": "https://example.com/gallery1.jpg"
 *     }
 *   ],
 *   "links": {                             // Optional (JSON 구조)
 *     "website": "https://studio.com",
 *     "sns": "https://instagram.com/studio"
 *   }
 * }
 *
 * Response:
 * {
 *   "id": "550e8400-e29b-41d4-a716-446655440000",
 *   "name": "헬퍼 피트니스 센터 강남점",
 *   "phone": "02-1234-5678",
 *   "description": "최고의 운동 시설을 제공합니다",
 *   "address": "서울시 강남구 테헤란로 123",
 *   "images": [],
 *   "links": {
 *     "website": "https://studio.com",
 *     "sns": "https://instagram.com/studio"
 *   },
 *   "createdAt": "2024-01-01T00:00:00.000Z",
 *   "updatedAt": "2024-01-01T00:00:00.000Z"
 * }
 * ```
 */

/**
 * 파트너 스튜디오 목록 조회
 *
 * @description 로그인된 파트너가 소유한 모든 스튜디오 목록을 조회합니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 *
 * @returns {200} 스튜디오 목록 조회 성공
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * GET /api/partner/studios
 *
 * Response:
 * [
 *   {
 *     "id": "550e8400-e29b-41d4-a716-446655440000",
 *     "name": "헬퍼 피트니스 센터 강남점",
 *     "phone": "02-1234-5678",
 *     "description": "최고의 운동 시설을 제공합니다",
 *     "address": "서울시 강남구 테헤란로 123",
 *     "createdAt": "2024-01-01T00:00:00.000Z",
 *     "updatedAt": "2024-01-01T00:00:00.000Z"
 *   }
 * ]
 * ```
 */
export async function GET(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const studioService = new StudioService();
    const studios = await studioService.getStudiosByPartnerId(partner!.id);

    return NextResponse.json(studios, { status: 200 });
  } catch (error) {
    console.error('스튜디오 목록 조회 오류:', error);

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (error.message.includes('올바르지 않은')) {
        return NextResponse.json({ message: error.message }, { status: 400 });
      }
    }

    return NextResponse.json(
      { message: '스튜디오 목록 조회 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();

    // 이미지 개수 검증
    if (body.images && Array.isArray(body.images)) {
      if (body.images.length > 10) {
        return NextResponse.json(
          { message: '이미지는 최대 10장까지 업로드 가능합니다.' },
          { status: 400 }
        );
      }
    }

    // 요청 데이터에 partnerId 추가
    const studioData = {
      ...body,
      partnerId: partner!.id,
    };

    // 요청 데이터 검증
    const validatedData = CreateStudioSchema.parse(studioData);

    const studioService = new StudioService();
    const newStudio = await studioService.createStudio(validatedData);

    // 캐시 무효화 - partner studios 관련 모든 캐시 갱신
    revalidateTag('/api/partner/studios');

    return NextResponse.json(newStudio, { status: 201 });
  } catch (error) {
    console.error('스튜디오 생성 오류:', error);

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (
        error.message.includes('올바르지 않은') ||
        error.message.includes('입력해주세요') ||
        error.message.includes('이하로') ||
        error.message.includes('이상')
      ) {
        return NextResponse.json({ message: error.message }, { status: 422 });
      }
    }

    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        {
          message: '요청 데이터가 올바르지 않습니다.',
          errors: error,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: '스튜디오 생성 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
