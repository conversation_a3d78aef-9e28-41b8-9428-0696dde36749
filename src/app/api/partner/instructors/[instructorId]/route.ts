import { NextRequest, NextResponse } from 'next/server';
import { InstructorService } from '@/lib/services/instructor.service';
import { UpdateInstructorSchema } from '@/lib/schemas/instructor';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { ZodError } from 'zod';
import { revalidateTag } from 'next/cache';

// Service 인스턴스 생성
const instructorService = new InstructorService();

/**
 * 특정 강사의 상세 정보를 조회합니다
 *
 * @description 파트너가 등록한 특정 강사 정보를 조회합니다
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.instructorId - 강사 ID
 * @returns {Promise<NextResponse>} 200: 강사 정보, 403: 권한 없음, 404: 강사 없음, 500: 서버 오류
 * @example
 * // GET /api/partner/instructors/uuid
 * // Response:
 * {
 *   "id": "uuid",
 *   "partnerId": "uuid",
 *   "name": "김요가",
 *   "gender": "female",
 *   "contact": "010-1234-5678",
 *   "description": "10년 경력의 전문 요가 강사입니다",
 *   "links": {
 *     "website": "https://yogakim.com",
 *     "sns": "https://instagram.com/yogakim"
 *   },
 *   "profileImages": [...],
 *   "experienceTotalYears": 10,
 *   "specialties": [...],
 *   "certificates": [...],
 *   "status": "active",
 *   "createdAt": "2024-01-01T00:00:00Z",
 *   "updatedAt": "2024-01-01T00:00:00Z"
 * }
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    // 파트너 인증
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) return errorResponse;

    const { instructorId } = await params;

    // 강사 상세 정보 조회 (권한 검증 포함)
    const instructor = await instructorService.findInstructorById(
      instructorId,
      partner!.id
    );

    if (!instructor) {
      return NextResponse.json(
        { error: '강사를 찾을 수 없습니다' },
        { status: 404 }
      );
    }

    return NextResponse.json(instructor);
  } catch (error) {
    console.error('Error fetching instructor:', error);

    // 권한 에러 처리
    if (error instanceof Error && error.message.includes('권한')) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }

    return NextResponse.json(
      { error: '강사 조회에 실패했습니다' },
      { status: 500 }
    );
  }
}

/**
 * 강사 정보를 수정합니다
 *
 * @description 파트너가 등록한 강사 정보를 수정합니다
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.instructorId - 강사 ID
 * @param {Object} request.body - 수정할 강사 정보 (모든 필드 선택사항)
 * @param {string} [request.body.name] - 강사 이름
 * @param {'male'|'female'} [request.body.gender] - 성별
 * @param {string} [request.body.contact] - 연락처
 * @param {string} [request.body.description] - 강사 소개
 * @param {Object} [request.body.links] - 링크 정보
 * @param {Array<Object>} [request.body.profileImages] - 프로필 이미지
 * @param {number} [request.body.experienceTotalYears] - 총 경력
 * @param {Array<Object>} [request.body.specialties] - 전문 분야
 * @param {Array<Object>} [request.body.certificates] - 자격증 정보
 * @returns {Promise<NextResponse>} 200: 수정된 강사 정보, 400: 유효성 검증 실패, 403: 권한 없음, 404: 강사 없음, 500: 서버 오류
 * @example
 * // PUT /api/partner/instructors/uuid
 * {
 *   "description": "15년 경력의 요가 마스터입니다",
 *   "experienceTotalYears": 15,
 *   "certificates": [
 *     {
 *       "name": "요가 마스터 자격증",
 *       "issuing_organization": "국제요가협회",
 *       "issue_date": "2024-01-01"
 *     }
 *   ]
 * }
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    // 파트너 인증
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) return errorResponse;

    const { instructorId } = await params;
    const body = await request.json();

    // 입력값 검증 (partial update이므로 모든 필드가 선택사항)
    const validatedData = UpdateInstructorSchema.parse(body);

    // 강사 정보 수정 (권한 검증 포함)
    const updatedInstructor = await instructorService.updateInstructor(
      instructorId,
      partner!.id,
      validatedData
    );

    if (!updatedInstructor) {
      return NextResponse.json(
        { error: '강사를 찾을 수 없습니다' },
        { status: 404 }
      );
    }

    revalidateTag('instructors');

    return NextResponse.json(updatedInstructor);
  } catch (error) {
    console.error('Error updating instructor:', error);

    if (error instanceof ZodError) {
      return NextResponse.json(
        {
          error: '입력값이 올바르지 않습니다',
          details: error.issues,
        },
        { status: 400 }
      );
    }

    // 권한 에러 처리
    if (error instanceof Error && error.message.includes('권한')) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }

    return NextResponse.json(
      { error: '강사 정보 수정에 실패했습니다' },
      { status: 500 }
    );
  }
}

/**
 * 강사를 비활성화합니다 (소프트 삭제)
 *
 * @description 파트너가 등록한 강사를 비활성화합니다. 실제 데이터는 삭제되지 않습니다.
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.instructorId - 강사 ID
 * @returns {Promise<NextResponse>} 204: 성공, 403: 권한 없음, 404: 강사 없음, 500: 서버 오류
 * @example
 * // DELETE /api/partner/instructors/uuid
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ instructorId: string }> }
) {
  try {
    // 파트너 인증
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) return errorResponse;

    const { instructorId } = await params;

    // 강사 비활성화 (권한 검증 포함)
    const success = await instructorService.deleteInstructor(
      instructorId,
      partner!.id
    );

    if (!success) {
      return NextResponse.json(
        { error: '강사를 찾을 수 없습니다' },
        { status: 404 }
      );
    }

    // 성공 시 JSON 응답으로 변경해 클라이언트 파서와 정합성 유지
    return NextResponse.json(
      { message: 'Instructor deleted', instructorId },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting instructor:', error);

    // 권한 에러 처리
    if (error instanceof Error && error.message.includes('권한')) {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }

    return NextResponse.json(
      { error: '강사 비활성화에 실패했습니다' },
      { status: 500 }
    );
  }
}
