import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { classCancellationService } from '@/lib/services/class-cancellation.service';
import { BusinessRuleError } from '@/lib/types/class-cancellation.types';
import { 
  cancelClassRequestSchema,
  cancelClassSuccessResponseSchema,
  ClassCancellationErrorCodes,
  type CancelClassSuccessResponse,
  type ClassCancellationErrorResponse 
} from '@/lib/validations/class-cancellation.validation';

/**
 * 파트너 수업 취소 API
 * POST /api/partner/classes/{id}/schedules/{scheduleGroupId}/cancel
 * 
 * @description
 * - 인증된 파트너가 자신의 클래스(확정/미확정 모두)를 취소
 * - 확정된 클래스의 경우 진행 중이거나 D-1 이후의 수업은 취소 불가
 * - 모든 수강생에게 환불 처리 후 스케줄 그룹 상태를 cancelled로 변경
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; scheduleGroupId: string }> }
) {
  try {
    // 1. 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 요청 데이터 검증
    const body = await request.json();
    const validationResult = cancelClassRequestSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      const errorResponse: ClassCancellationErrorResponse = {
        success: false,
        error: ClassCancellationErrorCodes.VALIDATION_ERROR,
        message: '입력값이 올바르지 않습니다',
        details: validationResult.error.issues,
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { cancelReason } = validationResult.data;
    const { id, scheduleGroupId: scheduleGroupIdParam } = await params;
    const scheduleGroupId = parseInt(scheduleGroupIdParam);

    if (isNaN(scheduleGroupId)) {
      const errorResponse: ClassCancellationErrorResponse = {
        success: false,
        error: ClassCancellationErrorCodes.VALIDATION_ERROR,
        message: '올바른 스케줄 그룹 ID가 아닙니다',
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    console.log('Processing class cancellation:', {
      partnerId: partner!.id,
      classId: id,
      scheduleGroupId,
      cancelReason,
      timestamp: new Date().toISOString()
    });

    // 3. 서비스 레이어를 통한 수업 취소 처리
    const result = await classCancellationService.cancelClass({
      classId: id,
      scheduleGroupId,
      partnerId: partner!.id,
      cancelReason
    });

    console.log('Class cancellation completed:', {
      partnerId: partner!.id,
      classId: result.classId,
      scheduleGroupId: result.scheduleGroupId,
      cancelledAt: result.cancelledAt,
      refundedCount: result.refundedCount,
      totalRefundAmount: result.totalRefundAmount,
    });

    // 4. 응답 데이터 검증 및 포맷팅
    const response: CancelClassSuccessResponse = cancelClassSuccessResponseSchema.parse({
      success: true,
      data: {
        classId: result.classId,
        scheduleGroupId: result.scheduleGroupId,
        cancelledAt: result.cancelledAt,
        refundedCount: result.refundedCount,
        totalRefundAmount: result.totalRefundAmount,
        refunds: result.refunds.map(refund => ({
          enrollmentId: refund.enrollmentId,
          memberId: refund.memberId,
          paymentId: refund.paymentId,
          refundAmount: refund.refundAmount,
          status: refund.status,
        }))
      },
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Class cancellation error:', error);

    // 비즈니스 룰 에러 처리
    if (error instanceof BusinessRuleError) {
      const errorResponse: ClassCancellationErrorResponse = {
        success: false,
        error: error.code,
        message: error.message
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 예상치 못한 에러
    const errorResponse: ClassCancellationErrorResponse = {
      success: false,
      error: ClassCancellationErrorCodes.INTERNAL_ERROR,
      message: '서버 오류가 발생했습니다'
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}