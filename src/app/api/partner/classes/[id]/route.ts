import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { partnerClassService } from '@/lib/services/partner-class.service';
import { updateClassSchema, patchClassSchema } from '@/lib/validations/partner-class.validation';
import { revalidateTag } from 'next/cache';

/**
 * 파트너 클래스 상세 조회
 *
 * @description 파트너가 소유한 특정 클래스의 상세 정보를 조회합니다.
 * 클래스 정보, 스케줄, 스튜디오, 강사 정보를 포함합니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {object} params - URL 파라미터 { id: string }
 *
 * @returns {200} 클래스 상세 조회 성공
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 클래스를 찾을 수 없음
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * GET /api/partner/classes/class-uuid
 *
 * Response:
 * {
 *   "id": "class-uuid",
 *   "partnerId": "partner-uuid",
 *   "studioId": "studio-uuid",
 *   "instructorId": "instructor-uuid",
 *   "title": "초급 요가 클래스",
 *   "description": "요가 기초를 배우는 클래스입니다",
 *   "category": "yoga",
 *   "level": "beginner",
 *   "target": "all",
 *   "maxParticipants": 8,
 *   "pricePerSession": 15000,
 *   "sessionDurationMinutes": 60,
 *   "durationWeeks": 4,
 *   "sessionsPerWeek": 2,
 *   "images": [...],
 *   "status": "active",
 *   "visible": true,
 *   "schedules": [
 *     {
 *       "id": 1,
 *       "classId": "class-uuid",
 *       "dayOfWeek": 1,
 *       "startTime": "10:00",
 *       "endTime": "11:00",
 *       "status": "pending"
 *     }
 *   ],
 *   "studio": {
 *     "id": "studio-uuid",
 *     "name": "강남 요가 스튜디오",
 *     "address": "서울시 강남구 ..."
 *   },
 *   "instructor": {
 *     "id": "instructor-uuid",
 *     "name": "김강사",
 *     "profileImages": [...]
 *   },
 *   "createdAt": "2024-01-01T00:00:00.000Z",
 *   "updatedAt": "2024-01-01T00:00:00.000Z"
 * }
 * ```
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const { id: classId } = await params;

    // 서비스 호출
    const classDetail = await partnerClassService.getClassDetail(
      partner!,
      classId
    );

    if (!classDetail) {
      return NextResponse.json(
        { message: '클래스를 찾을 수 없습니다.' },
        { status: 404 }
      );
    }

    return NextResponse.json(classDetail);
  } catch (error) {
    console.error('클래스 상세 조회 오류:', error);

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (
        error.message.includes('access denied') ||
        error.message.includes('not found')
      ) {
        return NextResponse.json({ message: error.message }, { status: 403 });
      }
    }

    return NextResponse.json(
      { message: '클래스 상세 조회 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 클래스 수정
 *
 * @description 파트너가 소유한 클래스의 정보를 수정합니다.
 * 클래스 정보와 스케줄 정보를 함께 수정할 수 있습니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {object} params - URL 파라미터 { id: string }
 *
 * @returns {200} 클래스 수정 성공
 * @returns {400} 잘못된 요청 데이터
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 클래스를 찾을 수 없음
 * @returns {422} 잘못된 데이터 형식
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * PUT /api/partner/classes/class-uuid
 * Content-Type: application/json
 *
 * {
 *   "title": "수정된 클래스 제목",
 *   "description": "수정된 설명",
 *   "maxParticipants": 10,
 *   "pricePerSession": 20000,
 *   "visible": false,
 *   "schedules": [
 *     {
 *       "dayOfWeek": 2,
 *       "startTime": "14:00",
 *       "endTime": "15:30"
 *     }
 *   ]
 * }
 *
 * Response:
 * {
 *   "id": "class-uuid",
 *   "title": "수정된 클래스 제목",
 *   "description": "수정된 설명",
 *   "maxParticipants": 10,
 *   "pricePerSession": 20000,
 *   "visible": false,
 *   "schedules": [
 *     {
 *       "id": 2,
 *       "classId": "class-uuid",
 *       "dayOfWeek": 2,
 *       "startTime": "14:00",
 *       "endTime": "15:30",
 *       "status": "pending"
 *     }
 *   ],
 *   "updatedAt": "2024-01-01T12:00:00.000Z"
 * }
 * ```
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const { id: classId } = await params;
    const body = await request.json();

    // 요청 데이터 검증
    const validatedData = updateClassSchema.parse(body);

    // 서비스 호출
    const updatedClass = await partnerClassService.updateClass(
      partner!,
      classId,
      validatedData
    );

    revalidateTag('/api/partner/classes');
    revalidateTag('/api/partner/classes/' + classId);

    return NextResponse.json(updatedClass);
  } catch (error) {
    console.error('클래스 수정 오류:', error);

    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        {
          message: '요청 데이터가 올바르지 않습니다.',
          errors: error,
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (
        error.message.includes('Class not found') ||
        error.message.includes('access denied')
      ) {
        return NextResponse.json(
          { message: '클래스를 찾을 수 없거나 수정 권한이 없습니다.' },
          { status: 404 }
        );
      }

      if (error.message.includes('does not belong')) {
        return NextResponse.json({ message: error.message }, { status: 422 });
      }
    }

    return NextResponse.json(
      { message: '클래스 수정 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 클래스 부분 업데이트
 *
 * @description 파트너가 소유한 클래스의 특정 필드만 업데이트합니다.
 * visible, status 필드를 개별적으로 변경할 수 있습니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {object} params - URL 파라미터 { id: string }
 *
 * @returns {200} 업데이트 성공 (빈 응답)
 * @returns {400} 잘못된 요청 데이터
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 클래스를 찾을 수 없음
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * PATCH /api/partner/classes/class-uuid
 * Content-Type: application/json
 *
 * {
 *   "visible": false
 * }
 *
 * Response: 200 OK (empty body)
 * ```
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const { id: classId } = await params;
    const body = await request.json();

    // 요청 데이터 검증
    const validatedData = patchClassSchema.parse(body);

    // 서비스 호출
    await partnerClassService.patchClass(partner!, classId, validatedData);

    revalidateTag('/api/partner/classes');
    revalidateTag('/api/partner/classes/' + classId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('클래스 부분 업데이트 오류:', error);

    // Zod 검증 오류
    if (error && typeof error === 'object' && 'issues' in error) {
      return NextResponse.json(
        {
          message: '요청 데이터가 올바르지 않습니다.',
          errors: error,
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // 비즈니스 로직 오류
      if (
        error.message.includes('Class not found') ||
        error.message.includes('access denied')
      ) {
        return NextResponse.json(
          { message: '클래스를 찾을 수 없거나 수정 권한이 없습니다.' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { message: '클래스 업데이트 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 파트너 클래스 삭제
 *
 * @description 파트너가 소유한 클래스를 삭제합니다 (소프트 삭제).
 * 활성화된 수강신청이 있는 경우 삭제할 수 없습니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체 (인증 정보 포함)
 * @param {object} params - URL 파라미터 { id: string }
 *
 * @returns {200} 클래스 삭제 성공
 * @returns {401} 인증되지 않은 요청 (로그인 필요)
 * @returns {403} 권한 없음 (파트너 계정이 아니거나 비활성 상태)
 * @returns {404} 클래스를 찾을 수 없음
 * @returns {409} 활성화된 수강신청이 있어 삭제 불가
 * @returns {500} 서버 내부 오류
 *
 * @example
 * ```
 * DELETE /api/partner/classes/class-uuid
 *
 * Response:
 * {
 *   "message": "클래스가 성공적으로 삭제되었습니다."
 * }
 * ```
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const { id: classId } = await params;

    // 서비스 호출
    await partnerClassService.deleteClass(partner!, classId);

    return NextResponse.json({
      message: '클래스가 성공적으로 삭제되었습니다.',
    });
  } catch (error) {
    console.error('클래스 삭제 오류:', error);

    if (error instanceof Error) {
      // 비즈니스 로직 오류 분류
      if (
        error.message.includes('Class not found') ||
        error.message.includes('access denied')
      ) {
        return NextResponse.json(
          { message: '클래스를 찾을 수 없거나 삭제 권한이 없습니다.' },
          { status: 404 }
        );
      }

      if (error.message.includes('active enrollments')) {
        return NextResponse.json(
          { message: '활성화된 수강신청이 있어 클래스를 삭제할 수 없습니다.' },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { message: '클래스 삭제 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
