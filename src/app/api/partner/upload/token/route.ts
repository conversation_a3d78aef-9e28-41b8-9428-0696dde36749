import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import {
  generateUniqueFileName,
  generateStudioImagePath,
  generateInstructorImagePath,
  generateClassImagePath,
} from '@/lib/utils/upload';
import { createSupabaseClient } from '@/lib/supabase/server';

/**
 * 파트너 이미지 직접 업로드용 signed URL 생성
 * @method POST
 * @description 클라이언트가 Supabase에 직접 업로드할 수 있는 signed URL을 생성합니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체
 *
 * Request Body:
 * {
 *   "fileName": "image.jpg",
 *   "fileSize": 1024000,
 *   "contentType": "image/jpeg",
 *   "type": "studio" | "instructor" | "class",
 *   "studioId": "optional-studio-id",
 *   "prefix": "optional-prefix"
 * }
 *
 * @returns {200} signed URL 생성 성공
 * @returns {400} 잘못된 요청
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음
 * @returns {500} 서버 내부 오류
 *
 * @example Response
 * {
 *   "success": true,
 *   "uploadUrl": "https://[project-id].supabase.co/storage/v1/object/sign/upload/...",
 *   "filePath": "studios/[partnerId]/[studioId]/featured-123456789.jpg",
 *   "publicUrl": "https://[project-id].supabase.co/storage/v1/object/public/images/studios/...",
 *   "token": "upload-token-for-verification"
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();
    const { fileName, fileSize, contentType, type, studioId, prefix } = body;

    // 필수 필드 검증
    if (!fileName || !fileSize || !contentType || !type) {
      return NextResponse.json(
        {
          error:
            '필수 필드가 누락되었습니다. (fileName, fileSize, contentType, type)',
        },
        { status: 400 }
      );
    }

    if (!['studio', 'instructor', 'class'].includes(type)) {
      return NextResponse.json(
        { error: '올바른 업로드 타입을 선택해주세요.' },
        { status: 400 }
      );
    }

    // 파일 크기 검증 (50MB 제한)
    const maxFileSize = 50 * 1024 * 1024; // 50MB
    if (fileSize > maxFileSize) {
      return NextResponse.json(
        {
          error: `파일 크기는 ${maxFileSize / 1024 / 1024}MB 이하여야 합니다.`,
        },
        { status: 400 }
      );
    }

    // 지원 파일 타입 검증
    const supportedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'image/heic',
      'image/heif',
    ];
    if (!supportedTypes.includes(contentType.toLowerCase())) {
      return NextResponse.json(
        {
          error:
            '지원되지 않는 파일 형식입니다. JPG, PNG, WebP, HEIC 파일만 업로드 가능합니다.',
        },
        { status: 400 }
      );
    }

    // 고유한 파일명 생성
    const uniqueFileName = generateUniqueFileName(fileName, prefix);

    // 타입별 파일 경로 생성
    let filePath: string;
    switch (type) {
      case 'studio':
        if (!partner!.id) {
          return NextResponse.json(
            { error: '파트너 ID가 필요합니다.' },
            { status: 400 }
          );
        }
        filePath = generateStudioImagePath(
          partner!.id,
          studioId || null,
          uniqueFileName
        );
        break;

      case 'instructor':
        filePath = generateInstructorImagePath(partner!.id, uniqueFileName);
        break;

      case 'class':
        filePath = generateClassImagePath(null, uniqueFileName);
        break;

      default:
        return NextResponse.json(
          { error: '지원되지 않는 업로드 타입입니다.' },
          { status: 400 }
        );
    }

    // Supabase 클라이언트 생성
    const supabase = await createSupabaseClient();

    // signed URL 생성 (업로드용)
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('images')
      .createSignedUploadUrl(filePath, {
        upsert: false, // 덮어쓰기 방지
      });

    if (uploadError) {
      console.error('Signed URL 생성 오류:', uploadError);
      return NextResponse.json(
        { error: 'signed URL 생성에 실패했습니다.' },
        { status: 500 }
      );
    }

    // 공개 URL 생성
    const { data: publicData } = supabase.storage
      .from('images')
      .getPublicUrl(filePath);

    // 업로드 검증용 토큰 생성 (JSON 기반)
    const tokenData = {
      partnerId: partner!.id,
      filePath: filePath,
      timestamp: Date.now(),
      type: type,
    };
    const uploadToken = Buffer.from(JSON.stringify(tokenData)).toString(
      'base64'
    );

    console.log('✅ Signed URL 생성 성공:', {
      partnerId: partner!.id,
      filePath,
      fileSize: `${(fileSize / 1024 / 1024).toFixed(2)}MB`,
    });

    return NextResponse.json({
      success: true,
      uploadUrl: uploadData.signedUrl,
      filePath,
      publicUrl: publicData.publicUrl,
      token: uploadToken,
      metadata: {
        fileName: uniqueFileName,
        originalFileName: fileName,
        fileSize,
        contentType,
        type,
        partnerId: partner!.id,
        createdAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Upload token generation error:', error);
    return NextResponse.json(
      { error: 'signed URL 생성 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
