import { NextRequest, NextResponse } from 'next/server';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { createSupabaseClient } from '@/lib/supabase/server';

/**
 * 파트너 이미지 직접 업로드 완료 처리
 * @method POST
 * @description 클라이언트에서 직접 업로드 완료 후 메타데이터를 서버에 저장합니다.
 *
 * @param {NextRequest} request - Next.js 요청 객체
 *
 * Request Body:
 * {
 *   "filePath": "studios/[partnerId]/[studioId]/featured-123456789.jpg",
 *   "publicUrl": "https://[project-id].supabase.co/storage/v1/object/public/images/studios/...",
 *   "token": "upload-token-from-previous-step",
 *   "metadata": {
 *     "fileName": "featured-123456789.jpg",
 *     "originalFileName": "my-image.jpg",
 *     "fileSize": 1024000,
 *     "contentType": "image/jpeg",
 *     "type": "studio",
 *     "partnerId": "partner-id",
 *     "createdAt": "2023-..."
 *   },
 *   "uploadSuccess": true
 * }
 *
 * @returns {200} 업로드 완료 처리 성공
 * @returns {400} 잘못된 요청
 * @returns {401} 인증되지 않은 요청
 * @returns {403} 권한 없음
 * @returns {500} 서버 내부 오류
 *
 * @example Response
 * {
 *   "success": true,
 *   "result": {
 *     "url": "https://[project-id].supabase.co/storage/v1/object/public/images/studios/...",
 *     "path": "studios/[partnerId]/[studioId]/featured-123456789.jpg"
 *   }
 * }
 */
export async function POST(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();
    const { filePath, publicUrl, token, metadata, uploadSuccess } = body;

    // 필수 필드 검증
    if (!filePath || !publicUrl || !token || !metadata) {
      return NextResponse.json(
        {
          error:
            '필수 필드가 누락되었습니다. (filePath, publicUrl, token, metadata)',
        },
        { status: 400 }
      );
    }

    // 업로드 성공 여부 확인
    if (!uploadSuccess) {
      console.log('❌ 클라이언트 업로드 실패 보고됨:', { filePath, metadata });
      return NextResponse.json(
        { error: '클라이언트에서 업로드가 실패했습니다.' },
        { status: 400 }
      );
    }

    // 토큰 검증 (JSON 기반)
    try {
      const decodedToken = Buffer.from(token, 'base64').toString('utf-8');
      const tokenData = JSON.parse(decodedToken);
      const {
        partnerId: tokenPartnerId,
        filePath: tokenFilePath,
        timestamp,
        type: tokenType,
      } = tokenData;

      // 토큰 만료 시간 검증 (1시간)
      const tokenAge = Date.now() - timestamp;
      if (tokenAge > 60 * 60 * 1000) {
        console.log('❌ 토큰 만료:', {
          tokenAge: `${Math.round(tokenAge / 1000 / 60)}분`,
        });
        return NextResponse.json(
          { error: '토큰이 만료되었습니다.' },
          { status: 403 }
        );
      }

      if (tokenPartnerId !== partner!.id || tokenFilePath !== filePath) {
        console.log('❌ 토큰 검증 실패:', {
          tokenPartnerId,
          partnerId: partner!.id,
          tokenFilePath,
          filePath,
        });
        return NextResponse.json(
          { error: '유효하지 않은 업로드 토큰입니다.' },
          { status: 403 }
        );
      }
    } catch (tokenError) {
      console.error('토큰 검증 오류:', tokenError);
      return NextResponse.json(
        { error: '토큰 형식이 올바르지 않습니다.' },
        { status: 400 }
      );
    }

    // 메타데이터 검증
    if (metadata.partnerId !== partner!.id) {
      return NextResponse.json(
        { error: '파트너 ID가 일치하지 않습니다.' },
        { status: 403 }
      );
    }

    // Supabase 클라이언트 생성
    const supabase = await createSupabaseClient();

    // 파일이 실제로 업로드되었는지 확인
    const { data: fileData, error: checkError } = await supabase.storage
      .from('images')
      .list(filePath.split('/').slice(0, -1).join('/'), {
        search: filePath.split('/').pop(),
      });

    if (checkError || !fileData || fileData.length === 0) {
      console.log('❌ 업로드된 파일을 찾을 수 없음:', { filePath, checkError });
      return NextResponse.json(
        { error: '업로드된 파일을 확인할 수 없습니다.' },
        { status: 404 }
      );
    }

    // 추가 메타데이터 저장 (필요시 데이터베이스에 저장)
    // 예: 업로드 로그, 파일 정보 등을 별도 테이블에 저장

    console.log('✅ 직접 업로드 완료:', {
      partnerId: partner!.id,
      filePath,
      fileSize: `${(metadata.fileSize / 1024 / 1024).toFixed(2)}MB`,
      originalFileName: metadata.originalFileName,
    });

    // 업로드 통계 업데이트 (선택사항)
    // await updateUploadStats(partner.id, metadata);

    return NextResponse.json({
      success: true,
      result: {
        url: publicUrl,
        path: filePath,
        uploadedAt: new Date().toISOString(),
        metadata: {
          fileName: metadata.fileName,
          originalFileName: metadata.originalFileName,
          fileSize: metadata.fileSize,
          contentType: metadata.contentType,
        },
      },
    });
  } catch (error) {
    console.error('Upload complete processing error:', error);
    return NextResponse.json(
      { error: '업로드 완료 처리 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 업로드 실패 처리 (선택사항)
 * @method DELETE
 * @description 업로드 실패 시 정리 작업을 수행합니다.
 */
export async function DELETE(request: NextRequest) {
  try {
    // 파트너 인증 확인
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const body = await request.json();
    const { filePath, token } = body;

    if (!filePath || !token) {
      return NextResponse.json(
        { error: '필수 필드가 누락되었습니다.' },
        { status: 400 }
      );
    }

    // 토큰 검증 (JSON 기반)
    try {
      const decodedToken = Buffer.from(token, 'base64').toString('utf-8');
      const tokenData = JSON.parse(decodedToken);
      const {
        partnerId: tokenPartnerId,
        filePath: tokenFilePath,
        timestamp,
      } = tokenData;

      // 토큰 만료 시간 검증 (1시간)
      const tokenAge = Date.now() - timestamp;
      if (tokenAge > 60 * 60 * 1000) {
        console.log('❌ 토큰 만료:', {
          tokenAge: `${Math.round(tokenAge / 1000 / 60)}분`,
        });
        return NextResponse.json(
          { error: '토큰이 만료되었습니다.' },
          { status: 403 }
        );
      }

      if (tokenPartnerId !== partner!.id || tokenFilePath !== filePath) {
        return NextResponse.json(
          { error: '유효하지 않은 토큰입니다.' },
          { status: 403 }
        );
      }
    } catch (tokenError) {
      console.error('토큰 검증 오류:', tokenError);
      return NextResponse.json(
        { error: '토큰 형식이 올바르지 않습니다.' },
        { status: 400 }
      );
    }

    // Supabase 클라이언트 생성
    const supabase = await createSupabaseClient();

    // 업로드 실패한 파일이 있다면 정리
    const { error: deleteError } = await supabase.storage
      .from('images')
      .remove([filePath]);

    if (deleteError && !deleteError.message.includes('not found')) {
      console.error('파일 정리 오류:', deleteError);
    }

    console.log('🧹 업로드 실패 파일 정리 완료:', {
      partnerId: partner!.id,
      filePath,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Upload cleanup error:', error);
    return NextResponse.json(
      { error: '업로드 정리 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
