import { getMemberFromRequest } from '@/lib/auth/member.server';
import { db } from '@/lib/db';
import { schedule_requests } from '@/lib/db/schema';
import { scheduleRequestSchema } from '@/schemas/schedule-request';
import { and, eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

// POST - 희망 스케줄 신청
export async function POST(request: NextRequest) {
  try {
    // 로그인 확인
    const authResult = await getMemberFromRequest();
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.statusCode || 401 }
      );
    }
    const member = authResult.member!;

    // 요청 데이터 파싱 및 검증
    const body = await request.json();
    const validatedData = scheduleRequestSchema.parse(body);

    // 중복 신청 확인 (같은 클래스에 대해 pending 상태인 신청이 있는지)
    const existingRequest = await db
      .select()
      .from(schedule_requests)
      .where(
        and(
          eq(schedule_requests.member_id, member.id),
          eq(schedule_requests.class_id, validatedData.class_id),
          eq(schedule_requests.status, 'pending')
        )
      )
      .limit(1);

    if (existingRequest.length > 0) {
      return NextResponse.json(
        { error: '이미 해당 클래스에 대한 신청이 진행 중입니다.' },
        { status: 400 }
      );
    }

    // 스케줄 신청 생성
    const [newRequest] = await db
      .insert(schedule_requests)
      .values({
        member_id: member.id,
        class_id: validatedData.class_id,
        preferred_days: validatedData.preferred_days,
        preferred_start_time: validatedData.preferred_start_time,
        preferred_end_time: validatedData.preferred_end_time,
        status: 'pending',
      })
      .returning();

    return NextResponse.json(
      {
        message: '희망 스케줄 신청이 완료되었습니다.',
        data: newRequest,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Schedule request POST error:', error);

    if (error instanceof Error && error.message.includes('validation')) {
      return NextResponse.json(
        { error: '입력 데이터가 올바르지 않습니다.' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '신청 처리 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
