import { NextRequest, NextResponse } from 'next/server';
import { getMemberFromRequest } from '@/lib/auth/member.server';
import { profileService } from '@/lib/services/profile.service';

/**
 * 회원 프로필 정보 조회 API
 *
 * @description
 * 인증된 사용자의 프로필 정보를 조회합니다.
 * - 회원 닉네임
 * - 총 수업일수 (진행중/완료된 클래스의 실제 수업일)
 * - 출석일수 (현재는 총 수업일수와 동일)
 * - 다음 수업 일정 최대 3개
 *
 * @param request - Next.js 요청 객체 (인증 토큰 포함)
 * @returns 회원 프로필 정보 또는 에러 응답
 *
 * @throws {401} 인증되지 않은 사용자
 * @throws {404} 회원 정보를 찾을 수 없음
 * @throws {500} 서버 내부 오류
 */
export async function GET(): Promise<NextResponse> {
  try {
    // 1. 회원 인증 확인
    const authResult = await getMemberFromRequest();

    console.log('api profile authResult', authResult);

    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error || 'Unauthorized',
          },
        },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. 프로필 정보 조회
    const profileData = await profileService.getMemberProfile(
      authResult.member!.id
    );

    if (!profileData) {
      return NextResponse.json(
        {
          error: {
            message: 'Member not found',
          },
        },
        { status: 404 }
      );
    }

    return NextResponse.json(profileData);
  } catch (error) {
    console.error('Error fetching user profile:', error);

    return NextResponse.json(
      {
        error: {
          message: 'Error fetching user profile',
        },
      },
      { status: 500 }
    );
  }
}
