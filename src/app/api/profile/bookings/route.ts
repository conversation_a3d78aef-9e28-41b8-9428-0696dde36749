import { BookingActionRequestSchema } from '@/schemas/profile';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getMemberFromRequest } from '@/lib/auth/member.server';
import { profileService } from '@/lib/services/profile.service';

/**
 * 회원 수강신청 내역 조회 API
 *
 * @description
 * 인증된 사용자의 수강신청 내역을 상태별로 조회합니다.
 * - 신청(시작 전): 스케줄 미확정 또는 시작일 전
 * - 진행중: 현재 수업 기간 내
 * - 완료: 수업 종료일이 지난 것
 *
 * @returns 상태별 수강신청 내역
 *
 * @throws {401} 인증되지 않은 사용자
 * @throws {500} 서버 내부 오류
 */
export async function GET() {
  try {
    // 1. 회원 인증 확인
    const authResult = await getMemberFromRequest();

    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error || 'Unauthorized',
          },
        },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. 수강신청 내역 조회
    const bookings = await profileService.getMemberBookings(
      authResult.member!.id
    );

    return NextResponse.json(bookings);
  } catch (error) {
    console.error('Error fetching booking history:', error);

    return NextResponse.json(
      {
        error: {
          message: 'Error fetching booking history',
        },
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedRequest = BookingActionRequestSchema.parse(body);

    const { action, bookingId } = validatedRequest;

    switch (action) {
      case 'pay_deposit':
        console.log(`Processing deposit payment for booking: ${bookingId}`);
        return NextResponse.json({
          message: '예약금 결제가 완료되었습니다.',
        });

      case 'cancel_booking':
        console.log(`Cancelling booking: ${bookingId}`);
        return NextResponse.json({
          message: '예약이 취소되었습니다.',
        });

      default:
        return NextResponse.json(
          {
            error: {
              message: '지원하지 않는 작업입니다.',
            },
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error processing booking action:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: {
            message: '잘못된 요청 형식입니다.',
          },
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: {
          message: '서버 오류가 발생했습니다.',
        },
      },
      { status: 500 }
    );
  }
}
