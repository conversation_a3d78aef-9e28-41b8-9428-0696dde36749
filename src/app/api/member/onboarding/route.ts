import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { member_preferences } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import {
  OnboardingDataSchema,
  type OnboardingData,
} from '@/lib/constants/onboarding';
import { createSupabaseClient } from '@/lib/supabase/server';

/**
 * 학생 온보딩 API 엔드포인트
 *
 * @description
 * - POST: 온보딩 데이터 저장 (6단계 완료 후)
 * - GET: 온보딩 상태 확인 (완료 여부)
 */

/**
 * 온보딩 데이터 저장 API
 * POST /api/member/onboarding
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseClient();
    // 현재 사용자 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 파트너 계정인 경우 비로그인으로 처리
    const { data: partner } = await supabase
      .from('partners')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (partner) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 요청 데이터 파싱 및 유효성 검사
    const requestData = await request.json();
    const validationResult = OnboardingDataSchema.safeParse(requestData);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: '유효하지 않은 데이터입니다.',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const onboardingData: OnboardingData = validationResult.data;

    // 기존 온보딩 완료 여부 확인
    const [existingPreferences] = await db
      .select({ id: member_preferences.id })
      .from(member_preferences)
      .where(eq(member_preferences.member_id, user.id))
      .limit(1);

    if (existingPreferences) {
      return NextResponse.json(
        { error: '이미 온보딩을 완료한 사용자입니다.' },
        { status: 409 }
      );
    }

    // 데이터베이스 트랜잭션 시작
    const result = await db.transaction(async tx => {
      // member_preferences 테이블에 선호도 데이터 저장 (선택사항 필드들)
      const [preferences] = await tx
        .insert(member_preferences)
        .values({
          member_id: user.id,
          fitness_goals: onboardingData.fitnessGoals || [],
          preferred_stations: onboardingData.preferredStations || [],
          preferred_specialties: onboardingData.preferredSpecialties || [],
          preferred_days: onboardingData.preferredDays || [],
          preferred_time_slots: onboardingData.preferredTimeSlots || [],
          fitness_level: onboardingData.fitnessLevel || 'all_levels',
        })
        .returning({
          id: member_preferences.id,
          created_at: member_preferences.created_at,
        });

      return {
        preferences_id: preferences.id,
        completed_at: preferences.created_at,
      };
    });

    // 성공 응답
    return NextResponse.json({
      success: true,
      message: '온보딩이 완료되었습니다.',
      data: {
        preferencesId: result.preferences_id,
        completedAt: result.completed_at,
      },
    });
  } catch (error) {
    console.error('온보딩 API 오류:', error);

    return NextResponse.json(
      {
        error: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
      { status: 500 }
    );
  }
}

/**
 * 온보딩 상태 확인 API
 * GET /api/member/onboarding
 */
export async function GET() {
  try {
    const supabase = await createSupabaseClient();

    // 현재 사용자 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 파트너 계정인 경우 비로그인으로 처리
    const { data: partner } = await supabase
      .from('partners')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (partner) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 온보딩 완료 상태 확인 (member_preferences 레코드 존재 여부)
    const [preferences] = await db
      .select({
        id: member_preferences.id,
        created_at: member_preferences.created_at,
      })
      .from(member_preferences)
      .where(eq(member_preferences.member_id, user.id))
      .limit(1);

    const isCompleted = !!preferences;
    const completedAt = preferences?.created_at?.toISOString();

    return NextResponse.json({
      isCompleted,
      completedAt,
      currentStep: isCompleted ? undefined : 1, // 미완료시 1단계부터 시작
    });
  } catch (error) {
    console.error('온보딩 상태 확인 오류:', error);

    return NextResponse.json(
      { error: '서버 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}

/**
 * 온보딩 데이터 조회 API (선택사항)
 * 이미 완료된 온보딩 데이터를 조회할 때 사용
 */
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createSupabaseClient();

    // 현재 사용자 확인
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 파트너 계정인 경우 비로그인으로 처리
    const { data: partner } = await supabase
      .from('partners')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (partner) {
      return NextResponse.json(
        { error: '인증되지 않은 사용자입니다.' },
        { status: 401 }
      );
    }

    // 요청 데이터 파싱 및 유효성 검사
    const requestData = await request.json();
    const validationResult = OnboardingDataSchema.safeParse(requestData);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: '유효하지 않은 데이터입니다.',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const onboardingData: OnboardingData = validationResult.data;

    // 기존 선호도 데이터 확인
    const [existingPreferences] = await db
      .select({ id: member_preferences.id })
      .from(member_preferences)
      .where(eq(member_preferences.member_id, user.id))
      .limit(1);

    if (!existingPreferences) {
      return NextResponse.json(
        { error: '온보딩을 먼저 완료해주세요.' },
        { status: 404 }
      );
    }

    // 데이터베이스 트랜잭션으로 업데이트
    const result = await db.transaction(async tx => {
      // member_preferences 테이블 업데이트
      const [preferences] = await tx
        .update(member_preferences)
        .set({
          fitness_goals: onboardingData.fitnessGoals,
          preferred_stations: onboardingData.preferredStations,
          preferred_specialties: onboardingData.preferredSpecialties,
          preferred_days: onboardingData.preferredDays,
          preferred_time_slots: onboardingData.preferredTimeSlots,
          fitness_level: onboardingData.fitnessLevel,
          updated_at: new Date(),
        })
        .where(eq(member_preferences.member_id, user.id))
        .returning({
          id: member_preferences.id,
          updated_at: member_preferences.updated_at,
        });

      return {
        preferences_id: preferences.id,
        updated_at: preferences.updated_at,
      };
    });

    // 성공 응답
    return NextResponse.json({
      success: true,
      message: '선호도 설정이 업데이트되었습니다.',
      data: {
        preferencesId: result.preferences_id,
        updatedAt: result.updated_at,
      },
    });
  } catch (error) {
    console.error('온보딩 업데이트 API 오류:', error);

    return NextResponse.json(
      {
        error: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
      { status: 500 }
    );
  }
}
