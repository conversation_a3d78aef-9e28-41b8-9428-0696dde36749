import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { members } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';
import { getMemberIdFromRequest } from '@/lib/auth/member.server';
import { createSupabaseClient } from '@/lib/supabase/server';

// 요청 스키마
const UpdateMemberSchema = z.object({
  name: z
    .string()
    .min(2, '이름은 2자 이상이어야 합니다.')
    .max(50, '이름은 50자 이하여야 합니다.'),
  phone: z
    .string()
    .regex(
      /^01[0-9]-[0-9]{4}-[0-9]{4}$/,
      '올바른 휴대폰 번호 형식이 아닙니다.'
    ),
  birthDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '올바른 날짜 형식이 아닙니다.')
    .refine(date => {
      const birthDate = new Date(date);
      const today = new Date();
      return birthDate <= today;
    }, '미래 날짜는 선택할 수 없습니다.')
    .refine(date => {
      const birthDate = new Date(date);
      const minDate = new Date('1900-01-01');
      return birthDate >= minDate;
    }, '올바른 생년월일을 입력해주세요.'),
  gender: z.enum(['male', 'female'], '올바른 성별을 선택해주세요.'),
  agreements: z
    .object({
      age: z.boolean().optional(),
      terms: z.boolean().optional(),
      privacy: z.boolean().optional(),
      location: z.boolean().optional(),
      marketing: z.boolean().optional(),
      timestamp: z.string().datetime(),
    })
    .refine(
      data => data.age && data.terms && data.privacy,
      '필수 약관에 동의해야 합니다.'
    ),
});

/**
 * 회원 필수 정보 업데이트 API
 * PUT /api/member/signup-complete
 */
export async function PUT(request: NextRequest) {
  try {
    // 1. 인증 확인
    const memberId = await getMemberIdFromRequest();

    if (!memberId) {
      return NextResponse.json(
        { success: false, message: '로그인이 필요합니다.' },
        { status: 401 }
      );
    }

    // 2. 요청 데이터 파싱 및 검증
    const body = await request.json();
    const validationResult = UpdateMemberSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: '입력 데이터가 올바르지 않습니다.',
          errors: validationResult.error?.issues || [],
        },
        { status: 400 }
      );
    }

    const { name, phone, birthDate, gender, agreements } =
      validationResult.data;

    // 3. 기존 정보 확인 (이미 입력된 경우) - 존재하는 컬럼만 선택
    const [existingMember] = await db
      .select({
        id: members.id,
        nickname: members.nickname,
        email: members.email,
        status: members.status,
      })
      .from(members)
      .where(eq(members.id, memberId))
      .limit(1);

    if (!existingMember) {
      return NextResponse.json(
        {
          success: false,
          message: '회원 정보를 찾을 수 없습니다.',
        },
        { status: 404 }
      );
    }

    // 4. Service Role을 사용해 회원 정보 업데이트 (존재하지 않는 컬럼 우회)
    // const adminClient = createServerClient(
    //   process.env.NEXT_PUBLIC_SUPABASE_URL!,
    //   process.env.SUPABASE_SERVICE_ROLE_KEY!,
    //   {
    //     cookies: {
    //       get: () => undefined,
    //       set: () => {},
    //       remove: () => {},
    //     },
    //   }
    // );

    const supabase = await createSupabaseClient();

    const { data: updatedMember, error: updateError } = await supabase
      .from('members')
      .update({
        nickname: name, // name 대신 nickname 필드 사용
        // 추후 스키마 동기화 시 다른 필드들 추가 가능
      })
      .eq('id', memberId)
      .select('id, nickname, email')
      .single();

    if (updateError || !updatedMember) {
      console.error('회원 정보 업데이트 오류:', updateError);
      throw new Error('회원 정보 업데이트에 실패했습니다.');
    }

    return NextResponse.json({
      success: true,
      message: '회원 정보가 성공적으로 업데이트되었습니다.',
      data: {
        id: updatedMember.id,
        name: updatedMember.nickname,
        email: updatedMember.email,
      },
    });
  } catch (error) {
    console.error('회원 정보 업데이트 오류:', error);
    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
        error:
          process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
