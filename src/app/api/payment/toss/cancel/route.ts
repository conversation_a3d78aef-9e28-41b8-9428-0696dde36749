import { TossPaymentError } from '@/lib/api/toss-payments';
import { requireMemberAuth } from '@/lib/auth/member.server';
import { enrollmentService } from '@/lib/services/enrollment.service';
import {
  cancelPaymentRequestSchema,
  PaymentErrorCodes,
  type CancelPaymentErrorResponse,
} from '@/lib/validations/payment.validation';
import { NextRequest, NextResponse } from 'next/server';

/**
 * 결제 취소 API
 * POST /api/payment/toss/cancel
 *
 * @description
 * - 인증된 멤버의 결제 취소를 처리합니다
 * - 본인의 결제에 대해서만 취소 가능합니다
 * - TossPayments API를 통해 실제 결제를 취소합니다
 * - 전체 취소 및 부분 취소를 지원합니다
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 인증 확인
    const { member, errorResponse } = await requireMemberAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 요청 데이터 검증
    const body = await request.json();
    const validationResult = cancelPaymentRequestSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      const errorResponse: CancelPaymentErrorResponse = {
        success: false,
        error: PaymentErrorCodes.VALIDATION_ERROR,
        message: '입력값이 올바르지 않습니다',
        details: validationResult.error.issues,
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { paymentKey, cancelReason } = validationResult.data;

    console.log('Processing payment cancellation:', {
      memberId: member!.id,
      paymentKey: paymentKey.substring(0, 10) + '...', // 보안을 위해 일부만
      timestamp: new Date().toISOString(),
    });

    // 3. 서비스 레이어를 통한 결제 취소 처리
    const result = await enrollmentService.cancelPayment({
      paymentKey,
      memberId: member!.id,
      cancelReason, // 사용자 입력 취소 사유 전달
    });

    console.log('Payment cancelled successfully:', {
      memberId: member!.id,
      paymentId: result.payment.id,
      enrollmentId: result.enrollment.id,
      status: result.payment.paymentStatus,
      enrollmentStatus: result.enrollment.status,
      refundAmount: result.refundPolicy.refundAmount,
      refundRate: result.refundPolicy.refundRate,
    });

    // 성공 응답 반환
    const successResponse = {
      success: true,
      cancellation: {
        cancelAmount: result.tossResult.cancels[0]?.cancelAmount || result.payment.amount,
        refundAmount: result.refundPolicy.refundAmount,
        refundRate: result.refundPolicy.refundRate,
        cancelReason: result.refundPolicy.reason,
        canceledAt: result.tossResult.canceledAt,
      },
      enrollment: {
        id: result.enrollment.id,
        status: result.enrollment.status,
        updatedAt: result.enrollment.updatedAt.toISOString(),
      },
      payment: {
        id: result.payment.id,
        status: result.payment.paymentStatus,
        updatedAt: result.payment.updatedAt.toISOString(),
      },
    };

    return NextResponse.json(successResponse, { status: 200 });
  } catch (error) {
    console.error('Payment cancellation error:', error);

    // 비즈니스 룰 에러 처리
    if (error instanceof Error && 'code' in error) {
      const businessError = error as any;
      const errorResponse: CancelPaymentErrorResponse = {
        success: false,
        error:
          businessError.code || PaymentErrorCodes.PAYMENT_PROCESSING_FAILED,
        message: businessError.message,
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 토스페이먼츠 API 에러
    if (error instanceof TossPaymentError) {
      const errorResponse: CancelPaymentErrorResponse = {
        success: false,
        error: PaymentErrorCodes.TOSS_CANCEL_ERROR,
        message: error.message,
        details: { code: error.code, status: error.status },
      };
      return NextResponse.json(errorResponse, { status: error.status || 500 });
    }

    // 예상치 못한 에러
    const errorResponse: CancelPaymentErrorResponse = {
      success: false,
      error: PaymentErrorCodes.INTERNAL_ERROR,
      message: '서버 오류가 발생했습니다',
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
