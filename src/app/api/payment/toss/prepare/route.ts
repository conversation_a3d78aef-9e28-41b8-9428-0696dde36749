import { NextRequest, NextResponse } from 'next/server';
import { requireMemberAuth } from '@/lib/auth/member.server';
import { enrollmentService } from '@/lib/services/enrollment.service';
import {
  preparePaymentRequestSchema,
  preparePaymentSuccessResponseSchema,
  PaymentErrorCodes,
  type PreparePaymentSuccessResponse,
  type PreparePaymentErrorResponse
} from '@/lib/validations/payment.validation';

/**
 * 결제 준비 API
 * POST /api/payment/toss/prepare
 * 
 * @description
 * - TossPayments 보안 가이드라인에 따라 결제 전 orderId, amount를 서버에 저장합니다
 * - 결제 과정에서 악의적으로 결제 금액이 바뀌는 것을 방지하기 위함입니다
 * - 인증된 멤버의 수강신청에 대해서만 준비가 가능합니다
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 인증 확인
    const { member, errorResponse } = await requireMemberAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 요청 데이터 검증
    const body = await request.json();
    const validationResult = preparePaymentRequestSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      const errorResponse: PreparePaymentErrorResponse = {
        success: false,
        error: PaymentErrorCodes.VALIDATION_ERROR,
        message: '입력값이 올바르지 않습니다',
        details: validationResult.error.issues,
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { enrollmentId, orderId, amount } = validationResult.data;

    console.log('Processing payment prepare:', {
      memberId: member!.id,
      enrollmentId,
      orderId,
      amount,
      timestamp: new Date().toISOString()
    });

    // 3. 서비스 레이어를 통한 결제 준비 처리
    const prepareId = await enrollmentService.preparePayment({
      enrollmentId,
      orderId,
      amount,
      memberId: member!.id
    });

    console.log('Payment prepared successfully:', {
      memberId: member!.id,
      enrollmentId,
      prepareId,
    });

    // 4. 응답 데이터 검증 및 포맷팅
    const response: PreparePaymentSuccessResponse = preparePaymentSuccessResponseSchema.parse({
      success: true,
      prepareId,
      message: '결제 준비가 완료되었습니다',
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Payment prepare error:', error);

    // 비즈니스 룰 에러 처리
    if (error instanceof Error && 'code' in error) {
      const businessError = error as any;
      const errorResponse: PreparePaymentErrorResponse = {
        success: false,
        error: businessError.code || PaymentErrorCodes.INTERNAL_ERROR,
        message: businessError.message
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 예상치 못한 에러
    const errorResponse: PreparePaymentErrorResponse = {
      success: false,
      error: PaymentErrorCodes.INTERNAL_ERROR,
      message: '서버 오류가 발생했습니다'
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}
