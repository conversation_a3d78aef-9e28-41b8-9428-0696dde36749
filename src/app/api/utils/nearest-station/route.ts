import { NextResponse } from 'next/server';
import { z } from 'zod';
import {
  geocodeAddress,
  findNearestSubwayStationByCoords,
} from '@/lib/actions/geocoding';

const byAddressSchema = z.object({
  address: z.string().min(5),
  radiusMeters: z.number().int().min(10).max(20000).optional(),
});

const byCoordsSchema = z.object({
  latitude: z.number(),
  longitude: z.number(),
  radiusMeters: z.number().int().min(10).max(20000).optional(),
});

const bodySchema = z.union([byAddressSchema, byCoordsSchema]);

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}));
    const parsed = bodySchema.safeParse(json);
    if (!parsed.success) {
      return NextResponse.json({ message: 'Invalid body' }, { status: 400 });
    }

    const data = parsed.data as z.infer<typeof bodySchema>;

    let latitude: number | undefined;
    let longitude: number | undefined;

    if ('address' in data) {
      const coords = await geocodeAddress(data.address);
      if (!coords) {
        return NextResponse.json(
          { message: 'Address not found' },
          { status: 404 }
        );
      }
      latitude = coords.latitude;
      longitude = coords.longitude;
    } else {
      latitude = data.latitude;
      longitude = data.longitude;
    }

    const result = await findNearestSubwayStationByCoords(
      { latitude: latitude!, longitude: longitude! },
      { radiusMeters: 'radiusMeters' in data ? data.radiusMeters : undefined }
    );

    if (!result) {
      return NextResponse.json(
        { message: 'No nearby station found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { station: result.stationName, distance: result.distanceMeters },
      { status: 200 }
    );
  } catch (error) {
    console.error('nearest-station error', error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
