import { NextResponse } from 'next/server';
import { z } from 'zod';
import { geocodeAddress } from '@/lib/actions/geocoding';

const bodySchema = z.object({ address: z.string().min(5) });

export async function POST(req: Request) {
  try {
    const json = await req.json().catch(() => ({}));
    const parsed = bodySchema.safeParse(json);
    if (!parsed.success) {
      return NextResponse.json({ message: 'Invalid body' }, { status: 400 });
    }

    const coords = await geocodeAddress(parsed.data.address);
    if (!coords) {
      return NextResponse.json({ message: 'Not found' }, { status: 404 });
    }

    return NextResponse.json(coords, { status: 200 });
  } catch (error) {
    console.error('geocode error', error);
    return NextResponse.json(
      { message: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
