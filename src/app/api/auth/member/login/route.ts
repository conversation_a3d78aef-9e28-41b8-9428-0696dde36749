import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { members } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import {
  MemberLoginSchema,
  type MemberLoginResponse,
  formatZodErrors,
} from '@/schemas/member';
import { createSupabaseClient } from '@/lib/supabase/server';

/**
 * Member ID/PW 로그인 API 엔드포인트
 *
 * @description
 * 임시 기능: 심사자용 ID/PW 로그인
 * - Supabase Email/Password 인증 사용
 * - 기존 member.server.ts와 호환되는 세션 설정
 * - members 테이블 자동 생성/매칭
 */

export async function POST(
  request: NextRequest
): Promise<NextResponse<MemberLoginResponse>> {
  try {
    // 요청 데이터 파싱
    let requestData;
    try {
      const body = await request.text();
      if (!body || body.trim() === '') {
        return NextResponse.json(
          {
            success: false,
            message: '요청 본문이 비어있습니다.',
          },
          { status: 400 }
        );
      }
      requestData = JSON.parse(body);
    } catch (parseError) {
      console.error('JSON 파싱 오류:', parseError);
      return NextResponse.json(
        {
          success: false,
          message: '잘못된 JSON 형식입니다.',
          error:
            process.env.NODE_ENV === 'development'
              ? String(parseError)
              : undefined,
        },
        { status: 400 }
      );
    }

    // Zod 스키마로 유효성 검사
    const validationResult = MemberLoginSchema.safeParse(requestData);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: '입력 데이터가 올바르지 않습니다.',
          errors: formatZodErrors(validationResult.error),
        },
        { status: 400 }
      );
    }

    const { email, password } = validationResult.data;

    const supabase = await createSupabaseClient();
    // Supabase 이메일/비밀번호 로그인
    const { data: authData, error: authError } =
      await supabase.auth.signInWithPassword({
        email,
        password,
      });

    if (authError || !authData.user) {
      console.error('Supabase Auth 로그인 오류:', authError);

      // 인증 실패 메시지 처리
      if (
        authError?.message?.includes('Invalid login credentials') ||
        authError?.message?.includes('invalid_credentials')
      ) {
        return NextResponse.json(
          {
            success: false,
            message: '이메일 또는 비밀번호가 올바르지 않습니다.',
            error: authError.message,
          },
          { status: 401 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          message: '로그인 중 오류가 발생했습니다.',
          error: authError?.message || 'Authentication failed',
        },
        { status: 500 }
      );
    }

    const user = authData.user;

    // members 테이블에서 기존 레코드 확인 (존재하는 컬럼만)
    let memberRecord = await db
      .select({
        id: members.id,
        email: members.email,
        nickname: members.nickname,
        status: members.status,
      })
      .from(members)
      .where(eq(members.id, user.id))
      .limit(1);

    // members 레코드가 없으면 자동 생성
    if (memberRecord.length === 0) {
      const nickname =
        user.user_metadata?.nickname || user.email?.split('@')[0] || '심사자';

      const newMember = await db
        .insert(members)
        .values({
          id: user.id,
          email: user.email,
          nickname: nickname,
          status: 'ACTIVE',
        })
        .returning({
          id: members.id,
          email: members.email,
          nickname: members.nickname,
          status: members.status,
        });

      memberRecord = newMember;
    }

    const member = memberRecord[0];

    // 성공 응답
    return NextResponse.json(
      {
        success: true,
        message: '로그인이 완료되었습니다.',
        member: {
          id: member.id,
          email: member.email || '',
          nickname: member.nickname || '',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Member 로그인 오류:', error);

    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다.',
        error:
          process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
