import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import {
  MemberRegisterSchema,
  type MemberRegisterResponse,
  formatZodErrors,
} from '@/schemas/member';
import { createSupabaseClient } from '@/lib/supabase/server';

/**
 * Member 회원가입 API 엔드포인트
 *
 * @description
 * 임시 기능: 심사자용 계정 생성
 * - Supabase Email/Password 회원가입 사용
 * - 이메일 인증 없이 바로 활성화
 * - members 테이블 자동 생성
 */

export async function POST(
  request: NextRequest
): Promise<NextResponse<MemberRegisterResponse>> {
  try {
    // 요청 데이터 파싱
    let requestData;
    try {
      const body = await request.text();
      if (!body || body.trim() === '') {
        return NextResponse.json(
          {
            success: false,
            message: '요청 본문이 비어있습니다.',
          },
          { status: 400 }
        );
      }
      requestData = JSON.parse(body);
    } catch (parseError) {
      console.error('JSON 파싱 오류:', parseError);
      return NextResponse.json(
        {
          success: false,
          message: '잘못된 JSON 형식입니다.',
          error:
            process.env.NODE_ENV === 'development'
              ? String(parseError)
              : undefined,
        },
        { status: 400 }
      );
    }

    // Zod 스키마로 유효성 검사
    const validationResult = MemberRegisterSchema.safeParse(requestData);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: '입력 데이터가 올바르지 않습니다.',
          errors: formatZodErrors(validationResult.error),
        },
        { status: 400 }
      );
    }

    const { email, password, nickname } = validationResult.data;

    const supabase = await createSupabaseClient();

    const { data: existingMember } = await supabase
      .from('members')
      .select('id, email')
      .eq('email', email)
      .single();

    if (existingMember) {
      return NextResponse.json(
        {
          success: false,
          message: '이미 등록된 이메일 주소입니다.',
          error: '이미 등록된 이메일입니다',
        },
        { status: 409 }
      );
    }

    // 2. Supabase Auth 회원가입 (이메일 인증 없이)
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: undefined, // 이메일 인증 비활성화
        data: {
          nickname: nickname,
        },
      },
    });

    if (authError) {
      console.error('Supabase Auth 회원가입 오류:', authError);

      // 1. 이메일 중복 관련 에러 처리
      if (
        authError.message.includes('User already registered') ||
        authError.message.includes('already been registered') ||
        authError.message.includes('duplicate') ||
        authError.message.includes('email address is already registered') ||
        authError.code === '23505' || // PostgreSQL unique constraint violation
        authError.status === 422
      ) {
        // Unprocessable Entity
        return NextResponse.json(
          {
            success: false,
            message: '이미 등록된 이메일 주소입니다.',
            error: authError.message,
          },
          { status: 409 }
        );
      }

      // 2. 비밀번호 정책 위반
      if (
        authError.message.includes('Password') ||
        authError.message.includes('password') ||
        authError.code === 'weak_password'
      ) {
        return NextResponse.json(
          {
            success: false,
            message:
              '비밀번호가 정책에 맞지 않습니다. 더 강한 비밀번호를 사용해주세요.',
            error: authError.message,
          },
          { status: 400 }
        );
      }

      // 3. 이메일 형식 오류
      if (authError.message.includes('Invalid email')) {
        return NextResponse.json(
          {
            success: false,
            message: '올바른 이메일 형식을 입력해주세요.',
            error: authError.message,
          },
          { status: 400 }
        );
      }

      // 기타 에러
      return NextResponse.json(
        {
          success: false,
          message: '회원가입 중 오류가 발생했습니다.',
          error: authError.message,
        },
        { status: 500 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        {
          success: false,
          message: '회원가입에 실패했습니다.',
          error: 'No user data returned',
        },
        { status: 500 }
      );
    }

    const user = authData.user;

    try {
      // 3. members 테이블에 레코드 생성 또는 기존 레코드 확인
      let member;

      // 먼저 해당 user.id로 members 레코드가 이미 존재하는지 확인
      const { data: existingUserMember } = await supabase
        .from('members')
        .select('id, email, nickname, status')
        .eq('id', user.id)
        .single();

      if (existingUserMember) {
        // 이미 존재하는 경우 기존 레코드 사용
        member = existingUserMember;
      } else {
        // 새로 생성
        const { data: memberData, error: memberInsertError } = await supabase
          .from('members')
          .insert({
            id: user.id,
            email: user.email,
            nickname: nickname,
            status: 'ACTIVE',
          })
          .select('id, email, nickname, status')
          .single();

        if (memberInsertError) {
          console.error('Members 테이블 insert 오류:', memberInsertError);
          throw memberInsertError;
        }

        member = memberData;
      }

      // 4. 성공 응답
      return NextResponse.json(
        {
          success: true,
          message: '회원가입이 완료되었습니다. 바로 로그인하실 수 있습니다.',
          member: {
            id: member.id,
            email: member.email || '',
            nickname: member.nickname || '',
          },
        },
        { status: 201 }
      );
    } catch (dbError) {
      console.error('Members 테이블 생성 오류:', dbError);

      // DB 오류 시 생성된 Supabase Auth 계정 정리
      try {
        const supabase = await createSupabaseClient();
        if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
          await supabase.auth.admin.deleteUser(user.id);
        }
      } catch (cleanupError) {
        console.error('Auth 계정 정리 실패:', cleanupError);
      }

      return NextResponse.json(
        {
          success: false,
          message: '회원 정보 저장 중 오류가 발생했습니다.',
          error: 'Database error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Member 회원가입 오류:', error);

    return NextResponse.json(
      {
        success: false,
        message: '서버 오류가 발생했습니다.',
        error:
          process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
