import { NextRequest, NextResponse } from 'next/server';
import { requireMemberAuth } from '@/lib/auth/member.server';
import { db } from '@/lib/db';
import { 
  enrollments, 
  class_schedule_groups, 
  class_schedules 
} from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * 수강신청의 스케줄 그룹 및 스케줄 조회 API
 * GET /api/enrollments/[enrollmentId]/schedules
 * 
 * @description
 * - 수강신청 ID로 해당 수강신청의 스케줄 그룹과 스케줄 정보를 조회합니다
 * - 인증된 멤버만 접근 가능하며, 본인의 수강신청만 조회할 수 있습니다
 * 
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 경로 매개변수
 * @param {string} params.enrollmentId - 수강신청 UUID
 * 
 * @returns {Promise<NextResponse>} JSON 응답
 * @returns {Object} 200 - 스케줄 그룹 및 스케줄 정보
 * @returns {Object} 401 - 인증 실패
 * @returns {Object} 403 - 권한 없음 (다른 사용자의 수강신청)
 * @returns {Object} 404 - 수강신청을 찾을 수 없음
 * @returns {Object} 500 - 서버 내부 오류
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ enrollmentId: string }> }
) {
  try {
    // 1. 인증 확인
    const { member, errorResponse } = await requireMemberAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    const { enrollmentId } = await params;

    // 2. 수강신청 조회 및 권한 확인
    const enrollment = await db
      .select()
      .from(enrollments)
      .where(
        and(
          eq(enrollments.id, enrollmentId),
          eq(enrollments.memberId, member!.id)
        )
      )
      .limit(1);

    if (enrollment.length === 0) {
      return NextResponse.json(
        { 
          error: 'ENROLLMENT_NOT_FOUND', 
          message: '수강신청을 찾을 수 없거나 접근 권한이 없습니다' 
        },
        { status: 404 }
      );
    }

    const scheduleGroupId = enrollment[0].scheduleGroupId;

    // 3. 스케줄 그룹 조회
    const scheduleGroup = await db
      .select()
      .from(class_schedule_groups)
      .where(eq(class_schedule_groups.id, scheduleGroupId))
      .limit(1);

    if (scheduleGroup.length === 0) {
      return NextResponse.json(
        { 
          error: 'SCHEDULE_GROUP_NOT_FOUND', 
          message: '스케줄 그룹을 찾을 수 없습니다' 
        },
        { status: 404 }
      );
    }

    // 4. 스케줄 목록 조회
    const schedules = await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.schedule_group_id, scheduleGroupId))
      .orderBy(class_schedules.day_of_week);

    // 5. 응답 데이터 구성
    const response = {
      scheduleGroup: {
        id: scheduleGroup[0].id,
        status: scheduleGroup[0].status,
        startDate: scheduleGroup[0].start_date,
        schedules: schedules.map(schedule => ({
          id: schedule.id,
          dayOfWeek: schedule.day_of_week,
          startTime: schedule.start_time,
          endTime: schedule.end_time
        }))
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Schedule fetch error:', error);
    
    return NextResponse.json(
      { 
        error: 'INTERNAL_SERVER_ERROR', 
        message: '서버 오류가 발생했습니다' 
      },
      { status: 500 }
    );
  }
}