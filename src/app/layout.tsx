import { MobileContainer } from '@/components/layout/MobileContainer';
import QueryProvider from '@/components/providers/QueryProvider';
import getEnv from '@/lib/config/get-env';
import type { Metadata, Viewport } from 'next';
import Script from 'next/script';
import './globals.css';
import { Toaster } from 'sonner';
import { Analytics } from '@vercel/analytics/next';
import AuthStateListener from '@/components/providers/AuthStateListener';
import { GoogleAnalytics } from '@next/third-parties/google';
import { configManager } from '@/lib/config/config-manager';
import UserStoreProvider from '@/components/providers/UserStoreProvider';

const siteUrl = new URL('https://shallwe.team/');
export const metadata: Metadata = {
  metadataBase: siteUrl, // 상대 URL을 절대 URL로 자동 보정
  title: {
    default: '쉘위 - 4060을 위한 그룹 운동 플랫폼',
    template: '%s | 쉘위',
  },
  description:
    '내 집 가까운 곳에서 비슷한 연령, 체력의 파트너와 함께 운동을 시작해보세요.',
  authors: [{ name: 'TeamOverflowing Inc' }],
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    // googleBot: { index: true, follow: true },
  },
  openGraph: {
    type: 'website',
    url: siteUrl,
    siteName: '쉘위',
    locale: 'ko_KR',
    title: '쉘위 - 4060을 위한 그룹 운동 플랫폼',
    description:
      '4060 수준 맞춤 그룹 운동, 집 가까운 센터에서 함께 시작하세요.',
    images: [
      {
        // url: 'https://framerusercontent.com/images/KTQvW22E6j7vlg8nsb5IqG9lI.png',
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: '쉘위 OG 이미지',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '쉘위 - 그룹 운동 플랫폼',
    description: '집 근처에서 함께하는 4060 맞춤 운동',
    images: [
      // 'https://framerusercontent.com/images/KTQvW22E6j7vlg8nsb5IqG9lI.png',
      '/logo.png',
    ],
  },
  verification: {
    // 네이버 소유 확인
    other: {
      'naver-site-verification': 'ec68d2c0d62852c5debbb32693385cc0ee6acc45',
    },
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: '#6114cc',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang='ko'>
      <Script
        src={`https://oapi.map.naver.com/openapi/v3/maps.js?ncpKeyId=${configManager.getValue('naver.clientId')}`}
      />
      <Script
        id='ld-org'
        type='application/ld+json'
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Organization',
            name: '쉘위',
            url: 'https://shallwe.overflowing.team/',
            logo: 'https://framerusercontent.com/images/KTQvW22E6j7vlg8nsb5IqG9lI.png',
            sameAs: ['https://www.instagram.com/shallwe_pt/'],
          }),
        }}
      />
      <body className='antialiased'>
        <UserStoreProvider />
        <GoogleAnalytics gaId={configManager.getValue('gaId')} />
        <AuthStateListener />
        <QueryProvider>
          <MobileContainer>{children}</MobileContainer>
          <Toaster position='top-right' />
        </QueryProvider>
        <Analytics />
      </body>
    </html>
  );
}
