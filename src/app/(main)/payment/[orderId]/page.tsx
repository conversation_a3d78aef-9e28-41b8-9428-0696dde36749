'use client';

import { BottomBar } from '@/components/layout/BottomBar';
import TossPaymentWidget, {
  TossPaymentWidgetHandle,
} from '@/components/payment/TossPaymentWidget';
import { Separator } from '@/components/ui/separator';
import { usePaymentStore } from '@/contexts/payment.store';
import { useParams, useRouter } from 'next/navigation';
import { Suspense, useMemo, useRef, useState } from 'react';
import { useMyRole } from '@/lib/hooks/useMyRole';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const PAYMENT_INFO_NOTES: readonly string[] = [
  '예약금은 고객 사유로 취소할 경우 수업 시작 48시간 전 100%, 24시간 전 50% 환불됩니다.',
  '수업 시작 24시 이내 또는 수업 시작 이후는 예약금 환불 불가합니다',
  '결제 후 수업 시작까지는 평균 1-2주 소요될 수 있으며, 최대 6주 내 수업이 시작되지 않는 경우 예약금은 전액 환불됩니다.',
  '수업은 4주간 진행됩니다.',
];

function PaymentPageContent() {
  const router = useRouter();
  const params = useParams();
  const paymentInfo = usePaymentStore(state => state.info);
  const { data: myRole } = useMyRole();
  const [isPaymentReady, setIsPaymentReady] = useState(false);
  const [isPaymentMethodSelected, setIsPaymentMethodSelected] = useState(true);
  const widgetRef = useRef<TossPaymentWidgetHandle | null>(null);
  const [dialogState, setDialogState] = useState<{
    open: boolean;
    title: string;
    description: string;
  }>({ open: false, title: '', description: '' });

  const selectedScheduleGroup = useMemo(
    () =>
      paymentInfo?.classData.scheduleGroups.find(
        g => g.id === paymentInfo?.selectedScheduleGroupId
      ),
    [paymentInfo]
  );
  const hasMissingPaymentInfo = !paymentInfo;
  const hasMissingSelectedGroup = !!paymentInfo && !selectedScheduleGroup;
  const hasError = hasMissingPaymentInfo || hasMissingSelectedGroup;
  const errorMessage = hasMissingPaymentInfo
    ? '결제 정보가 없습니다. 다시 예약을 진행해주세요.'
    : '선택한 수업 일정 정보를 찾을 수 없습니다.';

  // Derive view model (won't be used when hasError)
  const cls = paymentInfo?.classData.class;
  const scheduleText = useMemo(
    () =>
      selectedScheduleGroup?.schedules
        .map(s => `${s.dayOfWeek} ${s.startTime}~${s.endTime}`)
        .join(', '),
    [selectedScheduleGroup]
  );
  const totalAmount = useMemo(
    () =>
      cls ? cls.pricePerSession * cls.sessionsPerWeek * cls.durationWeeks : 0,
    [cls]
  );
  const depositRate = useMemo(
    () =>
      cls && totalAmount > 0 && paymentInfo
        ? Math.round((paymentInfo.enrollment.depositAmount / totalAmount) * 100)
        : 0,
    [cls, totalAmount, paymentInfo]
  );
  const classData = useMemo(
    () => ({
      title: cls?.title || '',
      coach: cls?.instructor.name || '',
      center: cls?.studio.name || '',
      location: cls?.studio.address || '',
      schedule: scheduleText || '',
      sessions: cls
        ? `${cls.durationWeeks}개월, 주 ${cls.sessionsPerWeek}회`
        : '',
      monthlyFee: totalAmount,
      depositAmount: paymentInfo?.enrollment.depositAmount || 0,
      depositRate,
      promotionDiscount: 0,
      enrollmentId: paymentInfo?.enrollment.id || '',
    }),
    [cls, scheduleText, totalAmount, paymentInfo, depositRate]
  );

  // 실제 로그인한 사용자 ID로 customerKey 설정 (email 생략)
  const customerKey = useMemo(
    () => (myRole?.userId ? `${myRole.userId}` : ''),
    [myRole?.userId]
  );

  // URL params에서 orderId 가져오기 (enrollments API에서 생성된 실제 주문 ID)
  const orderId = params.orderId as string;
  const hasOrderMismatch =
    !!paymentInfo && paymentInfo.paymentWidget.orderId !== orderId;

  const finalAmount = paymentInfo?.paymentWidget.amount || 0;

  const handlePaymentReady = () => {
    setIsPaymentReady(true);
  };

  const handlePaymentMethodSelectionChange = (isSelected: boolean) => {
    setIsPaymentMethodSelected(isSelected);
  };

  const handleRequestPayment = async () => {
    if (hasError) {
      alert(errorMessage);
      return;
    }

    if (!isPaymentReady) {
      setDialogState({
        open: true,
        title: '결제 불가',
        description:
          '결제 위젯이 아직 준비되지 않았습니다. 잠시 후 다시 시도해주세요.',
      });
      return;
    }

    if (!isPaymentMethodSelected) {
      setDialogState({
        open: true,
        title: '결제수단 미선택',
        description: '결제수단을 먼저 선택해주세요.',
      });
      return;
    }

    if (
      !widgetRef.current ||
      typeof widgetRef.current.requestPayment !== 'function'
    ) {
      // 개발자 안내: TossPaymentWidget의 ref 연결 및 forwardRef/handle 노출 확인
      console.error(
        '결제 위젯 참조가 존재하지 않거나 requestPayment가 없습니다.'
      );
      setDialogState({
        open: true,
        title: '결제 불가',
        description:
          '결제 위젯을 초기화하는 중 문제가 발생했습니다. 새로고침 후 다시 시도해주세요.',
      });
      return;
    }

    // 선택: 라우트 파라미터와 store의 orderId 일치 여부 확인 (디버그/보안 보조)
    if (hasOrderMismatch) {
      setDialogState({
        open: true,
        title: '주문 정보 불일치',
        description:
          '현재 페이지의 주문번호와 결제 정보가 일치하지 않습니다. 이전 페이지로 돌아가 다시 시도해주세요.',
      });
      return;
    }

    try {
      await widgetRef.current.requestPayment();
    } catch (err) {
      console.error('결제 요청 중 오류:', err);
      setDialogState({
        open: true,
        title: '결제 실패',
        description:
          '결제 요청에 실패했습니다. 네트워크 상태를 확인 후 다시 시도해주세요.',
      });
    }
  };

  return hasError ? (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='text-center'>
        <p className='mb-4 text-sm text-gray-600'>{errorMessage}</p>
        <button
          className='bg-primary hover:bg-primary/90 rounded-md px-4 py-2 text-white'
          onClick={() => router.back()}
        >
          이전 페이지로 돌아가기
        </button>
      </div>
    </div>
  ) : (
    <div className='min-h-screen bg-white p-4'>
      <div className='flex flex-col gap-4'>
        {/* Main Message */}
        <header className=''>
          <h2 className='mb-2 text-2xl font-bold text-black'>
            쉘위 그룹 수업 확정을 위해 <br />
            <span className='text-primary-dark font-bold'>예약금 결제</span>
            해주세요.
          </h2>
          <p className='text-sm text-gray-500'>
            원활한 그룹 수업 운영을 위한 예약금 결제이며,
            <br /> 수업이 취소될 경우 예약금은 전액 환불되어요.
          </p>
        </header>

        {/* Location Info */}
        {/* <p className='text-center text-gray-600'>
          {classData.center} · {classData.location}
        </p> */}

        {/* Class Information */}
        <section>
          <h3 className='mb-3 text-lg font-semibold text-black'>수업 정보</h3>
          <div className='rounded-md border p-4'>
            <div className='space-y-3'>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업내용</span>
                <span className='max-w-[200px] truncate text-right'>
                  {classData.title}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업코치</span>
                <span>{classData.coach}</span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업센터</span>
                <span>{classData.center}</span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>PT 횟수</span>
                <span>{classData.sessions}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Toss Payment Widget */}
        <section>
          <TossPaymentWidget
            ref={widgetRef}
            customerKey={customerKey}
            amount={paymentInfo.paymentWidget.amount}
            enrollmentId={paymentInfo.enrollment.id}
            orderId={paymentInfo.paymentWidget.orderId}
            orderName={paymentInfo.paymentWidget.orderName}
            successUrl={paymentInfo.paymentWidget.successUrl}
            failUrl={paymentInfo.paymentWidget.failUrl}
            customerName={paymentInfo.paymentWidget.customerName}
            onPaymentReady={handlePaymentReady}
            // onPaymentMethodSelect={handlePaymentMethodSelect}
            isPaymentMethodSelected={isPaymentMethodSelected}
            onPaymentMethodSelectionChange={handlePaymentMethodSelectionChange}
            hidePayButton
            onError={({ title, description }) =>
              setDialogState({ open: true, title, description })
            }
          />
        </section>

        {/* Payment Information */}
        <section>
          <h3 className='mb-3 text-lg font-semibold text-black'>결제 정보</h3>
          <div className='rounded-md border p-4'>
            <div className='flex flex-col gap-3'>
              <div className='flex justify-between font-semibold'>
                <span>최종 결제 금액</span>
                <span className='text-primary'>
                  {finalAmount.toLocaleString()}원
                </span>
              </div>

              <Separator />

              <div className='flex flex-col gap-2 text-xs'>
                <div className='flex justify-between'>
                  <span className='font-bold text-black'>
                    예약금 결제 (수업료 {classData.depositRate}%)
                  </span>
                  <span className='font-bold text-black'>
                    {classData.depositAmount.toLocaleString()}원
                  </span>
                </div>
                <div className='flex justify-between pl-5'>
                  <span className='text-gray-500'>월 수업료</span>
                  <span>{classData.monthlyFee.toLocaleString()}원</span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-bold text-black'>프로모션 적용가</span>
                  <span className='text-primary font-bold'>
                    -{classData.promotionDiscount.toLocaleString()}원
                  </span>
                </div>
                <div className='flex justify-between pl-5 text-xs text-gray-500'>
                  <span>센터 현장 결제</span>
                  <span>-{classData.promotionDiscount.toLocaleString()}원</span>
                </div>
              </div>

              <div className='space-y-1 text-xs text-gray-500'>
                {PAYMENT_INFO_NOTES.map((note, idx) => (
                  <div key={idx} className='flex items-start gap-2'>
                    <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                    <span>{note}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Agreements */}
        <section>
          <div className='flex flex-col gap-1'>
            <label className='text-primary-dark flex items-center justify-between gap-3 text-sm'>
              <span>주문 내용을 확인하였으며 결제에 동의합니다.</span>
              <button className='text-primary underline'>자세히보기</button>
            </label>
            <label className='text-primary-dark flex items-center justify-between gap-3 text-sm'>
              <span>개인정보 제3자 제공 내용을 확인하였으며 동의합니다.</span>
              <button className='text-primary underline'>자세히보기</button>
            </label>
          </div>
        </section>

        {/* Additional Info */}
        <div className='text-xs text-gray-600'>
          쉘위는 수업 운영을 위해 고객님의 정보를 파트너 센터에 제공합니다.
          고객님의 개인정보는 안전하게 처리되며, 자세한 내용은 약관에서 확인하실
          수 있습니다.
        </div>
      </div>
      {(() => {
        const isPayDisabled =
          !isPaymentMethodSelected ||
          hasError ||
          !isPaymentReady ||
          !customerKey ||
          hasOrderMismatch;
        return (
          <BottomBar
            actions={[
              {
                label: `${finalAmount.toLocaleString()}원 결제하기`,
                onClick: handleRequestPayment,
                variant: 'default',
                disabled: isPayDisabled,
              },
            ]}
            className={''}
          />
        );
      })()}

      {/* 공통 Alert Dialog */}
      <AlertDialog
        open={dialogState.open}
        onOpenChange={open => setDialogState(s => ({ ...s, open }))}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{dialogState.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {dialogState.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              onClick={() =>
                setDialogState({ open: false, title: '', description: '' })
              }
            >
              확인
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function PaymentPageFallback() {
  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='text-center'>
        <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent'></div>
        <p className='text-sm text-gray-600'>결제 정보를 불러오는 중...</p>
      </div>
    </div>
  );
}

export default function PaymentPage() {
  return (
    <Suspense fallback={<PaymentPageFallback />}>
      <PaymentPageContent />
    </Suspense>
  );
}
