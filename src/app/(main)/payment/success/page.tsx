import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock } from 'lucide-react';
import PaymentStoreResetter from '../_components/PaymentStoreResetter';
import { DAYS_MAP, DAYS_ORDER } from '@/lib/constants/days';
import { getEnrolledSchedules } from '@/lib/api/enrollments/enrollments.server';
import { paymentApi } from '@/lib/api/payment';

interface PaymentSuccessPageProps {
  searchParams: Promise<{
    paymentKey?: string;
    orderId?: string; // enrollmentId
    amount?: string;
  }>;
}

export default async function PaymentSuccessPage({
  searchParams,
}: PaymentSuccessPageProps) {
  const params = await searchParams;
  const { paymentKey, orderId, amount } = params;

  if (!orderId) {
    redirect('/payment?error=payment_not_found');
  }

  // 서버에서 결제 승인 확정
  if (paymentKey && amount) {
    try {
      await paymentApi.confirmTossPaymentOnServer({
        paymentKey,
        orderId,
        amount: parseInt(amount, 10),
      });
    } catch (error) {
      const msg =
        error instanceof Error
          ? error.message
          : '결제 처리 중 오류가 발생했습니다.';
      redirect(
        '/payment/fail?code=PAYMENT_PROCESSING_ERROR&message=' +
          encodeURIComponent(msg)
      );
    }
  }

  // 스케줄 조회 후 바로 렌더
  const schedulesData = await getEnrolledSchedules(orderId);
  const schedules = schedulesData.scheduleGroup.schedules
    .sort((a, b) => (DAYS_ORDER[a.dayOfWeek] || 999) - (DAYS_ORDER[b.dayOfWeek] || 999));
  const sessionsPerWeek = schedules.length;
  const daysShort = schedules
    .map(s => (DAYS_MAP[s.dayOfWeek] || s.dayOfWeek).slice(0, 1))
    .join('/');

  return (
    <div className='flex min-h-screen flex-col bg-white'>
      <PaymentStoreResetter />
      <div className='flex flex-1 flex-col px-3 pt-10'>
        <div className='space-y-6 text-center'>
          <div className='bg-primary mx-auto flex size-20 items-center justify-center rounded-full'>
            <CheckCircle className='size-14 text-white' />
          </div>
          <div className='space-y-2'>
            <h2 className='text-lg font-bold text-black'>
              수업 예약 결제 완료되었습니다!
            </h2>
            <p className='text-sm text-gray-600'>
              센터에서 수업 확인 후 시작일 안내드릴 예정입니다.
              <br /> 잠시만 기다려주세요.
            </p>
          </div>
          <div className='flex flex-col items-center rounded-md bg-gray-50 p-3'>
            <h3 className='mb-3 text-xl font-bold text-black'>{`주 ${sessionsPerWeek}회 ${daysShort} 예약 완료`}</h3>
            <div className='flex flex-col gap-1'>
              {schedules.map(s => (
                <div
                  key={s.id}
                  className='flex items-center justify-between gap-5'
                >
                  <div className='flex items-center gap-2'>
                    <Clock className='h-4 w-4' />
                    <span className='text-black'>
                      {DAYS_MAP[s.dayOfWeek] || s.dayOfWeek}
                    </span>
                  </div>
                  <span>
                    {s.startTime.slice(0, 5)} ~ {s.endTime.slice(0, 5)}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className='pt-2'>
            <Button size='lg' asChild className='bg-primary w-full'>
              <Link href='/profile'>확인했어요.</Link>
            </Button>
            <p className='mt-4 text-xs text-gray-500'>
              예약 완료 상태는 마이페이지에서 확인할 수 있어요.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
