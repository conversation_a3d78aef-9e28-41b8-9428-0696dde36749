import HomePageContent from '@/components/pages/HomePage';
import SkeletonPage from '@/components/SkeletonPage';
import { getActiveClasses } from '@/lib/api/classes/api';
import { Suspense } from 'react';

export default async function HomePage() {
  const initialData = await getActiveClasses({
    page: 1,
    limit: 20,
  });
  return (
    <Suspense fallback={<SkeletonPage page='class-list' />}>
      <HomePageContent initialData={initialData} />
    </Suspense>
  );
}
