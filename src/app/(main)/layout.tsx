import { InquireDialog } from '@/components/InquireDialog';
import { AppBar } from '@/components/layout/AppBar';
import { GlobalBottomNav } from '@/components/layout/GlobalBottomNav';
import { LogoutDialog } from '@/components/LogoutDialog';
import OnboardingGuard from '@/components/OnboardingGuard';
import { Suspense } from 'react';

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Suspense
        fallback={
          <div className='fixed inset-0 z-[60] bg-white/40 backdrop-blur-sm' />
        }
      >
        <OnboardingGuard>
          <div className='flex min-h-screen flex-col border-x border-gray-100 bg-white'>
            <AppBar />
            <main className='mb-[var(--bottom-nav-height)] flex flex-1 flex-col'>
              {children}
            </main>
            <InquireDialog />
            <LogoutDialog />
            <GlobalBottomNav />
          </div>
        </OnboardingGuard>
      </Suspense>
    </>
  );
}
