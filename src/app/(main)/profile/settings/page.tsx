'use client';

import Link from 'next/link';
import { uiStore, uiStoreActions } from '@/contexts/ui.store';

export default function ProfileSettingsPage() {
  return (
    <div className='space-y-4 p-4'>
      <Link
        href='/profile/settings/notifications'
        className='block text-base text-gray-900'
      >
        알림 설정
      </Link>
      <button
        type='button'
        className='block w-full text-left text-base text-gray-900'
        onClick={() => {
          // 전역 다이얼로그 열기 (메인 레이아웃에 마운트됨)
          uiStoreActions.setOpen('InquireDialog', true);
        }}
      >
        문의/요청하기
      </button>
      <button
        type='button'
        className='block w-full text-left text-base text-gray-900'
        onClick={() => uiStore.setOpen('LogoutDialog', true)}
      >
        로그아웃
      </button>
    </div>
  );
}
