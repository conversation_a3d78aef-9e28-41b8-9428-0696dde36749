'use client';

import { Switch } from '@/components/ui/switch';
import { useState } from 'react';

export default function NotificationSettingsPage() {
  const [classGuideEnabled, setClassGuideEnabled] = useState(true);
  const [marketingEnabled, setMarketingEnabled] = useState(false);

  return (
    <div className='space-y-6 p-4'>
      <section className='space-y-2'>
        <div className='flex items-center justify-between'>
          <h2 className='text-base font-medium text-gray-900'>
            수업 추천 및 안내 알림
          </h2>
          <Switch
            checked={classGuideEnabled}
            onCheckedChange={setClassGuideEnabled}
            aria-label='수업 추천 및 안내 알림'
          />
        </div>
        <p className='text-sm text-gray-500'>
          내게 맞는 수업 추천, 일정 알림, 연장 안내 등을 보내드려요.
        </p>
      </section>

      <section className='space-y-2'>
        <div className='flex items-center justify-between'>
          <h2 className='text-base font-medium text-gray-900'>
            마케팅 정보 수신 알림
          </h2>
          <Switch
            checked={marketingEnabled}
            onCheckedChange={setMarketingEnabled}
            aria-label='마케팅 정보 수신 알림'
          />
        </div>
        <p className='text-sm text-gray-500'>
          프로모션, 할인 등 다양한 혜택 정보를 안내해드려요.
        </p>
      </section>
    </div>
  );
}
