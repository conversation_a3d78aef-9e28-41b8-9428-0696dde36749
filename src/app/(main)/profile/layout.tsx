import KakaoInquiry from '@/components/shared/KakaoInquiry';
import { ReactNode } from 'react';
import PartnerAccessError from '@/components/shared/profile/PartnerAccessError';
import { createSupabaseClient } from '@/lib/supabase/server';

export default async function ProfileLayout({
  children,
}: {
  children: ReactNode;
}) {
  const supabase = await createSupabaseClient();

  // 사용자 인증 확인
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  // 인증되지 않은 경우는 미들웨어에서 처리되므로 여기서는 처리하지 않음
  if (error || !user) {
    return (
      <>
        {children}
        <KakaoInquiry />
      </>
    );
  }

  // 파트너 계정인지 확인
  const { data: partner } = await supabase
    .from('partners')
    .select('id')
    .eq('user_id', user.id)
    .single();

  // 파트너 계정인 경우 에러 다이얼로그 표시
  if (partner) {
    return (
      <>
        <PartnerAccessError />
        <KakaoInquiry />
      </>
    );
  }

  // 일반 회원인 경우 정상적으로 프로필 페이지 표시
  return (
    <>
      {children}
      <KakaoInquiry />
    </>
  );
}
