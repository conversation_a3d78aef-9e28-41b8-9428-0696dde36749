import BookingTabsServer from '@/components/shared/profile/BookingTabs.server';
import UserStatsServer from '@/components/shared/profile/UserStats.server';
import SkeletonPage from '@/components/SkeletonPage';
import { Suspense } from 'react';

export const dynamic = 'force-dynamic';

export default async function ProfilePage() {
  return (
    <div className='space-y-6 p-4'>
      <Suspense fallback={<SkeletonPage page='profile' />}>
        <UserStatsServer />
        <BookingTabsServer />
      </Suspense>
    </div>
  );
}
