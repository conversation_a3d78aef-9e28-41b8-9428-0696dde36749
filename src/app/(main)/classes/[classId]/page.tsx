import { Badge } from '@/components/ui/badge';
import { getClassDetailById } from '@/lib/api/classes/api';
import { mapLevelToLabel, mapSpecialtyToLabel } from '@/lib/utils/format';
import { MapPin } from 'lucide-react';
import ClassDetailTab from './components/ClassDetailTab';
import ClassImageSection from './components/ClassImageSection';
import { ClientBottomBar } from './components/ClientActions';
import TopOverlayActions from './components/TopOverlayActions';
import KakaoInquiry from '@/components/shared/KakaoInquiry';

interface ClassDetailPageProps {
  params: Promise<{ classId: string }>;
}

export default async function ClassDetailPage({
  params,
}: ClassDetailPageProps) {
  const { classId } = await params;
  const data = await getClassDetailById(classId);
  const { studio } = data;

  const classLevelText = mapLevelToLabel(data.level);
  const classCategoryText = mapSpecialtyToLabel(data.category);
  return (
    <>
      <div className='relative'>
        <TopOverlayActions />
        <ClassImageSection images={data.images} title={data.title} />

        <div className='space-y-4 p-4'>
          <div className='flex items-center gap-2'>
            <Badge className='bg-secondary text-secondary-foreground border-none'>
              {classCategoryText}
            </Badge>
            <Badge className='bg-primary text-primary-foreground border-none'>
              {classLevelText}
            </Badge>
          </div>
          <header className='flex flex-col gap-1'>
            <h1 className='text-xl font-bold text-gray-900'>{data.title}</h1>
            <div className='flex items-center gap-1 text-sm text-gray-600'>
              <MapPin className='h-3 w-3' />
              <span>
                {studio.address} {studio.addressDetail}
              </span>
              <span>{studio.nearestStation}</span>
            </div>
          </header>

          <div className='mb-1 flex items-center justify-between border-t pt-4'>
            <div className='flex items-center gap-1'>
              <span className='text-primary'>회당</span>
              <span className='text-primary text-xl font-bold'>
                {data.pricePerSession.toLocaleString()}원
              </span>
            </div>
          </div>
        </div>

        <ClassDetailTab classDetail={data} />
        <ClientBottomBar classId={classId} />
      </div>
      <KakaoInquiry />
    </>
  );
}
