export interface Schedule {
  id: string;
  day_of_week: string;
  start_time: string;
  end_time: string;
  is_active: boolean;
  enrollment_count: number;
  occupancy_rate: number;
}

export interface ScheduleGroup {
  id: string;
  group_name: string;
  group_description: string | null;
  sessions_per_week: number;
  max_participants: number;
  price_per_session: string | null;
  is_active: boolean;
  schedules: Schedule[];
}

export interface CurriculumStep {
  step: number;
  title: string;
  description: string;
}

export interface Curriculum {
  overview: string;
  materials: string[];
  curriculum: CurriculumStep[];
  targetAudience: string[];
}

export interface Studio {
  id: string;
  name: string;
  address: string;
  studio_type: string;
}

export interface Enrollment {
  id: string;
  // Add other enrollment fields as needed
}

export interface Review {
  id: string;
  // Add other review fields as needed
}

export interface ClassStats {
  total_enrollments: number;
  confirmed_enrollments: number;
  pending_enrollments: number;
  cancelled_enrollments: number;
  total_revenue: number;
  total_schedule_groups: number;
  total_schedules: number;
  active_schedules: number;
  review_count: number;
  average_rating: number;
}

export interface ClassDetailResponse {
  id: string;
  title: string;
  description: string;
  curriculum: Curriculum;
  category: string;
  specialty: string;
  level: string;
  duration_minutes: number;
  price_per_session: string;
  max_capacity: number;
  recruitment_start_date: string;
  recruitment_end_date: string;
  class_start_date: string;
  class_end_date: string;
  status: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  studio: Studio;
  schedule_groups: ScheduleGroup[];
  enrollments: Enrollment[];
  reviews: Review[];
  stats: ClassStats;
}