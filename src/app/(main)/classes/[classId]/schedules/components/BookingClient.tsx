'use client';

import { useState } from 'react';
import { BookingCard } from '@/components/shared/booking/BookingCard';
import { BookingPaymentModal } from '@/components/shared/booking/BookingPaymentModal';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BottomBar, bottomBarConfigs } from '@/components/layout/BottomBar';
import { MessageCircle } from 'lucide-react';
import { ClassSchedulesResponse } from '@/app/api/classes/schema';
import { DAYS_MAP } from '@/lib/constants/days';
import { getDayOrderIndex } from '@/lib/utils';
import ScheduleRequestDialog from './ScheduleRequestDialog';

interface BookingClientProps {
  data: ClassSchedulesResponse;
}

export function SchedulesClient({ data }: BookingClientProps) {
  const [selectedSchedule, setSelectedSchedule] = useState<string | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showScheduleRequest, setShowScheduleRequest] = useState(false);

  const schedules = (data.scheduleGroups || []).map(group => {
    const dayAbbreviations: Record<string, string> = {
      월요일: '월',
      화요일: '화',
      수요일: '수',
      목요일: '목',
      금요일: '금',
      토요일: '토',
      일요일: '일',
    };

    const times = group.schedules.map(schedule => ({
      day: DAYS_MAP[schedule.dayOfWeek] || schedule.dayOfWeek,
      time: `${schedule.startTime.slice(0, 5)} ~ ${schedule.endTime.slice(0, 5)}`,
    }));

    const sortedSchedules = [...group.schedules].sort((a, b) => {
      const dayA = getDayOrderIndex(DAYS_MAP[a.dayOfWeek] || a.dayOfWeek);
      const dayB = getDayOrderIndex(DAYS_MAP[b.dayOfWeek] || b.dayOfWeek);
      return dayA - dayB;
    });

    const dayNames = sortedSchedules
      .map(
        s =>
          dayAbbreviations[DAYS_MAP[s.dayOfWeek]] ||
          DAYS_MAP[s.dayOfWeek] ||
          s.dayOfWeek
      )
      .join('/');

    const title = `${dayNames} 수업 (주 ${group.schedules.length}회)`;
    const currentEnrollments = group.currentEnrollments || [];
    const spotsLeft = data.class.maxParticipants - currentEnrollments.length;

    return {
      id: group.id,
      title,
      frequency: `주 ${group.schedules.length}회`,
      times,
      spotsLeft,
      maxParticipants: data.class.maxParticipants,
      currentEnrollments,
    };
  });

  const handleScheduleSelect = (scheduleId: string) => {
    setSelectedSchedule(scheduleId === selectedSchedule ? null : scheduleId);
  };

  const handleBooking = () => {
    if (selectedSchedule) {
      console.log('selectedSchedule', selectedSchedule);
      setShowPaymentModal(true);
    }
  };

  const handleRequestCustomTime = () => {
    setShowScheduleRequest(true);
  };

  const bottomBarActions = selectedSchedule
    ? bottomBarConfigs.booking(handleBooking)
    : [];

  const selectedScheduleData = schedules.find(s => s.id === selectedSchedule);

  return (
    <div className=''>
      <div className='space-y-6 p-4'>
        <div>
          <h1 className='mb-2 text-xl font-bold text-gray-900'>
            예약 가능한 수업 일정
          </h1>
          <p className='text-gray-600'>원하시는 수업 시간을 선택해주세요</p>
        </div>

        <div className='space-y-4'>
          {schedules.map((schedule, index) => (
            <BookingCard
              index={index}
              key={schedule.id}
              onSelect={handleScheduleSelect}
              schedule={{
                ...schedule,
                isSelected: selectedSchedule === schedule.id,
              }}
            />
          ))}
        </div>

        <Card className='border-2 border-dashed border-gray-300'>
          <CardContent className='p-6 text-center'>
            <MessageCircle className='mx-auto mb-3 h-8 w-8 text-gray-400' />
            <h3 className='mb-2 font-medium text-gray-900'>
              희망하는 수업 시간이 없으신가요?
            </h3>
            <p className='mb-4 text-sm text-gray-600'>
              원하시는 시간대를 요청해주시면 코치님께 전달드려요
            </p>
            <Button
              variant='outline'
              onClick={handleRequestCustomTime}
              className='w-full'
            >
              희망 수업 시간 요청하기
            </Button>
          </CardContent>
        </Card>
      </div>

      {selectedSchedule && <BottomBar actions={bottomBarActions} />}

      {selectedScheduleData && selectedSchedule && (
        <BookingPaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          classData={data}
          selectedScheduleGroupId={selectedSchedule}
        />
      )}

      <ScheduleRequestDialog
        open={showScheduleRequest}
        onOpenChange={setShowScheduleRequest}
        classId={data.class.id}
      />
    </div>
  );
}
