import { useMemo, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { scheduleRequestsApi } from '@/lib/api/schedule-requests';
import type { ScheduleRequestInput } from '@/schemas/schedule-request';
import { toast } from 'sonner';

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  classId: string;
}

const MAX_DAYS = 2;
type Days = ScheduleRequestInput['preferred_days'];

// 선택된 요일 배열을 최대 2개로 제한합니다.
// 2개 초과 선택 시 가장 먼저 선택했던 항목을 제거하고 최근 2개만 유지합니다.
function clampToTwoDays(prev: Days, next: Days): Days {
  if (next.length <= MAX_DAYS) return next;

  const added = next.find(d => !prev.includes(d));
  if (added) {
    return [...prev.slice(-1), added] as Days;
  }
  return next.slice(-MAX_DAYS) as Days;
}

const DAYS: {
  key: ScheduleRequestInput['preferred_days'][number];
  label: string;
}[] = [
  { key: 'mon', label: '월' },
  { key: 'tue', label: '화' },
  { key: 'wed', label: '수' },
  { key: 'thu', label: '목' },
  { key: 'fri', label: '금' },
  { key: 'sat', label: '토' },
  { key: 'sun', label: '일' },
];

function buildTimeOptions() {
  const options: string[] = [];
  for (let h = 6; h <= 23; h++) {
    for (const m of [0, 30]) {
      const hh = String(h).padStart(2, '0');
      const mm = String(m).padStart(2, '0');
      options.push(`${hh}:${mm}`);
    }
  }
  return options;
}

export default function ScheduleRequestDialog({
  open,
  onOpenChange,
  classId,
}: Props) {
  const timeOptions = useMemo(() => buildTimeOptions(), []);
  const [selectedDays, setSelectedDays] = useState<Days>([]);
  const [startTime, setStartTime] = useState<string>();
  const [endTime, setEndTime] = useState<string>();

  const reset = () => {
    setSelectedDays([]);
    setStartTime(undefined);
    setEndTime(undefined);
  };

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (payload: ScheduleRequestInput) =>
      scheduleRequestsApi.create(payload),
    onSuccess: res => {
      toast.success(res?.message || '수업 오픈 요청이 접수되었습니다.');
      onOpenChange(false);
      reset();
    },
    onError: (err: unknown) => {
      let message = '요청 처리 중 오류가 발생했습니다.';
      if (err && typeof err === 'object') {
        const e = err as {
          data?: { error?: string; message?: string };
          message?: string;
        };
        message = e?.data?.error || e?.data?.message || e?.message || message;
      }
      toast.error(message);
    },
  });

  const hasTwoDays = selectedDays.length === MAX_DAYS;
  const isValidTimeRange = !!startTime && !!endTime && startTime! < endTime!;
  const canSubmit = hasTwoDays && isValidTimeRange;

  // 단일 요일 버튼 클릭 핸들러 (토글 동작 + 2개 제한 적용)
  const handleClickDay = (day: Days[number]) => {
    setSelectedDays(prev =>
      prev.includes(day)
        ? (prev.filter(d => d !== day) as Days)
        : clampToTwoDays(prev, [...prev, day] as Days)
    );
  };

  const handleSubmit = async () => {
    if (!canSubmit || isPending) return;
    const payload: ScheduleRequestInput = {
      class_id: classId,
      preferred_days: selectedDays,
      preferred_start_time: startTime!,
      preferred_end_time: endTime!,
    };
    await mutateAsync(payload);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={open => {
        if (!isPending) onOpenChange(open);
      }}
    >
      <DialogContent className=''>
        <DialogHeader>
          <DialogTitle className='text-lg font-bold'>
            수업 개설 요청
          </DialogTitle>
          <DialogDescription className='text-gray-500'>
            원하시는 운동 수업 시간대를 등록해주세요.
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          <div className='space-y-2'>
            <div className='text-sm font-medium'>요일 선택 *</div>
            <div className='grid grid-cols-7 gap-2'>
              {DAYS.map(d => {
                const isSelected = selectedDays.includes(d.key);
                return (
                  <Button
                    key={d.key}
                    type='button'
                    size='sm'
                    variant={isSelected ? 'default' : 'outline'}
                    aria-pressed={isSelected}
                    onClick={() => handleClickDay(d.key)}
                    className='px-0'
                  >
                    {d.label}
                  </Button>
                );
              })}
            </div>
          </div>

          <div className='space-y-2'>
            <div className='text-sm font-medium'>시간 선택 *</div>
            <div className='flex items-center gap-2'>
              <Select value={startTime} onValueChange={setStartTime}>
                <SelectTrigger className='flex-1'>
                  <SelectValue placeholder='시작 시간' />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map(t => (
                    <SelectItem key={t} value={t}>
                      {t}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={endTime} onValueChange={setEndTime}>
                <SelectTrigger className='flex-1'>
                  <SelectValue placeholder='종료 시간' />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map(t => (
                    <SelectItem key={t} value={t}>
                      {t}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className='grid grid-cols-2 gap-3 pt-2'>
            <Button
              variant='secondary'
              disabled={isPending}
              onClick={() => onOpenChange(false)}
            >
              닫기
            </Button>
            <Button
              variant='primaryDark'
              onClick={handleSubmit}
              disabled={!canSubmit || isPending}
            >
              {isPending ? '요청 중...' : '수업 오픈 요청하기'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
