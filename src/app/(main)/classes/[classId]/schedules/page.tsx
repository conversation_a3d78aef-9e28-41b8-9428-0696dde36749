import { getClassSchedules } from '@/lib/api/classes/classes.api';
import { SchedulesClient } from './components/BookingClient';

interface BookingPageProps {
  params: Promise<{ classId: string }>;
}

export default async function SchedulesPage({ params }: BookingPageProps) {
  const { classId } = await params;
  const data = await getClassSchedules(classId);
  return <SchedulesClient data={data} />;
}
