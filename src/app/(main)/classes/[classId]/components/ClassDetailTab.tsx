'use client';

import { ClassDetailResponse } from '@/app/api/classes/schema';
import NaverMap from '@/components/shared/NaverMap';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ExternalLink, MapPin, PhoneIcon } from 'lucide-react';
import { JSX, useState } from 'react';
import ImageViewer from '@/components/shared/ImageViewer';
import { FAQ } from './FAQ';

interface ClassDetailTabProps {
  classDetail: ClassDetailResponse;
}

export default function ClassDetailTab({ classDetail }: ClassDetailTabProps) {
  const { instructor, studio } = classDetail;
  const [activeTab, setActiveTab] = useState('class');
  const [isStudioImageViewerOpen, setIsStudioImageViewerOpen] = useState(false);
  const [selectedStudioImageIndex, setSelectedStudioImageIndex] = useState(0);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const studioImages = studio.images?.map(image => image.url) ?? [];
  const studioAmenities = studio.amenities;

  const handleStudioImageClick = (index: number) => {
    setSelectedStudioImageIndex(index);
    setIsStudioImageViewerOpen(true);
  };

  const handleCloseStudioImageViewer = () => {
    setIsStudioImageViewerOpen(false);
  };

  const renderAmenities = () => {
    if (!studioAmenities) return null;

    const amenityItems: JSX.Element[] = [];

    // 기본 시설들 렌더링
    if (studioAmenities.parking?.isAvailable) {
      amenityItems.push(
        <div key='parking' className='flex items-center gap-2'>
          <div className='bg-primary h-1 w-1 rounded-full'></div>
          <span>
            주차장{' '}
            {studioAmenities.parking.isFree
              ? '(무료)'
              : studioAmenities.parking.price
                ? `(${studioAmenities.parking.price.toLocaleString()}원)`
                : ''}
          </span>
        </div>
      );
    }

    if (studioAmenities.shower?.isAvailable) {
      amenityItems.push(
        <div key='shower' className='flex items-center gap-2'>
          <div className='bg-primary h-1 w-1 rounded-full'></div>
          <span>
            샤워실{' '}
            {studioAmenities.shower.isFree
              ? '(무료)'
              : studioAmenities.shower.price
                ? `(${studioAmenities.shower.price.toLocaleString()}원)`
                : ''}
          </span>
        </div>
      );
    }

    if (studioAmenities.locker?.isAvailable) {
      amenityItems.push(
        <div key='locker' className='flex items-center gap-2'>
          <div className='bg-primary h-1 w-1 rounded-full'></div>
          <span>
            개인라커{' '}
            {studioAmenities.locker.isFree
              ? '(무료)'
              : studioAmenities.locker.price
                ? `(${studioAmenities.locker.price.toLocaleString()}원)`
                : ''}
          </span>
        </div>
      );
    }

    if (studioAmenities.workoutClothes?.isAvailable) {
      amenityItems.push(
        <div key='workoutClothes' className='flex items-center gap-2'>
          <div className='bg-primary h-1 w-1 rounded-full'></div>
          <span>
            운동복 대여{' '}
            {studioAmenities.workoutClothes.isFree
              ? '(무료)'
              : studioAmenities.workoutClothes.price
                ? `(${studioAmenities.workoutClothes.price.toLocaleString()}원)`
                : ''}
          </span>
        </div>
      );
    }

    if (studioAmenities.towel?.isAvailable) {
      amenityItems.push(
        <div key='towel' className='flex items-center gap-2'>
          <div className='bg-primary h-1 w-1 rounded-full'></div>
          <span>
            수건 대여{' '}
            {studioAmenities.towel.isFree
              ? '(무료)'
              : studioAmenities.towel.price
                ? `(${studioAmenities.towel.price.toLocaleString()}원)`
                : ''}
          </span>
        </div>
      );
    }

    // 기타 시설들 렌더링
    if (studioAmenities.others && studioAmenities.others.length > 0) {
      studioAmenities.others.forEach((other, index) => {
        amenityItems.push(
          <div key={`other-${index}`} className='flex items-center gap-2'>
            <div className='bg-primary h-1 w-1 rounded-full'></div>
            <span>
              {other.name}{' '}
              {other.price ? `(${other.price.toLocaleString()}원)` : ''}
            </span>
          </div>
        );
      });
    }

    return amenityItems.length > 0 ? (
      amenityItems
    ) : (
      <div className='text-sm text-gray-500'>시설 정보가 없습니다.</div>
    );
  };
  const mapLink = `https://map.naver.com/v5/search/${encodeURIComponent(studio.address)}`;
  const latitude = studio.latitude ? Number.parseFloat(studio.latitude) : null;
  const longitude = studio.longitude
    ? Number.parseFloat(studio.longitude)
    : null;
  const hasCoordinates =
    latitude !== null &&
    Number.isFinite(latitude) &&
    longitude !== null &&
    Number.isFinite(longitude);
  return (
    <>
      <Tabs value={activeTab} onValueChange={handleTabChange} className='px-4'>
        <TabsList className='w-full'>
          <TabsTrigger value='class'>수업 소개</TabsTrigger>
          <TabsTrigger value='faq'>FAQ</TabsTrigger>
        </TabsList>

        <TabsContent value='class' className='mt-6 pb-10'>
          <div className='flex flex-col gap-24'>
            <div className='flex flex-col gap-6'>
              <h3 className='text-lg font-semibold'>수업 소개</h3>
              <p className='leading-relaxed whitespace-pre-line text-gray-700'>
                {classDetail.description}
              </p>
            </div>
            <div className='flex flex-col gap-6'>
              <h3 className='text-lg font-semibold'>수업 추천 대상</h3>
              <ul className='marker:text-primary list-inside list-disc space-y-2 pl-4 text-black'>
                {classDetail.recommendedFor.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
            <div className='flex flex-col gap-6'>
              {/* 코치 소개 섹션 */}
              <div className='flex flex-col gap-6'>
                <h2 className='text-lg font-bold'>코치 소개</h2>
                <div className='flex items-center gap-4'>
                  <Avatar className='h-20 w-20'>
                    <AvatarImage src={instructor.profileImage ?? ''} />
                    <AvatarFallback className='text-lg'>
                      {instructor.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex-1'>
                    <div className='mb-1 flex items-center gap-2'>
                      <h3 className='text-xl font-bold'>{instructor.name}</h3>
                      <span className='text-gray-500'>
                        / {instructor.experienceTotalYears}년차
                      </span>
                    </div>
                    {/* Tags - API에 specialties 배열이 없으므로 경험년수 기반 태그 표시 */}
                  </div>
                </div>

                <p className='leading-relaxed text-gray-700'>
                  {instructor.description}
                </p>
              </div>

              {/* 커리어 / 보유 자격증 섹션 */}
              {instructor.certificates &&
                instructor.certificates.length > 0 && (
                  <div className='flex flex-col gap-6'>
                    <h2 className='mb-4 text-lg font-bold'>
                      커리어 / 보유 자격증
                    </h2>
                    <div className='flex flex-col gap-2 pl-3'>
                      {instructor.certificates.map((certificate, index) => (
                        <div key={index} className='flex items-center gap-3'>
                          <CertIcon />
                          <span className='font-medium text-gray-800'>
                            {certificate.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>

            {/* Studio */}
            <div className='flex flex-col gap-6'>
              {/* Studio Header */}
              <div className='mb-2 flex items-center gap-2'>
                <h2 className='text-xl font-bold'>{studio.name}</h2>
                <div className='flex gap-2'>
                  {studio.links?.website && (
                    <a href={studio.links.website} target='_blank'>
                      <Badge variant='secondary' className='text-xs'>
                        웹 링크
                      </Badge>
                    </a>
                  )}
                  {studio.links?.sns && (
                    <a href={studio.links.sns} target='_blank'>
                      <Badge variant='secondary' className='text-xs'>
                        SNS 링크
                      </Badge>
                    </a>
                  )}
                </div>
              </div>

              {/* Studio Images */}
              <div className='grid grid-cols-3 gap-2'>
                {studioImages.map((image, index) => (
                  <div
                    key={index}
                    className='group relative aspect-square cursor-pointer overflow-hidden rounded-lg bg-gray-200 transition-transform hover:scale-105'
                    onClick={() => handleStudioImageClick(index)}
                  >
                    <img
                      src={image}
                      alt={`${studio.name} 사진 ${index + 1}`}
                      className='h-full w-full object-cover transition-transform group-hover:scale-110'
                    />
                    <div className='absolute inset-0 bg-black/0 transition-colors group-hover:bg-black/10' />
                  </div>
                ))}
              </div>

              {/* Studio Description */}
              <div className='leading-relaxed text-gray-700'>
                {studio.description}
              </div>

              {/* Facility Information */}
              <div className='flex flex-col gap-6'>
                <h3 className='text-lg font-semibold'>시설 정보</h3>
                <div className='grid grid-cols-2 gap-y-3'>
                  {renderAmenities()}
                </div>
              </div>

              {/* Location */}
              <div className='flex flex-col gap-6'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-semibold'>주소</h3>
                  <Button
                    variant='link'
                    size='sm'
                    onClick={() => window.open(mapLink, '_blank')}
                    className='text-xs text-blue-500 hover:text-blue-600 hover:underline'
                  >
                    네이버 지도
                    <ExternalLink className='h-2 w-2' />
                  </Button>
                </div>

                <div className='mb-2 flex flex-col gap-2'>
                  <div className='flex items-center gap-2'>
                    <MapPin className='h-4 w-4' />
                    {studio.address}
                  </div>
                  <div className='flex items-center gap-2'>
                    <PhoneIcon className='h-4 w-4' />
                    {studio.phone}
                  </div>
                </div>
                {/* Map Container */}
                {hasCoordinates ? (
                  <NaverMap
                    lat={latitude as number}
                    lng={longitude as number}
                  />
                ) : (
                  <div className='flex h-48 items-center justify-center rounded-md bg-gray-100 text-sm text-gray-500'>
                    지도 정보를 찾을 수 없습니다
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value='faq' className='mt-6'>
          <FAQ />
        </TabsContent>
      </Tabs>

      {/* Studio Image Viewer Modal */}
      <ImageViewer
        images={studioImages}
        initialIndex={selectedStudioImageIndex}
        isOpen={isStudioImageViewerOpen}
        onClose={handleCloseStudioImageViewer}
        title={`${studio.name} 스튜디오`}
      />
    </>
  );
}
const CertIcon = () => (
  <svg
    width='26'
    height='26'
    viewBox='0 0 26 26'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M9.01461 25.3316L6.78398 21.5722L2.54836 20.6441L2.9618 16.2838L0.0917969 13L2.9618 9.71627L2.54836 5.35596L6.78398 4.42783L9.01461 0.668457L13.0002 2.36064L16.9859 0.668457L19.2165 4.42783L23.4521 5.35596L23.0387 9.71627L25.9087 13L23.0387 16.2838L23.4521 20.6441L19.2165 21.5722L16.9859 25.3316L13.0002 23.6394L9.01461 25.3316ZM9.81274 22.9375L13.0002 21.5866L16.2262 22.9375L18.0002 19.9375L21.4377 19.1491L21.1252 15.625L23.4377 13L21.1252 10.3366L21.4377 6.81252L18.0002 6.06252L16.1877 3.06252L13.0002 4.41346L9.7743 3.06252L8.00024 6.06252L4.56274 6.81252L4.87524 10.3366L2.56273 13L4.87524 15.625L4.56274 19.1875L8.00024 19.9375L9.81274 22.9375ZM11.6877 17.0047L18.3174 10.375L17.0002 9.01939L11.6877 14.3319L9.00024 11.6828L7.68305 13L11.6877 17.0047Z'
      fill='#1C1B1F'
    />
  </svg>
);
