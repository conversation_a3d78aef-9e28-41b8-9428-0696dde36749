'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export default function TopOverlayActions() {
  const router = useRouter();
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const onScroll = () => {
      setIsSticky(window.scrollY > 24);
    };
    onScroll();
    window.addEventListener('scroll', onScroll, { passive: true });
    return () => window.removeEventListener('scroll', onScroll);
  }, []);

  const handleBack = () => {
    router.back();
  };

  const handleShare = async () => {
    try {
      const url = window.location.href;
      const shareData = { title: document.title, url };

      if (navigator.share) {
        await navigator.share(shareData);
      } else if (navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        alert('링크가 복사되었습니다.');
      }
    } catch (error) {
      console.error(error);
      toast.error('공유에 실패했습니다.');
    }
  };

  return (
    <div
      className={cn(
        'fixed inset-x-0 top-0 z-50 mx-auto flex max-w-3xl items-center justify-between px-3 py-2 transition-colors',
        isSticky ? 'bg-white' : 'bg-transparent'
      )}
    >
      <Button
        variant='ghost'
        size='sm'
        onClick={handleBack}
        className={cn('h-9 w-9 p-0', isSticky ? 'text-gray-900' : 'text-white')}
      >
        <ArrowLeft className='h-5 w-5' />
        <span className='sr-only'>뒤로가기</span>
      </Button>
      <Button
        variant='ghost'
        size='sm'
        onClick={handleShare}
        className={cn(
          'h-9 px-3 font-bold',
          isSticky ? 'text-gray-900' : 'text-white'
        )}
      >
        공유하기
      </Button>
    </div>
  );
}
