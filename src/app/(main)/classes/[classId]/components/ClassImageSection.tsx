'use client';

import { useState } from 'react';
import ImageCarousel from '@/components/shared/ImageCarousel';
import ImageViewer from '@/components/shared/ImageViewer';

interface ClassImageSectionProps {
  images: { url: string }[] | null;
  title: string;
}

export default function ClassImageSection({
  images,
  title,
}: ClassImageSectionProps) {
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const imageUrls = images?.map(img => img.url) ?? ['/mock.jpg'];

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index);
    setIsViewerOpen(true);
  };

  const handleCloseViewer = () => {
    setIsViewerOpen(false);
  };

  return (
    <>
      <div className='relative h-[300px] w-full overflow-hidden'>
        <ImageCarousel
          images={imageUrls}
          className='h-full w-full'
          aspectRatio='auto'
          showIndicators={true}
          showControls={true}
          onImageClick={handleImageClick}
        />
      </div>

      <ImageViewer
        images={imageUrls}
        initialIndex={selectedImageIndex}
        isOpen={isViewerOpen}
        onClose={handleCloseViewer}
        title={title}
      />
    </>
  );
}
