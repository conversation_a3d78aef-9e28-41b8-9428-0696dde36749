import { Card, CardContent } from '@/components/ui/card';

interface FAQItem {
  question: string;
  answer: string;
}

const FAQ_ITEMS: ReadonlyArray<FAQItem> = [
  {
    question: '운동 경험이 전혀 없어도 괜찮나요?',
    answer:
      '네, 전혀 문제없습니다. 초급자반의 경우 운동 경험이 없는 분들이 주로 신청하기 때문에 누구나 부담 없이 시작할 수 있습니다. 그리고 쉘위PT는 3~4인 소규모 그룹PT로 개인 상태에 따른 맞춤형 지도가 가능합니다.',
  },
  {
    question: '결제는 언제 진행하나요?',
    answer:
      '수업 예약 후 정원 모집이 완료되면 알림을 보내드릴 예정입니다. 이때 예약금 15%를 먼저 결제해주시면 되며, 잔여 수업료는 센터에 방문하여 결제해주시면 됩니다.',
  },
  {
    question: '수업료 환불이 가능한가요?',
    answer:
      '수업 확정 전까지는 가능하지만, 확정 이후에는 환불 정책에 따라 일부 위약금이 부담됩니다.',
  },
];

export function FAQ() {
  return (
    <div className='space-y-4'>
      {FAQ_ITEMS.map((item, index) => (
        <Card key={index}>
          <CardContent className='p-4'>
            <h4 className='text-primary mb-2 font-medium'>
              Q. {item.question}
            </h4>
            <p className='text-gray-700'>A. {item.answer}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
