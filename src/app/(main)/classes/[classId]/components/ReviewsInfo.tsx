import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, MessageCircle, User } from 'lucide-react';
import { ClassStats, Review } from '../types';

interface ReviewsInfoProps {
  stats: ClassStats;
  reviews: Review[];
}

export function ReviewsInfo({ stats, reviews }: ReviewsInfoProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center gap-2 mb-4'>
        <Star className='h-5 w-5' />
        <h3 className='text-lg font-semibold'>리뷰</h3>
      </div>

      {/* 리뷰 통계 */}
      <Card>
        <CardHeader>
          <CardTitle className='text-base'>리뷰 통계</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 gap-4 text-center'>
            <div>
              <div className='text-2xl font-bold text-primary mb-1'>
                {stats.review_count}
              </div>
              <div className='text-sm text-gray-600'>총 리뷰</div>
            </div>
            <div>
              <div className='flex items-center justify-center gap-1 mb-1'>
                <span className='text-2xl font-bold text-primary'>
                  {stats.average_rating > 0 ? stats.average_rating.toFixed(1) : '—'}
                </span>
                <Star className='h-5 w-5 fill-yellow-400 text-yellow-400' />
              </div>
              <div className='text-sm text-gray-600'>평균 평점</div>
            </div>
          </div>
          
          {stats.average_rating > 0 && (
            <div className='mt-4'>
              <div className='flex items-center justify-center gap-1 mb-2'>
                {renderStars(Math.round(stats.average_rating))}
              </div>
              <div className='text-center text-sm text-gray-600'>
                {stats.average_rating >= 4.5 ? '매우 만족' : 
                 stats.average_rating >= 4.0 ? '만족' :
                 stats.average_rating >= 3.5 ? '보통' :
                 stats.average_rating >= 3.0 ? '아쉬움' : '불만족'}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 리뷰 목록 */}
      {reviews.length === 0 ? (
        <Card>
          <CardContent className='p-4 text-center text-gray-500'>
            <MessageCircle className='h-8 w-8 mx-auto mb-2' />
            <p>아직 작성된 리뷰가 없습니다.</p>
            <p className='text-sm mt-1'>첫 번째 리뷰를 남겨보세요!</p>
          </CardContent>
        </Card>
      ) : (
        <div className='space-y-4'>
          {reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className='p-4'>
                <div className='flex items-start gap-3'>
                  <div className='flex-shrink-0'>
                    <div className='w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center'>
                      <User className='h-5 w-5 text-gray-500' />
                    </div>
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center gap-2 mb-2'>
                      <span className='font-medium'>익명</span>
                      <div className='flex items-center gap-1'>
                        {renderStars(5)} {/* 실제 리뷰 평점으로 대체 */}
                      </div>
                      <span className='text-sm text-gray-500'>
                        {/* 실제 리뷰 날짜로 대체 */}
                        2024년 1월 1일
                      </span>
                    </div>
                    <p className='text-sm text-gray-700'>
                      {/* 실제 리뷰 내용으로 대체 */}
                      리뷰 내용이 여기에 표시됩니다.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}