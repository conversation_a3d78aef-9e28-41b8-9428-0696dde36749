'use client';

import { BottomBar, bottomBarConfigs } from '@/components/layout/BottomBar';
import { useRouter } from 'next/navigation';

interface ClientBottomBarProps {
  classId: string;
}

export function ClientBottomBar({ classId }: ClientBottomBarProps) {
  const router = useRouter();
  const handleViewSchedule = () => {
    router.push(`/classes/${classId}/schedules`);
  };
  const bottomBarActions = bottomBarConfigs.classDetail(handleViewSchedule);
  return <BottomBar actions={bottomBarActions} />;
}
