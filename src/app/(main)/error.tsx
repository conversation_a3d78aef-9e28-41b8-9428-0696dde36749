'use client';

import { useEffect } from 'react';
import ErrorPage from '@/components/ErrorPage';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Page error boundary caught:', error);
  }, [error]);

  return (
    <ErrorPage
      title='문제가 발생했어요'
      message='일시적인 오류입니다. 잠시 후 다시 시도해주세요.'
      actions={[
        {
          kind: 'button',
          label: '다시 시도',
          variant: 'outline',
          onClick: reset,
        },
      ]}
    />
  );
}
