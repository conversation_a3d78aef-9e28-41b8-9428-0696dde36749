'use client';

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, Mail, ArrowRight, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';
import PartnerStatusStepper from '../../components/PartnerStatusStepper';
import { configManager } from '@/lib/config/config-manager';

interface PartnerStatus {
  partnerId: string;
  email: string;
  contactName: string;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'REJECTED';
  emailConfirmed: boolean;
  businessDocumentSubmitted: boolean;
  createdAt: string;
}

export default function PartnerRegisterCompletePage() {
  const [partnerStatus, setPartnerStatus] = useState<PartnerStatus | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  // 파트너 상태 조회 (PENDING 상태 허용)
  const fetchPartnerStatus = async (retryCount = 0) => {
    try {
      const response = await fetch('/api/partner/check-status', {
        credentials: 'include', // 쿠키 포함
      });
      const result = await response.json();

      if (result.success && result.data) {
        setPartnerStatus(result.data);
      } else {
        // API 호출 실패하면 기본 스테퍼 상태로 설정
        console.log('API 호출 실패 - 기본 스테퍼 표시');
        setPartnerStatus({
          partnerId: '',
          email: '',
          contactName: '사용자',
          status: 'PENDING',
          emailConfirmed: false,
          businessDocumentSubmitted: false,
          createdAt: new Date().toISOString(),
        });
      }
      setLoading(false);
    } catch (err) {
      console.error('파트너 상태 조회 오류:', err);
      if (retryCount < 2) {
        console.log(`네트워크 오류, ${retryCount + 1}초 후 재시도...`);
        await new Promise(resolve =>
          setTimeout(resolve, (retryCount + 1) * 1000)
        );
        return fetchPartnerStatus(retryCount + 1);
      } else {
        // 재시도 실패시에도 기본 스테퍼 표시
        console.log('네트워크 오류 - 기본 스테퍼 표시');
        setPartnerStatus({
          partnerId: '',
          email: '',
          contactName: '사용자',
          status: 'PENDING',
          emailConfirmed: false,
          businessDocumentSubmitted: false,
          createdAt: new Date().toISOString(),
        });
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    void fetchPartnerStatus();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  if (loading) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center bg-white p-4'>
        <div className='space-y-4 text-center'>
          <div className='mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-purple-600'></div>
          <p className='text-gray-600'>상태를 확인하고 있습니다...</p>
        </div>
      </div>
    );
  }

  if (!partnerStatus) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center bg-white p-4'>
        <div className='space-y-4 text-center'>
          <p className='text-red-600'>
            데이터를 불러오는 중 오류가 발생했습니다.
          </p>
          <Button
            onClick={() => fetchPartnerStatus()}
            className='bg-purple-600 hover:bg-purple-700'
          >
            다시 시도
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-white p-4'>
      <div className='w-full max-w-2xl space-y-8'>
        {/* 성공 아이콘 */}
        <div className='flex justify-center'>
          <div className='rounded-full bg-green-100 p-4'>
            <CheckCircle className='h-12 w-12 text-green-600' />
          </div>
        </div>

        {/* 메인 콘텐츠 */}
        <div className='space-y-4 text-center'>
          <h1 className='text-2xl font-bold text-gray-900'>
            안녕하세요, {partnerStatus.contactName}님!
          </h1>
          <p className='text-gray-600'>
            파트너 가입이 완료되었습니다. 아래 단계를 완료하면 서비스를 이용하실
            수 있습니다.
          </p>
        </div>

        {/* 단계별 진행 상황 */}
        <div className='rounded-lg bg-white p-6 shadow-sm'>
          <h2 className='mb-6 text-lg font-semibold text-gray-900'>
            가입 진행 상황
          </h2>
          <PartnerStatusStepper
            emailConfirmed={partnerStatus.emailConfirmed}
            businessDocumentSubmitted={partnerStatus.businessDocumentSubmitted}
            status={partnerStatus.status}
          />
        </div>

        {/* 다음 단계 안내 */}
        {!partnerStatus.emailConfirmed && (
          <div className='space-y-4 rounded-lg border border-blue-200 bg-blue-50 p-6'>
            <div className='flex items-center space-x-2'>
              <Mail className='h-5 w-5 text-blue-600' />
              <h3 className='text-lg font-semibold text-blue-900'>
                이메일 인증이 필요합니다
              </h3>
            </div>

            <div className='space-y-4 text-sm text-blue-800'>
              <p>이메일함을 확인하여 인증 링크를 클릭해주세요.</p>
            </div>
          </div>
        )}

        {partnerStatus.emailConfirmed && partnerStatus.status !== 'ACTIVE' && (
          <div className='space-y-4 rounded-lg border border-green-200 bg-green-50 p-6'>
            <div className='flex items-center space-x-2'>
              <CheckCircle className='h-5 w-5 text-green-600' />
              <h3 className='text-lg font-semibold text-green-900'>
                이메일 인증 완료!
              </h3>
            </div>

            <div className='space-y-4 text-sm text-green-800'>
              <p>이제 사업자등록증을 제출해주세요.</p>

              <div className='rounded-md border border-green-200 bg-white p-4'>
                <h4 className='mb-2 font-semibold'>사업자등록증 제출 방법</h4>
                <div className='space-y-2'>
                  <div className='flex items-center space-x-2'>
                    <span className='h-2 w-2 rounded-full bg-green-600'></span>
                    <span>
                      제출처: {configManager.getValue('client.email')}
                    </span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <span className='h-2 w-2 rounded-full bg-green-600'></span>
                    <span>제출 방법: 이메일 첨부 또는 스캔본</span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <span className='h-2 w-2 rounded-full bg-green-600'></span>
                    <span>승인 소요시간: 영업일 기준 1-2일</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {partnerStatus.status === 'ACTIVE' && (
          <div className='space-y-4 rounded-lg border border-green-200 bg-green-50 p-6'>
            <div className='flex items-center space-x-2'>
              <CheckCircle className='h-5 w-5 text-green-600' />
              <h3 className='text-lg font-semibold text-green-900'>
                파트너 승인 완료!
              </h3>
            </div>

            <div className='space-y-4 text-sm text-green-800'>
              <p>
                모든 절차가 완료되었습니다. 이제 파트너 대시보드에서 서비스를
                이용하실 수 있습니다.
              </p>
            </div>
          </div>
        )}

        {partnerStatus.status === 'REJECTED' && (
          <div className='space-y-4 rounded-lg border border-red-200 bg-red-50 p-6'>
            <div className='flex items-center space-x-2'>
              <CheckCircle className='h-5 w-5 text-red-600' />
              <h3 className='text-lg font-semibold text-red-900'>
                파트너 승인 거부
              </h3>
            </div>

            <div className='space-y-4 text-sm text-red-800'>
              <p>
                파트너 승인이 거부되었습니다. 자세한 사항은 고객센터로
                문의해주세요.
              </p>
            </div>
          </div>
        )}

        {/* 액션 버튼 */}
        <div className='space-y-3'>
          {partnerStatus.status === 'ACTIVE' ? (
            <Link href='/partner/dashboard' className='block'>
              <Button className='w-full bg-gradient-to-r from-[#5530FF] to-[#9D43FF] py-3 font-semibold text-white hover:from-[#5530FF]/90 hover:to-[#9D43FF]/90'>
                파트너 대시보드로 이동
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            </Link>
          ) : (
            <Link href='/partner/login' className='block'>
              <Button className='w-full bg-gradient-to-r from-[#5530FF] to-[#9D43FF] py-3 font-semibold text-white hover:from-[#5530FF]/90 hover:to-[#9D43FF]/90'>
                로그인 페이지로 이동
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>
            </Link>
          )}
        </div>

        {/* 문의 안내 */}
        <div className='border-t border-gray-200 pt-6 text-center'>
          <p className='mb-2 text-sm text-gray-500'>
            가입 관련 문의사항이 있으신가요?
          </p>
          <a
            href={`mailto:${configManager.getValue('client.email')}`}
            className='inline-flex items-center space-x-1 text-sm font-medium text-blue-600 transition-colors hover:text-blue-500'
          >
            <Mail className='h-4 w-4' />
            <span>{configManager.getValue('client.email')}</span>
          </a>
        </div>
      </div>
    </div>
  );
}
