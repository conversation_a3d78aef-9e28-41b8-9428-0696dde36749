'use client';

import { configManager } from '@/lib/config/config-manager';
import { cn } from '@/lib/utils';
import { CheckCircle, Clock, Mail, FileText, AlertCircle } from 'lucide-react';

interface PartnerStatusStepperProps {
  emailConfirmed: boolean;
  businessDocumentSubmitted: boolean;
  status: 'PENDING' | 'ACTIVE' | 'SUSPENDED' | 'REJECTED';
  className?: string;
}

interface StepConfig {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  current: boolean;
  error?: boolean;
}

export const PartnerStatusStepper: React.FC<PartnerStatusStepperProps> = ({
  emailConfirmed,
  businessDocumentSubmitted,
  status,
  className,
}) => {
  const steps: StepConfig[] = [
    {
      id: 'signup',
      title: '회원가입 완료',
      description: '파트너 계정이 생성되었습니다',
      icon: <CheckCircle className='h-5 w-5' />,
      completed: true,
      current: false,
    },
    {
      id: 'email',
      title: '이메일 인증',
      description: emailConfirmed
        ? '이메일 인증이 완료되었습니다'
        : '이메일함을 확인하여 인증을 완료해주세요',
      icon: emailConfirmed ? (
        <CheckCircle className='h-5 w-5' />
      ) : (
        <Mail className='h-5 w-5' />
      ),
      completed: emailConfirmed,
      current: !emailConfirmed,
    },
    {
      id: 'business',
      title: '사업자등록증 제출 및 승인',
      description: (() => {
        if (status === 'ACTIVE') {
          return '사업자등록증 검토가 완료되어 승인되었습니다';
        }
        if (status === 'REJECTED') {
          return '사업자등록증 검토 결과 승인이 거부되었습니다';
        }
        if (businessDocumentSubmitted) {
          return '사업자등록증 검토 중입니다 (영업일 1-2일 소요)';
        }
        return emailConfirmed
          ? `사업자등록증을 ${configManager.getValue('client.email')}으로 제출해주세요`
          : '이메일 인증 후 사업자등록증을 제출해주세요';
      })(),
      icon: (() => {
        if (status === 'ACTIVE') {
          return <CheckCircle className='h-5 w-5' />;
        }
        if (status === 'REJECTED') {
          return <AlertCircle className='h-5 w-5' />;
        }
        if (
          businessDocumentSubmitted ||
          (emailConfirmed && status === 'PENDING')
        ) {
          return <Clock className='h-5 w-5' />;
        }
        return <FileText className='h-5 w-5' />;
      })(),
      completed: status === 'ACTIVE',
      current: emailConfirmed && status !== 'ACTIVE',
      error: status === 'REJECTED',
    },
  ];

  const getStepStyles = (step: StepConfig, index: number) => {
    if (step.error) {
      return {
        iconBg: 'bg-red-100',
        iconColor: 'text-red-600',
        titleColor: 'text-red-900',
        descColor: 'text-red-600',
        connector: index < steps.length - 1 ? 'bg-red-200' : '',
      };
    }

    if (step.completed) {
      return {
        iconBg: 'bg-green-100',
        iconColor: 'text-green-600',
        titleColor: 'text-green-900',
        descColor: 'text-green-600',
        connector: index < steps.length - 1 ? 'bg-green-200' : '',
      };
    }

    if (step.current) {
      return {
        iconBg: 'bg-blue-100',
        iconColor: 'text-blue-600',
        titleColor: 'text-blue-900',
        descColor: 'text-blue-600',
        connector: index < steps.length - 1 ? 'bg-gray-200' : '',
      };
    }

    return {
      iconBg: 'bg-gray-100',
      iconColor: 'text-gray-400',
      titleColor: 'text-gray-500',
      descColor: 'text-gray-400',
      connector: index < steps.length - 1 ? 'bg-gray-200' : '',
    };
  };

  return (
    <div className={cn('w-full', className)}>
      <div className='space-y-6'>
        {steps.map((step, index) => {
          const styles = getStepStyles(step, index);

          return (
            <div key={step.id} className='relative'>
              <div className='flex items-start'>
                {/* Icon */}
                <div
                  className={cn(
                    'flex h-10 w-10 items-center justify-center rounded-full',
                    styles.iconBg
                  )}
                >
                  <div className={styles.iconColor}>{step.icon}</div>
                </div>

                {/* Content */}
                <div className='ml-4 flex-1'>
                  <h3
                    className={cn('text-sm font-semibold', styles.titleColor)}
                  >
                    {step.title}
                  </h3>
                  <p className={cn('mt-1 text-sm', styles.descColor)}>
                    {step.description}
                  </p>
                </div>
              </div>

              {/* Connector line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    'absolute top-10 left-5 h-6 w-0.5',
                    styles.connector
                  )}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PartnerStatusStepper;
