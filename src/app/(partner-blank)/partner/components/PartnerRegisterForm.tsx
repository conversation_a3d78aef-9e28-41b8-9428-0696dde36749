import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import { PartnerSignupFormSchema } from '@/schemas/partner';
import { PartnerSignupFormData } from '@/schemas/partner';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { ChevronRight } from 'lucide-react';
import { TermsModal } from '@/components/TermsModal';
import { formatPhoneNumber, normalizePhoneNumber } from '@/lib/utils/phone';

interface PartnerRegisterFormProps {
  className?: string;
  onSubmit: (data: PartnerSignupFormData) => Promise<void>;
  loading?: boolean;
}

interface AgreementOption {
  type: string;
  label: string;
  required: boolean;
  onClick: () => void;
}

export const PartnerRegisterForm: React.FC<PartnerRegisterFormProps> = ({
  className,
  onSubmit,
  loading = false,
}) => {
  const [registerError, setRegisterError] = useState<string | null>(null);
  const [termsModal, setTermsModal] = useState<{
    open: boolean;
    type: 'PARTNER_AGREEMENT' | 'PRIVACY_POLICY' | 'THIRD_PARTY_PROVISION' | 'MARKETING_AGREEMENT' | null;
  }>({ open: false, type: null });

  const form = useForm<PartnerSignupFormData>({
    resolver: zodResolver(PartnerSignupFormSchema),
    defaultValues: {
      email: '',
      password: '',
      passwordConfirm: '',
      terms_agreements: [],
      contactInfo: {
        name: '',
        phone: '',
      },
    },
  });

  const {
    watch,
    setValue,
    formState: { errors },
    handleSubmit: formHandleSubmit,
  } = form;

  const email = watch('email');
  const password = watch('password');
  const passwordConfirm = watch('passwordConfirm');
  const contactName = watch('contactInfo.name');
  const contactPhone = watch('contactInfo.phone');
  const termsAgreements = watch('terms_agreements');

  // 약관 모달 열기 함수
  const openTermsModal = (type: 'PARTNER_AGREEMENT' | 'PRIVACY_POLICY' | 'THIRD_PARTY_PROVISION' | 'MARKETING_AGREEMENT') => {
    setTermsModal({ open: true, type });
  };

  // 약관 동의 함수
  const handleTermsAgree = () => {
    if (termsModal.type) {
      handleAgreementChange(termsModal.type, true);
    }
  };

  // 이용약관 동의 옵션들 정의
  const AGREEMENT_OPTIONS: AgreementOption[] = [
    {
      type: 'AGE_VERIFICATION',
      label: '만 14세 이상 가입',
      required: true,
      onClick: () => {}, // 모달 없음
    },
    {
      type: 'PARTNER_AGREEMENT',
      label: '파트너 이용약관',
      required: true,
      onClick: () => openTermsModal('PARTNER_AGREEMENT'),
    },
    {
      type: 'PRIVACY_POLICY',
      label: '개인정보 수집 및 이용 동의',
      required: true,
      onClick: () => openTermsModal('PRIVACY_POLICY'),
    },
    {
      type: 'THIRD_PARTY_PROVISION',
      label: '제3자 정보제공 동의',
      required: true,
      onClick: () => openTermsModal('THIRD_PARTY_PROVISION'),
    },
    {
      type: 'MARKETING_AGREEMENT',
      label: '파트너 마케팅 정보 수신 동의',
      required: false,
      onClick: () => openTermsModal('MARKETING_AGREEMENT'),
    },
  ];

  // 전체 동의 상태 계산
  const allAgreed = AGREEMENT_OPTIONS.every(option =>
    termsAgreements.some(
      agreement => agreement.type === option.type && agreement.agreed
    )
  );

  // 필수 약관 동의 상태 확인
  const requiredAgreementsCompleted = AGREEMENT_OPTIONS.filter(option => option.required).every(option =>
    termsAgreements.some(
      agreement => agreement.type === option.type && agreement.agreed
    )
  );

  // 개별 동의 상태 변경
  const handleAgreementChange = (type: string, agreed: boolean) => {
    const currentAgreements = termsAgreements || [];
    const existingIndex = currentAgreements.findIndex(
      agreement => agreement.type === type
    );

    if (existingIndex >= 0) {
      const updatedAgreements = [...currentAgreements];
      updatedAgreements[existingIndex] = { type, agreed };
      setValue('terms_agreements', updatedAgreements);
    } else {
      setValue('terms_agreements', [...currentAgreements, { type, agreed }]);
    }
  };

  // 전체 동의 토글
  const handleAllAgreementToggle = () => {
    const newAgreements = AGREEMENT_OPTIONS.map(option => ({
      type: option.type,
      agreed: !allAgreed,
    }));
    setValue('terms_agreements', newAgreements);
  };

  // 개별 동의 상태 확인
  const isAgreed = (type: string) => {
    return termsAgreements.some(
      agreement => agreement.type === type && agreement.agreed
    );
  };

  const handleSubmit = async (data: PartnerSignupFormData) => {
    setRegisterError(null);

    try {
      // 폼 제출 시 휴대폰 번호에서 하이픈 제거
      const submitData = {
        ...data,
        contactInfo: {
          ...data.contactInfo,
          phone: normalizePhoneNumber(data.contactInfo.phone),
        },
      };
      
      await onSubmit(submitData);
    } catch (error) {
      console.error('Register error:', error);
      setRegisterError(
        '회원가입 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'
      );
    }
  };

  return (
    <div className={cn('flex flex-1 flex-col', className)}>
      <form
        onSubmit={formHandleSubmit(handleSubmit, e => {
          console.log(e);
        })}
        className='flex flex-1 flex-col justify-between'
      >
        {/* 입력 필드들 */}
        <div className='flex flex-col gap-4'>
          {/* 이메일 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='email'
              className='text-sm font-medium text-gray-700'
            >
              이메일
            </Label>
            <Input
              id='email'
              type='email'
              value={email}
              onChange={e => setValue('email', e.target.value)}
              placeholder='<EMAIL>'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.email && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.email && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.email.message}
              </p>
            )}
          </div>

          {/* 비밀번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='password'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호
            </Label>
            <Input
              id='password'
              type='password'
              value={password}
              onChange={e => setValue('password', e.target.value)}
              placeholder='비밀번호를 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.password && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.password && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.password.message}
              </p>
            )}
          </div>

          {/* 비밀번호 확인 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='passwordConfirm'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호 확인
            </Label>
            <Input
              id='passwordConfirm'
              type='password'
              value={passwordConfirm}
              onChange={e => setValue('passwordConfirm', e.target.value)}
              placeholder='비밀번호를 다시 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.passwordConfirm && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.passwordConfirm && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.passwordConfirm.message}
              </p>
            )}
          </div>

          {/* 담당자명 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='contactName'
              className='text-sm font-medium text-gray-700'
            >
              담당자명
            </Label>
            <Input
              id='contactName'
              type='text'
              value={contactName}
              onChange={e => setValue('contactInfo.name', e.target.value)}
              placeholder='담당자 성함을 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.contactInfo?.name && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.contactInfo?.name && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.contactInfo.name.message}
              </p>
            )}
          </div>

          {/* 휴대폰 번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='contactPhone'
              className='text-sm font-medium text-gray-700'
            >
              휴대폰 번호
            </Label>
            <div className='flex items-center gap-2'>
              <div className='flex-1'>
                <Input
                  id='contactPhone'
                  type='text'
                  placeholder='01012345678 (숫자만)'
                  value={contactPhone}
                  onChange={e => {
                    const { value } = e.target;
                    const formattedValue = formatPhoneNumber(value);
                    setValue('contactInfo.phone', formattedValue);
                  }}
                  maxLength={13}
                  disabled={loading}
                  required
                  className={cn(
                    'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                    errors.contactInfo?.phone &&
                      'border-red-500 focus:ring-red-500'
                  )}
                />
              </div>
            </div>
            {errors.contactInfo?.phone && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.contactInfo.phone.message}
              </p>
            )}
          </div>

          {/* 이용약관 동의 */}
          <div className='mt-6 flex flex-col gap-5'>
            <div className=''>
              <div className='flex items-center gap-3'>
                <Checkbox
                  checked={allAgreed}
                  onCheckedChange={handleAllAgreementToggle}
                  className='h-5 w-5'
                />
                <span 
                  className='text-sm font-medium text-gray-900 cursor-pointer select-none'
                  onClick={handleAllAgreementToggle}
                >
                  전체 동의
                </span>
              </div>
            </div>

            {/* 개별 약관 동의 */}
            <div className='flex flex-col gap-4 rounded-md border px-4 py-5'>
              {AGREEMENT_OPTIONS.map(option => (
                <div
                  key={option.type}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-3'>
                    <Checkbox
                      checked={isAgreed(option.type)}
                      onCheckedChange={checked =>
                        handleAgreementChange(option.type, !!checked)
                      }
                      className='h-4 w-4'
                    />
                    <span 
                      className='text-sm text-gray-700 cursor-pointer select-none'
                      onClick={() => handleAgreementChange(option.type, !isAgreed(option.type))}
                    >
                      {option.required ? (
                        <span className='text-primary'>(필수) </span>
                      ) : (
                        <span className='text-gray-500'>(선택) </span>
                      )}
                      {option.label}
                    </span>
                  </div>
                  {option.type !== 'AGE_VERIFICATION' && (
                    <button
                      type='button'
                      onClick={option.onClick}
                      className='text-gray-400 transition-colors hover:text-gray-600'
                    >
                      <ChevronRight className='h-4 w-4' />
                    </button>
                  )}
                </div>
              ))}
            </div>

            {/* 약관 동의 에러 메시지 */}
            {errors.terms_agreements && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.terms_agreements.message}
              </p>
            )}
          </div>

          {/* 에러 메시지 */}
          {registerError && (
            <div className='rounded-md bg-red-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-red-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>{registerError}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 회원가입 버튼 */}
        <div className='flex flex-col gap-2'>
          <Button
            type='submit'
            disabled={loading || !requiredAgreementsCompleted}
            className={cn(
              'h-10 w-full rounded-lg font-semibold text-white transition-colors',
              requiredAgreementsCompleted
                ? 'bg-gradient-to-r from-[#5000D0] to-[#8645EF] hover:from-[#5000D0]/80 hover:to-[#8645EF]/80'
                : 'bg-gray-300 cursor-not-allowed'
            )}
          >
            {loading ? (
              <div className='flex items-center justify-center'>
                <svg
                  className='mr-3 -ml-1 h-5 w-5 animate-spin text-white'
                  fill='none'
                  viewBox='0 0 24 24'
                >
                  <circle
                    className='opacity-25'
                    cx='12'
                    cy='12'
                    r='10'
                    stroke='currentColor'
                    strokeWidth='4'
                  />
                  <path
                    className='opacity-75'
                    fill='currentColor'
                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                  />
                </svg>
                회원가입 중...
              </div>
            ) : (
              '파트너 가입하기'
            )}
          </Button>

          {/* 로그인 링크 */}
          <div className='text-center'>
            <span className='text-sm text-gray-600'>
              이미 계정이 있으신가요?{' '}
              <Link
                href='/partner/login'
                className='text-primary hover:text-primary/50 font-medium transition-colors'
              >
                로그인하기
              </Link>
            </span>
          </div>
        </div>
      </form>
      
      {/* 약관 모달 */}
      {termsModal.type && (
        <TermsModal
          open={termsModal.open}
          onOpenChange={(open) => setTermsModal(prev => ({ ...prev, open }))}
          type={termsModal.type}
          onAgree={handleTermsAgree}
        />
      )}
    </div>
  );
};

export default PartnerRegisterForm;
