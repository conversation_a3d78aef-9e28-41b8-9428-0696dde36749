'use client';

import { useActionState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { loginPartnerAction } from '@/lib/api/partner/auth.actions';
import type { LoginActionState } from '@/lib/api/partner/auth.actions';
import Link from 'next/link';
import StatusMessage from './StatusMessage';

interface PartnerLoginFormServerProps {
  className?: string;
}

const PartnerLoginForm: React.FC<PartnerLoginFormServerProps> = ({
  className,
}) => {
  const router = useRouter();
  const [state, formAction, isPending] = useActionState<
    LoginActionState,
    FormData
  >(loginPartnerAction, { success: false });

  // 리다이렉션 처리
  useEffect(() => {
    if (state.success && state.redirectTo) {
      router.replace(state.redirectTo);
    }
  }, [state.success, state.redirectTo, router]);

  // 파트너 상태 메시지 표시
  if (state.success && state.partnerStatus) {
    return (
      <div className={cn('mx-auto w-full max-w-3xl', className)}>
        <StatusMessage status={state.partnerStatus} />
        <form action={formAction} className='mt-4'>
          <Button type='submit' variant='outline' className='w-full'>
            다시 로그인
          </Button>
        </form>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-1 flex-col', className)}>
      <form action={formAction} className='flex flex-1 flex-col gap-6'>
        {/* 입력 필드들 */}
        <div className='flex flex-col gap-4'>
          {/* 이메일 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='email'
              className='text-sm font-medium text-gray-700'
            >
              이메일
            </Label>
            <Input
              id='email'
              name='email'
              type='email'
              placeholder='<EMAIL>'
              disabled={isPending}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                state.errors?.email && 'border-red-500 focus:ring-red-500'
              )}
            />
            {state.errors?.email && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {state.errors.email[0]}
              </p>
            )}
          </div>

          {/* 비밀번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='password'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호
            </Label>
            <Input
              id='password'
              name='password'
              type='password'
              placeholder='비밀번호를 입력하세요'
              disabled={isPending}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                state.errors?.password && 'border-red-500 focus:ring-red-500'
              )}
            />
            {state.errors?.password && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {state.errors.password[0]}
              </p>
            )}
          </div>

          {/* 기억하기 체크박스 */}
          {/* <div className='flex items-center space-x-2'>
            <input
              id='rememberMe'
              name='rememberMe'
              type='checkbox'
              value='true'
              disabled={isPending}
              className='h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500'
            />
            <Label
              htmlFor='rememberMe'
              className='text-sm font-medium text-gray-700'
            >
              로그인 상태 유지
            </Label>
          </div> */}

          {/* 일반 에러 메시지 */}
          {state.errors?.general && (
            <div className='rounded-md bg-red-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-red-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>
                    {state.errors.general[0]}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 성공 메시지 (상태가 ACTIVE가 아닌 경우) */}
          {state.success && !state.partnerStatus && state.message && (
            <div className='rounded-md bg-green-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-green-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M5 13l4 4L19 7'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-green-800'>{state.message}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 로그인 버튼 */}
        <div className='flex flex-col gap-2'>
          <Button
            type='submit'
            disabled={isPending}
            className='gradient-bg h-10 w-full rounded-md font-semibold text-white transition-colors'
          >
            {isPending ? (
              <div className='flex items-center justify-center'>
                <svg
                  className='mr-3 -ml-1 h-5 w-5 animate-spin text-white'
                  fill='none'
                  viewBox='0 0 24 24'
                >
                  <circle
                    className='opacity-25'
                    cx='12'
                    cy='12'
                    r='10'
                    stroke='currentColor'
                    strokeWidth='4'
                  />
                  <path
                    className='opacity-75'
                    fill='currentColor'
                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                  />
                </svg>
                로그인 중...
              </div>
            ) : (
              '로그인'
            )}
          </Button>

          {/* 회원가입 링크 */}
          <div className='text-center'>
            <span className='text-sm text-gray-600'>
              아직 계정이 없으신가요?{' '}
              <Link
                href='/partner/register'
                className='text-primary hover:text-primary/50 font-medium transition-colors'
              >
                파트너 가입하기
              </Link>
            </span>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PartnerLoginForm;
