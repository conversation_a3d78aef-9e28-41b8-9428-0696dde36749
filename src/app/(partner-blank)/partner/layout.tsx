// import type { Metadata } from 'next';

// export const metadata: Metadata = {
//   title: '파트너 로그인 | Shallwe',
//   description:
//     'Shallwe 파트너 로그인 페이지입니다. 파트너 계정으로 로그인하여 스튜디오를 관리하세요.',
//   keywords: ['파트너', '로그인', 'Shallwe', '스튜디오'],
//   openGraph: {
//     title: '파트너 로그인 | Shallwe',
//     description: 'Shallwe 파트너 로그인 페이지입니다.',
//     type: 'website',
//   },
// };

export default function PartnerBlankLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='flex min-h-screen flex-col bg-white'>
      <main className='flex flex-1 items-center justify-center px-5'>{children}</main>
    </div>
  );
}
