'use client';

import { useEffect } from 'react';
import ErrorPage from '@/components/ErrorPage';

export default function RootError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Root error boundary caught:', error);
  }, [error]);

  return (
    <ErrorPage
      title='오류가 발생했어요'
      message='잠시 후 다시 시도해주세요.'
      theme='user'
      className='min-h-[60vh]'
      actions={[
        {
          kind: 'button',
          label: '다시 시도',
          variant: 'outline',
          onClick: reset,
        },
        {
          kind: 'link',
          label: '홈으로 이동',
          href: '/',
        },
      ]}
    />
  );
}
