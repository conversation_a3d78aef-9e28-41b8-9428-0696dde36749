'use client';

import './globals.css';
import { useEffect } from 'react';
import ErrorPage from '@/components/ErrorPage';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Global error boundary caught:', error);
  }, [error]);

  const message = `일시적인 오류가 발생했어요. 잠시 후 다시 시도해주세요.${
    error?.digest ? ` #${error.digest}` : ''
  }`;

  return (
    <html lang='ko'>
      <body>
        <div className='flex min-h-screen items-center justify-center bg-gray-50'>
          <ErrorPage
            title='문제가 발생했어요'
            message={message}
            theme='user'
            actions={[
              {
                kind: 'button',
                label: '다시 시도',
                variant: 'outline',
                onClick: reset,
              },
              {
                kind: 'link',
                label: '홈으로 이동',
                href: '/',
              },
            ]}
          />
        </div>
      </body>
    </html>
  );
}
