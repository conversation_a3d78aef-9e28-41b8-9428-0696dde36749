'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function BlankLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const handleBackClick = () => {
    router.back();
  };

  return (
    <div className='flex min-h-screen flex-col'>
      {/* 백버튼 헤더 */}
      <header className='sticky top-0 z-50 flex h-14 w-full items-center bg-white px-4'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleBackClick}
          className='h-8 w-8 p-2'
        >
          <ArrowLeft className='h-4 w-4' />
          <span className='sr-only'>뒤로가기</span>
        </Button>
      </header>

      {/* 메인 컨텐츠 */}
      <main className='flex flex-1 items-start justify-center'>{children}</main>
    </div>
  );
}
