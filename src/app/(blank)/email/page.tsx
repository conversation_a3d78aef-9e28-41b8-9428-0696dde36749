'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useState } from 'react';

export default function EmailLoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleEmailLogin = () => {
    // TODO: 이메일 로그인 구현
    console.log('이메일 로그인 시도:', {
      email: email,
      passwordLength: password.length,
    });
  };

  const handleFindEmail = () => {
    // TODO: 이메일 찾기 구현
    console.log('이메일 찾기');
  };

  const handleFindPassword = () => {
    // TODO: 비밀번호 찾기 구현
    console.log('비밀번호 찾기');
  };

  return (
    <div className='w-full max-w-3xl space-y-8 px-4'>
      {/* 로고 영역 */}
      <div className='text-left'>
        <h1 className='logo mb-4 text-4xl font-bold'>SHALLWE</h1>
        <h2 className='mb-8 text-2xl font-bold text-gray-900'>이메일 로그인</h2>
      </div>

      {/* 로그인 폼 */}
      <div className='space-y-6'>
        {/* 이메일 입력 */}
        <div className='space-y-2'>
          <Label htmlFor='email' className='text-base font-medium'>
            이메일
          </Label>
          <Input
            id='email'
            type='email'
            placeholder='<EMAIL>'
            value={email}
            onChange={e => setEmail(e.target.value)}
            className='h-12 rounded-lg border-gray-300 text-base placeholder:text-gray-400'
          />
        </div>

        {/* 비밀번호 입력 */}
        <div className='space-y-2'>
          <Label htmlFor='password' className='text-base font-medium'>
            비밀번호
          </Label>
          <Input
            id='password'
            type='password'
            placeholder='비밀번호 입력'
            value={password}
            onChange={e => setPassword(e.target.value)}
            className='h-12 rounded-lg border-gray-300 text-base placeholder:text-gray-400'
          />
        </div>

        {/* 찾기 링크 */}
        <div className='flex justify-center py-4'>
          <div className='flex items-center gap-2 text-sm text-gray-500'>
            <button
              onClick={handleFindEmail}
              className='underline-offset-4 hover:text-gray-700 hover:underline'
            >
              이메일 찾기
            </button>
            <span className='text-gray-300'>|</span>
            <button
              onClick={handleFindPassword}
              className='underline-offset-4 hover:text-gray-700 hover:underline'
            >
              비밀번호 찾기
            </button>
          </div>
        </div>

        {/* 로그인 버튼 */}
        <Button
          onClick={handleEmailLogin}
          className='w-full font-semibold'
          size='lg'
        >
          로그인
        </Button>
      </div>
    </div>
  );
}
