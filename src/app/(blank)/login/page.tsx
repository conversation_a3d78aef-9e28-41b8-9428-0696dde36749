'use client';

import { Kaka<PERSON>Button } from '@/components/auth/KakaoButton';
import { MemberLoginForm } from '@/components/auth/MemberLoginForm';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className='w-full max-w-full px-4 pt-4'>
          <h1 className='logo mb-8 text-4xl font-bold'>SHALLWE</h1>
          <div className='h-10 w-full animate-pulse rounded-md bg-gray-100' />
        </div>
      }
    >
      <LoginPageContent />
    </Suspense>
  );
}

function LoginPageContent() {
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirectTo') || '/';

  return (
    <div className='w-full max-w-full space-y-8 px-4 pt-4'>
      <div className='text-left'>
        <h1 className='logo mb-8 text-4xl font-bold'>SHALLWE</h1>
        <div className='space-y-1'>
          <p className='text-2xl font-bold text-gray-900'>우리동네</p>
          <p className='text-2xl font-bold text-gray-900'>프리미엄 그룹 수업</p>
          <p className='text-2xl font-bold text-gray-900'>쉘위 입니다.</p>
        </div>
      </div>

      <div className='space-y-6'>
        {/* 카카오 로그인 (기본) */}
        <div className='space-y-3'>
          <KakaoButton redirectTo={redirectTo} />
        </div>

        {/* 개발자/심사자 로그인 (임시 기능) */}
        <div className='space-y-4'>
          <div className='relative'>
            <div className='absolute inset-0 flex items-center'>
              <span className='w-full border-t border-gray-300' />
            </div>
            <div className='relative flex justify-center text-xs uppercase'>
              <span className='bg-gray-50 px-2 text-gray-500'>또는</span>
            </div>
          </div>

          <div className='space-y-3 rounded-lg border bg-white p-4'>
            <div className='text-center'>
              <h3 className='text-sm font-medium text-gray-900'>
                개발자/심사자 로그인
              </h3>
              <p className='mt-1 text-xs text-gray-600'>
                임시 계정으로 로그인하세요
              </p>
            </div>

            <MemberLoginForm redirectTo={redirectTo} />
          </div>
        </div>
      </div>
    </div>
  );
}
