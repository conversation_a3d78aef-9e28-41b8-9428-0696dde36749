import type { Metadata } from 'next';
import { MemberRegisterForm } from '@/components/auth/MemberRegisterForm';
import Link from 'next/link';

export const metadata: Metadata = {
  title: '임시 계정 생성 | Shallwe',
  description: '개발자/심사자 전용 임시 회원가입 페이지입니다.',
};

export default function RegisterPage() {
  return (
    <div className='flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4'>
      <div className='w-full max-w-3xl space-y-8'>
        {/* 로고 */}
        <div className='text-center'>
          <h1 className='logo mb-2 text-4xl font-bold text-gray-900'>
            SHALLWE
          </h1>
        </div>

        {/* 회원가입 폼 */}
        <div className='rounded-lg bg-white p-6 shadow-md'>
          <MemberRegisterForm />
        </div>

        {/* 푸터 */}
        <div className='space-y-2 text-center'>
          <p className='text-xs text-gray-500'>
            일반 사용자는{' '}
            <Link
              href='/login'
              className='text-blue-600 underline hover:text-blue-500'
            >
              카카오 로그인
            </Link>
            을 이용해주세요
          </p>

          <div className='flex justify-center space-x-4 text-xs text-gray-400'>
            <Link href='/' className='transition-colors hover:text-gray-600'>
              메인 페이지
            </Link>
            <span>•</span>
            <Link
              href='/login'
              className='transition-colors hover:text-gray-600'
            >
              로그인
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
