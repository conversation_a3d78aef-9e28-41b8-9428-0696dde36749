'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function OnboardingCompletePage() {
  const router = useRouter();

  useEffect(() => {
    // 3초 후 메인 페이지로 자동 리다이렉트
    const timer = setTimeout(() => {
      router.replace('/');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleGoToMain = () => {
    router.replace('/');
  };

  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='mx-auto w-full max-w-3xl px-4'>
        <div className='rounded-xl bg-white p-8 text-center shadow-sm'>
          {/* 성공 아이콘 */}
          <div className='mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100'>
            <svg
              className='h-8 w-8 text-green-600'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M5 13l4 4L19 7'
              />
            </svg>
          </div>

          {/* 완료 메시지 */}
          <div className='mb-8'>
            <h1 className='mb-3 text-2xl font-bold text-gray-900'>
              온보딩 완료! 🎉
            </h1>
            <p className='leading-relaxed text-gray-600'>
              회원님의 선호도를 성공적으로 저장했어요.
              <br />
              이제 맞춤형 클래스를 추천받으실 수 있습니다.
            </p>
          </div>

          {/* 다음 단계 안내 */}
          <div className='mb-6 rounded-xl bg-indigo-50 p-4'>
            <h3 className='mb-2 text-sm font-medium text-indigo-800'>
              ✨ 다음은 무엇을 할까요?
            </h3>
            <ul className='space-y-1 text-left text-xs text-indigo-700'>
              <li>• 맞춤 클래스 추천을 확인해보세요</li>
              <li>• 관심있는 클래스에 참여 신청해보세요</li>
              <li>• 프로필에서 선호도를 언제든지 수정할 수 있어요</li>
            </ul>
          </div>

          {/* 액션 버튼 */}
          <div className='space-y-3'>
            <button
              onClick={handleGoToMain}
              className='w-full rounded-xl bg-indigo-600 px-6 py-4 text-base font-medium text-white transition-colors hover:bg-indigo-700'
            >
              클래스 둘러보기
            </button>

            {/* 자동 리다이렉트 안내 */}
            <p className='text-xs text-gray-500'>
              3초 후 자동으로 메인 페이지로 이동합니다
            </p>
          </div>
        </div>

        {/* 추가 혜택 안내 */}
        <div className='mt-6 rounded-xl bg-white p-6 shadow-sm'>
          <h3 className='mb-4 text-center text-lg font-semibold text-gray-900'>
            🎁 신규 회원 혜택
          </h3>
          <div className='space-y-3'>
            <div className='flex items-start space-x-3'>
              <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-blue-100'>
                <span className='text-xs font-bold text-blue-600'>1</span>
              </div>
              <div>
                <p className='text-sm font-medium text-gray-900'>
                  첫 클래스 할인
                </p>
                <p className='text-xs text-gray-600'>
                  첫 번째 참여하는 클래스는 30% 할인혜택
                </p>
              </div>
            </div>

            <div className='flex items-start space-x-3'>
              <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100'>
                <span className='text-xs font-bold text-green-600'>2</span>
              </div>
              <div>
                <p className='text-sm font-medium text-gray-900'>무료 상담</p>
                <p className='text-xs text-gray-600'>
                  강사와 1:1 운동 상담 서비스 무료 제공
                </p>
              </div>
            </div>

            <div className='flex items-start space-x-3'>
              <div className='mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-purple-100'>
                <span className='text-xs font-bold text-purple-600'>3</span>
              </div>
              <div>
                <p className='text-sm font-medium text-gray-900'>운동 플래너</p>
                <p className='text-xs text-gray-600'>
                  개인 맞춤 운동 계획표 무료 제공
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
