import { FITNESS_LEVEL_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingLevelStepProps {
  selectedLevel: string;
  onLevelSelect: (level: string) => void;
}

export default function OnboardingLevelStep({
  selectedLevel,
  onLevelSelect,
}: OnboardingLevelStepProps) {
  return (
    <div className='space-y-6'>
      {/* 헤더 */}
      <div className='text-center'>
        <h2 className='mb-2 text-xl font-semibold text-gray-900'>
          현재 운동 수준은 어느 정도인가요?
        </h2>
        <p className='text-sm text-gray-600'>
          레벨에 맞는 클래스를 추천해드려요
        </p>
      </div>

      {/* 수준 선택 */}
      <div className='space-y-3'>
        {FITNESS_LEVEL_OPTIONS.map(option => {
          const isSelected = selectedLevel === option.value;

          return (
            <button
              key={option.value}
              type='button'
              onClick={() => onLevelSelect(option.value)}
              className={`w-full rounded-xl border p-5 text-left transition-all duration-200 ${
                isSelected
                  ? 'border-primary bg-primary/10 shadow-sm'
                  : 'border-gray-200 bg-white hover:bg-gray-50'
              } `}
            >
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-4'>
                  {/* 아이콘 */}
                  <div className='text-2xl'>{option.icon}</div>

                  {/* 제목과 설명 */}
                  <div>
                    <p
                      className={`text-md font-medium ${
                        isSelected ? 'text-primary' : 'text-gray-900'
                      }`}
                    >
                      {option.label}
                    </p>
                  </div>
                </div>

                {/* 선택 인디케이터 */}
                <div
                  className={`flex h-6 w-6 items-center justify-center rounded-full border-2 ${
                    isSelected ? 'border-primary bg-primary' : 'border-gray-300'
                  } `}
                >
                  {isSelected && (
                    <svg
                      className='h-4 w-4 text-white'
                      fill='currentColor'
                      viewBox='0 0 20 20'
                    >
                      <path
                        fillRule='evenodd'
                        d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                        clipRule='evenodd'
                      />
                    </svg>
                  )}
                </div>
              </div>
            </button>
          );
        })}
      </div>

      {/* 수준별 설명 */}
      <div className='grid grid-cols-1 gap-4'>
        {/* 초급자 가이드 */}
        <div className='rounded-xl bg-green-50 p-4'>
          <div className='flex items-start space-x-3'>
            <div className='text-lg'>🌱</div>
            <div>
              <p className='mb-1 text-sm font-medium text-green-800'>
                초급자라면?
              </p>
              <ul className='space-y-1 text-xs text-green-700'>
                <li>• 기초 동작부터 차근차근 배울 수 있어요</li>
                <li>• 친절한 강사님이 개별 지도해드려요</li>
                <li>• 부담 없는 강도로 시작할 수 있습니다</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 중급자 가이드 */}
        <div className='rounded-xl bg-blue-50 p-4'>
          <div className='flex items-start space-x-3'>
            <div className='text-lg'>🌿</div>
            <div>
              <p className='mb-1 text-sm font-medium text-blue-800'>
                중급자라면?
              </p>
              <ul className='space-y-1 text-xs text-blue-700'>
                <li>• 기존 실력을 바탕으로 한 단계 업그레이드</li>
                <li>• 다양한 응용 동작을 배울 수 있어요</li>
                <li>• 본격적인 운동 효과를 느낄 수 있습니다</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 고급자 가이드 */}
        <div className='rounded-xl bg-purple-50 p-4'>
          <div className='flex items-start space-x-3'>
            <div className='text-lg'>🌳</div>
            <div>
              <p className='mb-1 text-sm font-medium text-purple-800'>
                고급자라면?
              </p>
              <ul className='space-y-1 text-xs text-purple-700'>
                <li>• 전문적이고 심화된 프로그램 참여</li>
                <li>• 고강도 운동으로 한계를 돌파해보세요</li>
                <li>• 다른 회원들과 경험을 나눌 수 있어요</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* 안내 메시지 */}
      <div className='rounded-xl bg-gray-50 p-4'>
        <div className='flex items-start space-x-3'>
          <div className='mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gray-400'>
            <svg
              className='h-3 w-3 text-white'
              fill='currentColor'
              viewBox='0 0 20 20'
            >
              <path
                fillRule='evenodd'
                d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div>
            <p className='mb-1 text-sm font-medium text-gray-800'>
              💡 실력 향상 팁
            </p>
            <ul className='space-y-1 text-xs text-gray-600'>
              <li>• 운동하면서 실력이 늘어도 걱정 마세요</li>
              <li>• 언제든지 더 높은 레벨의 클래스로 참여할 수 있어요</li>
              <li>• 강사님과 상담을 통해 개인 맞춤 레벨을 찾아보세요</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
