import { DAYS_OPTIONS, TIME_SLOTS_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingScheduleStepProps {
  selectedDays: string[];
  selectedTimeSlots: string[];
  onDayToggle: (day: string) => void;
  onTimeSlotToggle: (timeSlot: string) => void;
}

export default function OnboardingScheduleStep({
  selectedDays,
  selectedTimeSlots,
  onDayToggle,
  onTimeSlotToggle,
}: OnboardingScheduleStepProps) {
  return (
    <div className='space-y-8'>
      {/* 헤더 */}
      <div className='text-center'>
        <h2 className='mb-2 text-xl font-semibold text-gray-900'>
          언제 운동하시는 게 좋을까요?
        </h2>
        <p className='text-sm text-gray-600'>요일과 시간대를 선택해주세요</p>
      </div>

      {/* 요일 선택 */}
      <div>
        <h3 className='text-md mb-4 font-medium text-gray-900'>
          선호하는 요일 <span className='text-red-500'>*</span>
        </h3>
        <div className='grid grid-cols-7 gap-2'>
          {DAYS_OPTIONS.map(day => {
            const isSelected = selectedDays.includes(day.value);

            return (
              <button
                key={day.value}
                type='button'
                onClick={() => onDayToggle(day.value)}
                className={`rounded-lg border p-3 text-center transition-all duration-200 ${
                  isSelected
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 bg-white text-gray-700 hover:bg-gray-50'
                } `}
              >
                <div className='text-xs font-medium'>{day.shortLabel}</div>
              </button>
            );
          })}
        </div>

        {/* {selectedDays.length > 0 && (
          <p className='mt-2 text-xs text-gray-500'>
            {selectedDays.length}개 요일 선택됨
          </p>
        )} */}
      </div>

      {/* 시간대 선택 */}
      <div>
        <h3 className='text-md mb-4 font-medium text-gray-900'>
          선호하는 시간대 <span className='text-red-500'>*</span>
        </h3>
        <div className='space-y-3'>
          {TIME_SLOTS_OPTIONS.map(timeSlot => {
            const isSelected = selectedTimeSlots.includes(timeSlot.value);

            return (
              <button
                key={timeSlot.value}
                type='button'
                onClick={() => onTimeSlotToggle(timeSlot.value)}
                className={`w-full rounded-xl border p-4 text-left transition-all duration-200 ${
                  isSelected
                    ? 'border-primary bg-primary/10'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                } `}
              >
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-3'>
                    <div className='text-xl'>{timeSlot.icon}</div>
                    <div>
                      <p
                        className={`text-sm font-medium ${
                          isSelected ? 'text-primary' : 'text-gray-900'
                        }`}
                      >
                        {timeSlot.label}
                      </p>
                      <p
                        className={`text-xs ${
                          isSelected ? 'text-primary' : 'text-gray-500'
                        }`}
                      >
                        {timeSlot.time}
                      </p>
                    </div>
                  </div>

                  {/* 선택 인디케이터 */}
                  <div
                    className={`flex h-5 w-5 items-center justify-center rounded-full border-2 ${
                      isSelected
                        ? 'border-primary bg-primary'
                        : 'border-gray-300'
                    } `}
                  >
                    {isSelected && (
                      <svg
                        className='h-3 w-3 text-white'
                        fill='currentColor'
                        viewBox='0 0 20 20'
                      >
                        <path
                          fillRule='evenodd'
                          d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                          clipRule='evenodd'
                        />
                      </svg>
                    )}
                  </div>
                </div>
              </button>
            );
          })}
        </div>

        {/* {selectedTimeSlots.length > 0 && (
          <p className='mt-2 text-xs text-gray-500'>
            {selectedTimeSlots.length}개 시간대 선택됨
          </p>
        )} */}
        <p className='mt-2 text-xs text-gray-500'>
          {selectedTimeSlots.length}개 시간대 선택됨
        </p>
      </div>

      {/* 유연성 안내 */}
      <div className='rounded-xl bg-amber-50 p-4'>
        <div className='flex items-start space-x-3'>
          <div className='mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-amber-400'>
            <svg
              className='h-3 w-3 text-white'
              fill='currentColor'
              viewBox='0 0 20 20'
            >
              <path
                fillRule='evenodd'
                d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <div>
            <p className='mb-1 text-sm font-medium text-amber-800'>
              ⏰ 유연한 스케줄링
            </p>
            <ul className='space-y-1 text-xs text-amber-700'>
              <li>
                • 여러 요일과 시간대를 선택하면 더 많은 옵션을 찾을 수 있어요
              </li>
              <li>
                • 선택한 시간대 외에도 유사한 시간의 클래스를 추천해드려요
              </li>
              <li>• 생활 패턴이 바뀌면 언제든지 수정 가능합니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
