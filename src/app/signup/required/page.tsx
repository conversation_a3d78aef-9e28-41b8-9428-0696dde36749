'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { getCurrentUser } from '@/lib/supabase/auth';
import { ChevronRight } from 'lucide-react';

interface AgreementOption {
  type: string;
  label: string;
  required: boolean;
  content?: string; // 약관 내용
}

// 약관 동의 옵션들 정의
const AGREEMENT_OPTIONS: AgreementOption[] = [
  {
    type: 'age',
    label: '만 14세 이상 확인',
    required: true,
    content: 'TODO: 만 14세 이상 확인 내용',
  },
  {
    type: 'terms',
    label: '서비스 이용약관',
    required: true,
    content: 'TODO: 서비스 이용약관 내용',
  },
  {
    type: 'privacy',
    label: '개인정보수집 및 이용 동의',
    required: true,
    content: 'TODO: 개인정보수집 및 이용 동의 내용',
  },
  {
    type: 'location',
    label: '위치기반 서비스 이용약관 동의',
    required: false,
    content: 'TODO: 위치기반 서비스 이용약관 내용',
  },
  {
    type: 'marketing',
    label: '수업 추천 및 마케팅 정보 수신 동의',
    required: false,
    content: 'TODO: 마케팅 정보 수신 동의 내용',
  },
];

export default function RequiredInfoPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showAgreementModal, setShowAgreementModal] =
    useState<AgreementOption | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    birthDate: '',
    gender: '',
  });
  const [agreements, setAgreements] = useState<Record<string, boolean>>({});
  const [allAgreed, setAllAgreed] = useState(false);

  useEffect(() => {
    // 로그인 여부 확인
    checkAuth();
  }, []);

  useEffect(() => {
    // 개별 동의 상태에 따른 전체 동의 상태 업데이트
    const allChecked = AGREEMENT_OPTIONS.every(
      option => agreements[option.type] === true
    );
    setAllAgreed(allChecked);
  }, [agreements]);

  const checkAuth = async () => {
    const user = await getCurrentUser();
    if (!user) {
      toast.error('로그인이 필요합니다.');
      router.push('/login');
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value.replace(/[^0-9]/g, '');

    // 자동 하이픈 추가
    if (value.length <= 3) {
      value = value;
    } else if (value.length <= 7) {
      value = value.slice(0, 3) + '-' + value.slice(3);
    } else if (value.length <= 11) {
      value =
        value.slice(0, 3) + '-' + value.slice(3, 7) + '-' + value.slice(7);
    }

    setFormData({ ...formData, phone: value });
  };

  const handleAllAgree = () => {
    const newState = !allAgreed;
    const newAgreements: Record<string, boolean> = {};
    AGREEMENT_OPTIONS.forEach(option => {
      newAgreements[option.type] = newState;
    });
    setAgreements(newAgreements);
  };

  const handleAgreementChange = (type: string, checked: boolean) => {
    setAgreements(prev => ({
      ...prev,
      [type]: checked,
    }));
  };

  const showAgreementContent = (option: AgreementOption) => {
    setShowAgreementModal(option);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 유효성 검사
    if (!formData.name || formData.name.length < 2) {
      toast.error('이름을 2자 이상 입력해주세요.');
      return;
    }

    const phoneRegex = /^01[0-9]-[0-9]{4}-[0-9]{4}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast.error('올바른 휴대폰 번호를 입력해주세요.');
      return;
    }

    if (!formData.birthDate) {
      toast.error('생년월일을 입력해주세요.');
      return;
    }

    if (!formData.gender) {
      toast.error('성별을 선택해주세요.');
      return;
    }

    // 필수 약관 동의 확인
    const requiredAgreements = AGREEMENT_OPTIONS.filter(opt => opt.required);
    const missingRequired = requiredAgreements.find(
      opt => !agreements[opt.type]
    );
    if (missingRequired) {
      toast.error('필수 약관에 모두 동의해주세요.');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/member/signup-complete', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          phone: formData.phone,
          birthDate: formData.birthDate,
          gender: formData.gender,
          agreements: {
            ...(agreements || {}),
            timestamp: new Date().toISOString(),
          },
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '정보 저장에 실패했습니다.');
      }

      toast.success('회원가입이 완료되었습니다!');
      router.push('/onboarding'); // 온보딩 페이지로 이동
    } catch (error) {
      console.error('Error:', error);
      toast.error(
        error instanceof Error ? error.message : '오류가 발생했습니다.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='flex min-h-screen items-center justify-center px-4'>
      <div className='w-full max-w-3xl space-y-8'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold'>필수 정보 입력</h1>
          <p className='mt-2 text-sm text-gray-600'>
            서비스 이용을 위한 필수 정보를 입력해주세요.
          </p>
        </div>

        <form onSubmit={handleSubmit} className='space-y-6'>
          <div className='space-y-4'>
            <div>
              <Label htmlFor='name'>이름 *</Label>
              <Input
                id='name'
                type='text'
                placeholder='실명을 입력해주세요'
                value={formData.name}
                onChange={e =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
                className='mt-1'
              />
            </div>

            <div>
              <Label htmlFor='phone'>휴대폰 번호 *</Label>
              <Input
                id='phone'
                type='tel'
                placeholder='010-0000-0000'
                value={formData.phone}
                onChange={handlePhoneChange}
                maxLength={13}
                required
                className='mt-1'
              />
            </div>

            <div>
              <Label htmlFor='birthDate'>생년월일 *</Label>
              <Input
                id='birthDate'
                type='date'
                value={formData.birthDate}
                onChange={e =>
                  setFormData({ ...formData, birthDate: e.target.value })
                }
                required
                className='mt-1'
                max={new Date().toISOString().split('T')[0]} // 오늘 날짜까지만 선택 가능
                min='1900-01-01' // 최소 날짜 제한
              />
            </div>

            <div>
              <Label>성별 *</Label>
              <div className='mt-1 flex gap-3'>
                <button
                  type='button'
                  onClick={() => setFormData({ ...formData, gender: 'male' })}
                  className={`flex-1 rounded-lg border px-4 py-2 transition-all ${
                    formData.gender === 'male'
                      ? 'border-primary bg-primary font-medium text-white'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                >
                  남자
                </button>
                <button
                  type='button'
                  onClick={() => setFormData({ ...formData, gender: 'female' })}
                  className={`flex-1 rounded-lg border px-4 py-2 transition-all ${
                    formData.gender === 'female'
                      ? 'border-primary bg-primary font-medium text-white'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                >
                  여자
                </button>
              </div>
            </div>
          </div>

          <div className='mt-6 flex flex-col gap-5'>
            <div>
              <div className='flex items-center gap-3'>
                <Checkbox
                  id='all-agree'
                  checked={allAgreed}
                  onCheckedChange={handleAllAgree}
                  className='h-5 w-5'
                />
                <Label
                  htmlFor='all-agree'
                  className='cursor-pointer text-sm font-medium'
                >
                  전체 동의
                </Label>
              </div>
            </div>

            {/* 개별 약관 동의 */}
            <div className='flex flex-col gap-4 rounded-md border px-4 py-5'>
              {AGREEMENT_OPTIONS.map(option => (
                <div
                  key={option.type}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-3'>
                    <Checkbox
                      id={option.type}
                      checked={agreements[option.type] || false}
                      onCheckedChange={checked =>
                        handleAgreementChange(option.type, !!checked)
                      }
                      className='h-4 w-4'
                    />
                    <Label
                      htmlFor={option.type}
                      className='flex-1 cursor-pointer text-sm'
                    >
                      {option.required && (
                        <span className='text-red-500'>(필수) </span>
                      )}
                      {!option.required && (
                        <span className='text-gray-500'>(선택) </span>
                      )}
                      {option.label}
                    </Label>
                  </div>
                  <button
                    type='button'
                    onClick={() => showAgreementContent(option)}
                    className='text-gray-400 transition-colors hover:text-gray-600'
                  >
                    <ChevronRight className='h-4 w-4' />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <Button
            type='submit'
            className='w-full'
            disabled={
              isLoading ||
              AGREEMENT_OPTIONS.filter(opt => opt.required).some(
                opt => !agreements[opt.type]
              )
            }
          >
            {isLoading ? '처리 중...' : '완료'}
          </Button>
        </form>

        {/* 약관 내용 모달 */}
        {showAgreementModal && (
          <div className='bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4'>
            <div className='max-h-[80vh] w-full max-w-2xl overflow-hidden rounded-lg bg-white'>
              <div className='border-b p-6'>
                <h3 className='text-lg font-semibold'>
                  {showAgreementModal.label}
                </h3>
              </div>
              <div className='max-h-[60vh] overflow-y-auto p-6'>
                <p className='text-sm whitespace-pre-wrap text-gray-600'>
                  {showAgreementModal.content}
                </p>
              </div>
              <div className='border-t p-6'>
                <Button
                  onClick={() => setShowAgreementModal(null)}
                  className='w-full'
                >
                  확인
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
