import { partnerStudioApi } from '@/lib/api/partner/studio.api';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';

export const useGetStudioById = (studioId: string | undefined | null) => {
  return useQuery({
    queryKey: ['studios', studioId],
    queryFn: () => partnerStudioApi.getStudioById(studioId as string),
    enabled: !!studioId, // studioId가 있을 때만 쿼리 실행
  });
};

export const useSuspenseGetStudioById = (studioId: string) => {
  return useSuspenseQuery({
    queryKey: ['studios', studioId],
    queryFn: () => partnerStudioApi.getStudioById(studioId),
  });
};
