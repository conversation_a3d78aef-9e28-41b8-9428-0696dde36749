import { usePartnerStore } from '../_store/partner.store';
import { useGetStudioById } from './useGetStudioById';
import { useQuery } from '@tanstack/react-query';
import { partnerStudioApi } from '@/lib/api/partner/studio.api';

export const useMyStudio = () => {
  const studioId = usePartnerStore(state => state.studioId);
  // studioId가 있으면 해당 스튜디오를, 없으면 목록 조회 후 첫 스튜디오를 반환
  const byIdQuery = useGetStudioById(studioId);

  const listQuery = useQuery({
    queryKey: ['studios'],
    queryFn: () => partnerStudioApi.getMyStudios(),
    enabled: !studioId,
  });

  const data = studioId
    ? byIdQuery.data
    : Array.isArray(listQuery.data) && listQuery.data.length > 0
      ? listQuery.data[0]
      : undefined;

  return {
    data,
    isLoading: studioId ? byIdQuery.isLoading : listQuery.isLoading,
    isError: studioId ? byIdQuery.isError : listQuery.isError,
    error: studioId ? byIdQuery.error : listQuery.error,
  } as const;
};
