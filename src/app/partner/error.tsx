'use client';

import { useEffect } from 'react';
import ErrorPage from '@/components/ErrorPage';

export default function PartnerError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Partner error boundary caught:', error);
  }, [error]);

  return (
    <ErrorPage
      title='오류가 발생했습니다'
      message='시스템 오류로 인해 일시적으로 서비스를 이용할 수 없습니다. 잠시 후 다시 시도해주세요.'
      theme='partner'
      actions={[
        {
          kind: 'button',
          label: '다시 시도',
          variant: 'outline',
          onClick: reset,
        },
        {
          kind: 'link',
          label: '파트너 홈',
          href: '/partner/dashboard',
        },
      ]}
    />
  );
}