import {
  CreateStudioRequest,
  UpdateStudioRequest,
} from '@/lib/api/partner/studio.schema';
import {
  EditStudioFormData,
  InstructorFormData,
  StudioFormData,
} from '../_schemas/form.schema';
import {
  geocode,
  findNearestStationByAddress,
} from '@/lib/api/utils/geocode.api';
import { partnerStudioApi } from '@/lib/api/partner/studio.api';
import { StudioImage } from '@/lib/schemas/studio';
import { CreateInstructorRequest } from '@/lib/api/partner/instructor.schema';

export async function toStudioFormDataToRequestData(
  studioFormData: StudioFormData
): Promise<Omit<CreateStudioRequest, 'partnerId'>> {
  const {
    address,
    name,
    phone,
    addressDetail,
    description,
    links,
    images,
    amenities,
    postalCode,
  } = studioFormData;

  // amenities가 있으면 그대로 사용, 없으면 빈 객체
  const processedAmenities = amenities || {};

  const coordinates = await geocode(address);
  if (!coordinates) {
    // 지오코딩 실패 시 사용자 친화적 메시지로 전달
    throw new Error(
      '주소의 좌표를 찾지 못했습니다. 주소를 다시 확인해 주세요.'
    );
  }

  // 가까운 지하철역 조회 (카카오 Local API)
  let nearestStation: string | undefined;
  let stationDistance: number | undefined;
  try {
    const nearest = await findNearestStationByAddress(address);
    if (nearest?.station) {
      nearestStation = nearest.station;
      stationDistance = nearest.distance;
    }
  } catch (e) {
    // 생성 플로우에서는 실패해도 진행
    console.warn(
      'toStudioFormDataToRequestData: nearest station lookup failed:',
      e
    );
  }

  let uploadedImages: StudioImage[] | undefined = undefined;
  if (images && images.length > 0) {
    // 1) 이미 서버에 업로드되어 url/path가 존재하는 이미지 포함
    const existingImages: StudioImage[] = images
      .filter(img => !!img.url && !!img.path)
      .map(img => ({ path: img.path!, url: img.url! }));

    // 2) 새로 업로드해야 하는 파일만 업로드 수행
    const filesToUpload = images.filter(img => img.file).map(img => img.file!);
    let newlyUploaded: StudioImage[] = [];
    if (filesToUpload.length > 0) {
      const uploadPromises = filesToUpload.map(file =>
        partnerStudioApi.uploadStudioImage({
          file,
          prefix: 'gallery',
        })
      );

      const uploadResults = await Promise.all(uploadPromises);
      newlyUploaded = uploadResults.map(result => ({
        path: result.path,
        url: result.url,
      }));
    }

    const combined = [...existingImages, ...newlyUploaded];
    uploadedImages = combined.length > 0 ? combined : undefined;
  }

  // 링크 빈 문자열 제거
  const sanitizedLinks = links
    ? {
        ...(links.website ? { website: links.website } : {}),
        ...(links.sns ? { sns: links.sns } : {}),
      }
    : undefined;

  const req: Omit<CreateStudioRequest, 'partnerId'> = {
    name,
    phone,
    address,
    latitude: coordinates.latitude,
    longitude: coordinates.longitude,
    ...(nearestStation && { nearestStation }),
    ...(typeof stationDistance === 'number' && { stationDistance }),
    ...(addressDetail && { addressDetail }),
    ...(description && { description }),
    ...(postalCode && { postalCode }),
    ...(sanitizedLinks && { links: sanitizedLinks }),
    ...(Object.keys(processedAmenities).length > 0 && {
      amenities: processedAmenities,
    }),
    ...(uploadedImages && { images: uploadedImages }),
  };

  return req;
}

export async function toEditStudioFormDataToUpdateRequest(
  formData: EditStudioFormData,
  options?: { prevAddress?: string }
): Promise<UpdateStudioRequest> {
  const { images, amenities, ...restData } = formData;

  // 주소 변경 시에만 좌표/가까운 역 계산
  let latitude: number | undefined;
  let longitude: number | undefined;
  let nearestStation: string | undefined;
  let stationDistance: number | undefined;

  try {
    const prevAddress = options?.prevAddress;
    if (restData.address && prevAddress && restData.address !== prevAddress) {
      const coords = await geocode(restData.address);
      latitude = coords.latitude;
      longitude = coords.longitude;
      try {
        const nearest = await findNearestStationByAddress(restData.address);
        if (nearest?.station) {
          nearestStation = nearest.station;
          stationDistance = nearest.distance;
        }
      } catch (e) {
        console.warn(
          'toEditStudioFormDataToUpdateRequest: nearest station lookup failed:',
          e
        );
      }
    }
  } catch (e) {
    console.warn('toEditStudioFormDataToUpdateRequest: geocode failed:', e);
  }

  // 이미지 병합 (기존 + 신규 업로드)
  const existingImages: StudioImage[] = (images || [])
    .filter(img => img.path && img.url)
    .map(img => ({ path: img.path!, url: img.url! }));

  const filesToUpload = (images || [])
    .filter(img => img.file)
    .map(img => img.file!);

  let newlyUploaded: StudioImage[] = [];
  if (filesToUpload.length > 0) {
    const uploadResults = await Promise.all(
      filesToUpload.map(file =>
        partnerStudioApi.uploadStudioImage({ file, prefix: 'gallery' })
      )
    );
    newlyUploaded = uploadResults.map(r => ({ path: r.path, url: r.url }));
  }

  // amenities가 있으면 그대로 사용, 없으면 undefined
  const processedAmenities = amenities;

  type Links = { website?: string; sns?: string } | undefined;
  const rawLinks = (restData as { links?: Links }).links;
  const sanitizedLinks: Links = rawLinks
    ? {
        ...(rawLinks.website ? { website: rawLinks.website } : {}),
        ...(rawLinks.sns ? { sns: rawLinks.sns } : {}),
      }
    : undefined;

  const req: UpdateStudioRequest = {
    ...(restData as Record<string, unknown>),
    ...(sanitizedLinks && { links: sanitizedLinks }),
    images: [...existingImages, ...newlyUploaded],
    ...(processedAmenities && { amenities: processedAmenities }),
    ...(latitude !== undefined && longitude !== undefined
      ? { latitude, longitude }
      : {}),
    ...(nearestStation ? { nearestStation } : {}),
    ...(typeof stationDistance === 'number' ? { stationDistance } : {}),
  };

  return req;
}

export async function toInstructorFormDataToRequestData(
  formData: InstructorFormData
): Promise<CreateInstructorRequest> {
  const {
    name,
    gender,
    contact,
    description,
    links,
    experienceTotalYears,
    specialties,
    certificates,
    profileImages,
  } = formData;

  // 이미 업로드된 이미지들에서 url과 path가 있는 것만 사용 (thumbnail 정보 포함)
  let uploadedImages: { path: string; url: string; thumbnail?: { path: string; url: string } }[] | undefined = undefined;
  console.log('instructor images', profileImages);
  if (profileImages && profileImages.length > 0) {
    uploadedImages = profileImages
      .filter(img => img.url && img.path)
      .map(img => ({
        path: img.path!,
        url: img.url!,
        ...(img.thumbnail && {
          thumbnail: {
            path: img.thumbnail.path,
            url: img.thumbnail.url,
          }
        }),
      }));
  }

  // 빈 문자열('')을 제거한 링크 정규화 (서버 z.url() 스키마와 정합성 맞춤)
  const sanitizedLinks = links
    ? {
        ...(links.website ? { website: links.website } : {}),
        ...(links.sns ? { sns: links.sns } : {}),
      }
    : undefined;

  const req: CreateInstructorRequest = {
    name,
    gender,
    description,
    experienceTotalYears,
    specialties,
    ...(contact && { contact }),
    ...(sanitizedLinks && { links: sanitizedLinks }),
    ...(certificates && { certificates }),
    profileImages: uploadedImages ?? [],
  };

  return req;
}
