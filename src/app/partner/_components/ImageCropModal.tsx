'use client';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useState, useRef, useCallback, useEffect } from 'react';
import ReactCrop, {
  centerCrop,
  makeAspectCrop,
  Crop,
  PixelCrop,
} from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

interface ImageCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  onCropComplete: (cropData: {
    x: number;
    y: number;
    width: number;
    height: number;
    unit: 'px' | '%';
  }) => void;
  onSkip?: () => void;
  initialCrop?: {
    x: number;
    y: number;
    width: number;
    height: number;
    unit: 'px' | '%';
  };
}

function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: '%',
        width: 90,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  )
}

export default function ImageCropModal({
  isOpen,
  onClose,
  imageUrl,
  onCropComplete,
  onSkip,
  initialCrop,
}: ImageCropModalProps) {
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const imgRef = useRef<HTMLImageElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  function onImageLoad(e: React.SyntheticEvent<HTMLImageElement>) {
    const { width, height } = e.currentTarget;
    
    let newCrop: Crop;
    
    if (initialCrop && initialCrop.unit === '%') {
      newCrop = {
        unit: '%',
        width: initialCrop.width,
        height: initialCrop.height,
        x: initialCrop.x,
        y: initialCrop.y,
      };
    } else {
      newCrop = centerAspectCrop(width, height, 1);
    }
    
    setCrop(newCrop);
  }

  const generatePreview = useCallback(
    async (image: HTMLImageElement, crop: PixelCrop) => {
      const canvas = previewCanvasRef.current;
      if (!canvas || !crop.width || !crop.height) {
        return;
      }

      const scaleX = image.naturalWidth / image.width;
      const scaleY = image.naturalHeight / image.height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        throw new Error('No 2d context');
      }

      const pixelRatio = window.devicePixelRatio;
      const previewSize = 150;

      canvas.width = previewSize * pixelRatio;
      canvas.height = previewSize * pixelRatio;

      ctx.scale(pixelRatio, pixelRatio);
      ctx.imageSmoothingQuality = 'high';

      const scaleToFit = previewSize / Math.max(crop.width, crop.height);

      const scaledWidth = crop.width * scaleToFit;
      const scaledHeight = crop.height * scaleToFit;
      const offsetX = (previewSize - scaledWidth) / 2;
      const offsetY = (previewSize - scaledHeight) / 2;

      ctx.drawImage(
        image,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        offsetX,
        offsetY,
        scaledWidth,
        scaledHeight,
      );
    },
    []
  );

  useEffect(() => {
    if (completedCrop && imgRef.current) {
      generatePreview(imgRef.current, completedCrop);
    }
  }, [completedCrop, generatePreview]);

  const handleSave = () => {
    if (!completedCrop || !imgRef.current) return;

    const { width, height } = imgRef.current;
    
    const cropData = {
      x: (completedCrop.x / width) * 100,
      y: (completedCrop.y / height) * 100,
      width: (completedCrop.width / width) * 100,
      height: (completedCrop.height / height) * 100,
      unit: '%' as const,
    };

    onCropComplete(cropData);
    onClose();
  };

  const handleReset = () => {
    if (!imgRef.current) return;
    
    const { width, height } = imgRef.current;
    const newCrop = centerAspectCrop(width, height, 1);
    setCrop(newCrop);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>썸네일 영역 설정</DialogTitle>
          <DialogDescription>
            프로필 이미지에서 썸네일로 표시될 영역을 선택해주세요. 원본 이미지는 그대로 보존됩니다.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 크롭 영역 */}
          <div className="lg:col-span-2">
            <div className="border rounded-lg overflow-hidden bg-gray-50">
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={1} // 1:1 비율 고정
                minWidth={50}
                minHeight={50}
                keepSelection
              >
                <img
                  ref={imgRef}
                  alt="Crop me"
                  src={imageUrl}
                  onLoad={onImageLoad}
                  className="max-w-full max-h-[60vh] object-contain"
                />
              </ReactCrop>
            </div>
          </div>

          {/* 미리보기 영역 */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-2">썸네일 미리보기</h4>
              <div className="border rounded-lg p-4 bg-gray-50">
                <canvas
                  ref={previewCanvasRef}
                  className="border rounded max-w-full"
                  style={{
                    width: '150px',
                    height: '150px',
                    objectFit: 'cover',
                  }}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="w-full"
              >
                영역 초기화
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            취소
          </Button>
          {onSkip && (
            <Button variant="secondary" onClick={onSkip}>
              건너뛰기
            </Button>
          )}
          <Button onClick={handleSave} disabled={!completedCrop}>
            적용
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}