export default function DashboardLoadingSkeleton() {
  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 프로필 카드 스켈레톤 */}
      <div className='px-4 py-6'>
        <div className='animate-pulse rounded-2xl bg-white p-6 shadow-sm'>
          <div className='mb-6 flex items-center'>
            <div className='mr-4 h-16 w-16 rounded-full bg-gray-200'></div>
            <div className='flex-1'>
              <div className='mb-2 h-6 w-32 rounded bg-gray-200'></div>
              <div className='h-4 w-48 rounded bg-gray-200'></div>
            </div>
          </div>
          <div className='mb-4 grid grid-cols-2 gap-3'>
            {[...Array(4)].map((_, i) => (
              <div key={i} className='rounded-lg bg-gray-50 p-3'>
                <div className='mx-auto mb-2 h-6 w-8 rounded bg-gray-200'></div>
                <div className='mx-auto h-3 w-12 rounded bg-gray-200'></div>
              </div>
            ))}
          </div>
          <div className='grid grid-cols-3 gap-4 border-t border-gray-200 pt-4'>
            {[...Array(3)].map((_, i) => (
              <div key={i} className='text-center'>
                <div className='mx-auto mb-2 h-5 w-5 rounded bg-gray-200'></div>
                <div className='mx-auto mb-2 h-8 w-12 rounded bg-gray-200'></div>
                <div className='mx-auto h-4 w-16 rounded bg-gray-200'></div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 빠른 액션 스켈레톤 */}
      <div className='mb-6 px-4'>
        <div className='grid grid-cols-2 gap-4'>
          {[...Array(4)].map((_, i) => (
            <div
              key={i}
              className='animate-pulse rounded-2xl bg-white p-4 shadow-sm'
            >
              <div className='mb-2 flex items-center'>
                <div className='mr-3 h-8 w-8 rounded-lg bg-gray-200'></div>
                <div className='h-4 w-20 rounded bg-gray-200'></div>
              </div>
              <div className='h-3 w-24 rounded bg-gray-200'></div>
            </div>
          ))}
        </div>
      </div>

      {/* 오늘의 수업 스켈레톤 */}
      <div className='mb-6 px-4'>
        <div className='animate-pulse rounded-2xl bg-gradient-to-r from-indigo-50 to-purple-50 p-5'>
          <div className='mb-3 h-6 w-24 rounded bg-indigo-200'></div>
          <div className='space-y-3'>
            {[...Array(2)].map((_, i) => (
              <div key={i} className='rounded-lg bg-white p-4 shadow-sm'>
                <div className='mb-2 flex items-center justify-between'>
                  <div className='h-5 w-32 rounded bg-gray-200'></div>
                  <div className='h-5 w-12 rounded-full bg-gray-200'></div>
                </div>
                <div className='flex items-center justify-between'>
                  <div className='h-4 w-24 rounded bg-gray-200'></div>
                  <div className='h-4 w-16 rounded bg-gray-200'></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
