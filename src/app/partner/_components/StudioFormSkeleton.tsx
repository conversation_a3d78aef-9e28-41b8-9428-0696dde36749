export default function StudioFormSkeleton() {
  return (
    <div className='p-3'>
      <div className='mb-4'>
        <div className='h-6 w-24 animate-pulse rounded bg-gray-200' />
      </div>

      <div className='flex flex-col gap-6'>
        {/* 기본 정보 섹션 타이틀 */}
        <div>
          <div className='mb-4 h-5 w-20 animate-pulse rounded bg-gray-200' />
          <div className='flex flex-col gap-5'>
            {/* 주소 검색 */}
            <div>
              <div className='mb-2 h-4 w-28 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 상세 주소 */}
            <div>
              <div className='mb-2 h-4 w-24 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 센터 이름 */}
            <div>
              <div className='mb-2 h-4 w-20 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 센터 소개 */}
            <div>
              <div className='mb-2 h-4 w-20 animate-pulse rounded bg-gray-200' />
              <div className='h-28 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 대표 이미지 업로드 썸네일 스켈레톤 */}
            <div>
              <div className='mb-2 h-4 w-24 animate-pulse rounded bg-gray-200' />
              <div className='grid grid-cols-3 gap-2'>
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className='h-24 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200'
                  />
                ))}
              </div>
            </div>

            {/* 연락처 */}
            <div>
              <div className='mb-2 h-4 w-28 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 웹사이트 링크 */}
            <div>
              <div className='mb-2 h-4 w-24 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* SNS 링크 */}
            <div>
              <div className='mb-2 h-4 w-20 animate-pulse rounded bg-gray-200' />
              <div className='h-10 w-full animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>
          </div>
        </div>

        {/* 시설 정보 섹션 */}
        <div>
          <div className='mb-4 h-5 w-28 animate-pulse rounded bg-gray-200' />
          <div className='flex flex-col gap-4'>
            {/* 주차장 */}
            <div className='flex items-center gap-2'>
              <div className='h-4 w-12 animate-pulse rounded bg-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 샤워실 */}
            <div className='flex items-center gap-2'>
              <div className='h-4 w-12 animate-pulse rounded bg-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 운동복 */}
            <div className='flex items-center gap-2'>
              <div className='h-4 w-12 animate-pulse rounded bg-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>

            {/* 락커룸 + 요금 */}
            <div className='flex items-center gap-2'>
              <div className='h-4 w-12 animate-pulse rounded bg-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
              <div className='h-10 flex-1 animate-pulse rounded-md bg-gray-100 ring-1 ring-gray-200' />
            </div>
          </div>
        </div>

        {/* 제출 버튼 */}
        <div className='pt-2'>
          <div className='h-12 w-full animate-pulse rounded-md bg-gray-200' />
        </div>
      </div>
    </div>
  );
}
