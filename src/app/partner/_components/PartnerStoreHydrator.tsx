'use client';
import { useEffect } from 'react';
import { usePartnerStore } from '@/app/partner/_store/partner.store';

interface PartnerStoreHydratorProps {
  initialData: {
    studioId: string | null;
    studioName: string | null;
  };
}

export default function PartnerStoreHydrator({
  initialData,
}: PartnerStoreHydratorProps) {
  const setStudio = usePartnerStore(state => state.setStudio);

  useEffect(() => {
    setStudio(initialData.studioId, initialData.studioName);
  }, [initialData, setStudio]);

  return null;
}
