'use client';
import StudioForm from '@/app/partner/_components/StudioForm';
import { StudioFormData } from '@/app/partner/_schemas/form.schema';
import { usePartnerStore } from '@/app/partner/_store/partner.store';
import { toStudioFormDataToRequestData } from '@/app/partner/_utils/transform';
import { partnerStudioApi } from '@/lib/api/partner/studio.api';
import { useRouter } from 'next/navigation';

export default function NewStudioClient() {
  const router = useRouter();
  const setStudioId = usePartnerStore(state => state.setStudioId);

  const handleSubmit = async (data: StudioFormData) => {
    const requestData = await toStudioFormDataToRequestData(data);
    const created = await partnerStudioApi.createStudio(requestData);
    setStudioId(created.id);

    // 캐시 갱신을 위해 router refresh 호출
    router.refresh();
    router.replace('/partner/studios/edit');
  };

  return (
    <StudioForm
      mode='create'
      onSubmit={handleSubmit}
      submitButtonText='센터등록'
    />
  );
}
