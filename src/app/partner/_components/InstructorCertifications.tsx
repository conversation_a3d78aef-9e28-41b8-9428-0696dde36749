import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Certificate, CertificateSchema } from '@/lib/schemas/instructor';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit2, Plus, ShieldCheck, Trash2 } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

// UI 상태 관리용 Certificate 확장 (id 포함)
interface CertificationWithId extends Certificate {
  id?: string;
}

interface InstructorCertificationsProps {
  // 자격증 데이터
  certifications: CertificationWithId[];

  // CRUD 핸들러들
  onAddCertification: (certification: Certificate) => void;
  onUpdateCertification: (id: string, certification: Certificate) => void;
  onDeleteCertification: (id: string) => void;

  // UI 설정
  className?: string;
  disabled?: boolean;
}

export default function InstructorCertifications({
  certifications,
  onAddCertification,
  onUpdateCertification,
  onDeleteCertification,
  className = '',
  disabled = false,
}: InstructorCertificationsProps) {
  const [showCertificationForm, setShowCertificationForm] = useState(false);
  const [editingCertification, setEditingCertification] =
    useState<CertificationWithId | null>(null);

  const certificationForm = useForm<Certificate>({
    resolver: zodResolver(CertificateSchema),
    defaultValues: {
      name: '',
    },
  });

  const resetCertificationForm = useCallback(() => {
    certificationForm.reset({
      name: '',
    });
  }, [certificationForm]);

  const handleCertificationSubmit = useCallback(
    (certificationData: Certificate) => {
      if (editingCertification) {
        // Update existing certification
        onUpdateCertification(editingCertification.id!, certificationData);
        toast.success('자격증이 수정되었습니다.');
      } else {
        // Add new certification
        onAddCertification(certificationData);
        toast.success('자격증이 추가되었습니다.');
      }

      setShowCertificationForm(false);
      setEditingCertification(null);
      resetCertificationForm();
    },
    [
      editingCertification,
      onAddCertification,
      onUpdateCertification,
      resetCertificationForm,
    ]
  );

  const editCertification = useCallback(
    (certification: CertificationWithId) => {
      setEditingCertification(certification);
      certificationForm.reset(certification);
      setShowCertificationForm(true);
    },
    [certificationForm]
  );

  const deleteCertification = useCallback(
    (certificationId: string) => {
      onDeleteCertification(certificationId);
      toast.success('자격증이 삭제되었습니다.');
    },
    [onDeleteCertification]
  );

  const handleAddClick = useCallback(() => {
    setEditingCertification(null);
    resetCertificationForm();
    setShowCertificationForm(true);
  }, [resetCertificationForm]);

  const handleCancelClick = useCallback(() => {
    setShowCertificationForm(false);
    setEditingCertification(null);
    resetCertificationForm();
  }, [resetCertificationForm]);

  return (
    <div className={className}>
      <h2 className='mb-4 text-lg font-semibold'>자격 사항</h2>
      <div className='flex flex-col gap-4'>
        <Button
          type='button'
          variant='secondary'
          onClick={handleAddClick}
          disabled={disabled}
          className='w-full'
        >
          <Plus size={16} className='mr-2' />
          자격증 추가
        </Button>

        {/* 자격증 추가/수정 폼 */}
        {showCertificationForm && (
          <div className='rounded-md border border-[#DFE7FF] bg-[#FAFBFF] p-4'>
            <h3 className='mb-3 text-sm font-semibold'>
              {editingCertification ? '자격증 수정' : '자격증 추가'}
            </h3>
            <Form {...certificationForm}>
              <div className='flex flex-col gap-3'>
                <FormField
                  control={certificationForm.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>자격증명 *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='예: 요가 지도자 2급'
                          disabled={disabled}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='flex gap-2'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleCancelClick}
                    disabled={disabled}
                    className='flex-1'
                  >
                    취소
                  </Button>
                  <Button
                    type='button'
                    onClick={certificationForm.handleSubmit(
                      handleCertificationSubmit
                    )}
                    disabled={disabled}
                    className='flex-1'
                  >
                    {editingCertification ? '수정' : '등록'}
                  </Button>
                </div>
              </div>
            </Form>
          </div>
        )}

        {/* 등록된 자격증 목록 */}
        {certifications.length > 0 && (
          <div className='flex flex-col gap-3'>
            <h3 className='text-sm font-semibold'>등록된 자격증</h3>
            {certifications.map(certification => (
              <div
                key={certification.id}
                className='rounded-md border px-2 py-1'
              >
                <div className='flex items-center justify-between'>
                  <div className='flex-1'>
                    <h4 className='flex items-center gap-1 text-sm font-medium'>
                      <ShieldCheck size={16} className='text-primary' />
                      {certification.name}
                    </h4>
                  </div>
                  <div className='flex gap-1'>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => editCertification(certification)}
                      disabled={disabled}
                      className='h-8 w-8 !p-0'
                    >
                      <Edit2 size={14} />
                    </Button>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => deleteCertification(certification.id!)}
                      disabled={disabled}
                      className='h-8 w-8 !p-0 text-red-500 hover:text-red-700'
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
