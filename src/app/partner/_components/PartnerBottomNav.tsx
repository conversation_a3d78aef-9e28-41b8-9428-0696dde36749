'use client';

import { cn } from '@/lib/utils';
import { Home, Users, BookOpen, Building2 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { usePartnerStore } from '@/app/partner/_store/partner.store';

/**
 * 파트너 페이지에서 버텀 네비게이션을 숨겨야 하는 경로들
 */
const PARTNER_BOTTOM_NAV_HIDDEN_PATHS = [
  '/partner/login',
  '/partner/register',
  '/partner/suspended',
];

/**
 * 주어진 경로에서 파트너 버텀 네비게이션을 표시해야 하는지 결정
 * @param pathname - 현재 경로
 * @returns 버텀 네비게이션 표시 여부
 */
export function shouldShowPartnerBottomNav(pathname: string): boolean {
  // 파트너 경로가 아니면 숨김
  if (!pathname.startsWith('/partner')) {
    return false;
  }

  // 숨겨야 할 경로인지 확인
  return !PARTNER_BOTTOM_NAV_HIDDEN_PATHS.some(hiddenPath =>
    pathname.startsWith(hiddenPath)
  );
}

export function PartnerBottomNav() {
  const pathname = usePathname();
  const studioId = usePartnerStore(state => state.studioId);
  const studioHref = studioId
    ? '/partner/studios/edit'
    : '/partner/studios/new';
  const navItems = [
    {
      href: '/partner/dashboard',
      label: '홈',
      icon: Home,
      activePattern: /^\/partner\/dashboard$/,
    },
    {
      href: studioHref,
      label: '센터',
      icon: Building2,
      activePattern: /^\/partner\/studios/,
    },

    {
      href: '/partner/instructor',
      label: '강사',
      icon: Users,
      activePattern: /^\/partner\/(instructor|profile|onboarding)/,
    },
    {
      href: '/partner/classes/schedules',
      label: '수업 일정',
      icon: BookOpen,
      activePattern: /^\/partner\/classes/,
    },
  ] as const;

  return (
    <nav
      className={cn(
        'fixed bottom-0 z-[49]',
        'mx-auto w-full max-w-3xl py-1',
        'border-t border-[#eeeeee] bg-white'
      )}
    >
      <div className='flex justify-around'>
        {navItems.map(item => {
          const IconComponent = item.icon;
          const isActive = item.activePattern.test(pathname);

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex flex-col items-center px-3 py-2 transition-colors',
                isActive
                  ? 'text-indigo-600'
                  : 'text-gray-500 hover:text-gray-700'
              )}
            >
              <IconComponent
                className={cn(
                  'mb-1 h-5 w-5',
                  isActive ? 'text-indigo-600' : 'text-gray-500'
                )}
              />
              <span
                className={cn(
                  'text-xs font-medium',
                  isActive ? 'text-indigo-600' : 'text-gray-500'
                )}
              >
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
