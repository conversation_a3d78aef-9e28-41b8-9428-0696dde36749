'use client';

import { cn } from '@/lib/utils';
import { Camera, X, Loader2, Crop } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import type { FormImage } from '../_schemas/form.schema';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import ImageCropModal from './ImageCropModal';
import { generateThumbnail, generateThumbnailPath, generateThumbnailFileName } from '@/lib/utils/thumbnail';

interface MultipleImageUploadProps {
  values: FormImage[];
  onChange: (data: FormImage[]) => void;
  type: 'studio' | 'instructor' | 'class';
  maxImages?: number;
}
const createFileId = (file: File) => {
  return `${file.name}-${file.size}-${file.lastModified}`;
};

export default function MultipleImageUpload({
  values,
  onChange,
  type,
  maxImages = 10,
}: MultipleImageUploadProps) {
  const [uploadingIds, setUploadingIds] = useState<Set<string>>(new Set());
  const [convertingIds, setConvertingIds] = useState<Set<string>>(new Set());
  
  // Progress UI 상태 관리
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({ current: 0, total: 0 });
  const [currentFileName, setCurrentFileName] = useState<string>('');
  const [uploadCancelled, setUploadCancelled] = useState(false);

  // Crop modal 상태 관리
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [selectedImageForCrop, setSelectedImageForCrop] = useState<FormImage | null>(null);
  const [pendingCropImages, setPendingCropImages] = useState<FormImage[]>([]);

  // 브라우저 감지 함수
  const isSafari = (): boolean => {
    return navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');
  };

  // Mobile Safari 감지 함수
  const isMobileSafari = (): boolean => {
    const userAgent = navigator.userAgent;
    return (
      isSafari() &&
      (userAgent.includes('Mobile') || userAgent.includes('iPhone') || userAgent.includes('iPad'))
    );
  };

  // 모바일 환경 감지 함수 (전체)
  const isMobile = (): boolean => {
    const userAgent = navigator.userAgent;
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
           (navigator.maxTouchPoints ? navigator.maxTouchPoints > 1 : false);
  };

  // WebP 지원 여부 확인
  const checkWebPSupport = (): Promise<boolean> => {
    if (typeof window === 'undefined') return Promise.resolve(false);
    return new Promise((resolve) => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  };

  // HEIC 파일 크기 기반으로 변환 후 예상 크기 계산
  const estimateConvertedSize = (originalSizeMB: number, quality: number, hasResizing: boolean): number => {
    // 경험적 공식: HEIC는 매우 효율적인 압축이므로 JPEG/WebP로 변환 시 크기 증가
    let multiplier = 2.5; // 기본 2.5배 증가
    
    // 품질에 따른 조정
    if (quality <= 0.3) multiplier *= 0.6;       // 저품질: 60%
    else if (quality <= 0.5) multiplier *= 0.8;  // 중품질: 80%
    
    // 해상도 축소 시 크기 감소
    if (hasResizing) multiplier *= 0.4;          // 해상도 축소: 40%
    
    // Safari 환경에서는 더 보수적으로 계산
    if (isSafari()) multiplier *= 1.2;
    
    return originalSizeMB * multiplier;
  };

  // HEIC 파일 감지 함수 (브라우저 호환성 강화)
  const isHeicFile = (file: File): boolean => {
    // 파일 확장자 체크 (가장 확실한 방법)
    const fileName = file.name.toLowerCase();
    const isHeicByExtension = fileName.endsWith('.heic') || fileName.endsWith('.heif');
    
    // MIME 타입 체크 (대소문자 구분 없이)
    const mimeType = file.type.toLowerCase();
    const isHeicByMime = mimeType === 'image/heic' || mimeType === 'image/heif';
    
    // Safari에서는 확장자 우선, 다른 브라우저에서는 MIME 타입도 고려
    if (isSafari()) {
      return isHeicByExtension;
    }
    
    return isHeicByMime || isHeicByExtension;
  };

  // 동적 heic2any import
  const loadHeic2any = async () => {
    if (typeof window === 'undefined') {
      throw new Error('HEIC conversion is only available in browser environment');
    }
    const heic2any = (await import('heic2any')).default;
    return heic2any;
  };

  // 특정 설정으로 HEIC 변환하는 헬퍼 함수
  const convertHeicWithSettings = async (file: File, quality: number, width?: number): Promise<File> => {
    const heic2any = await loadHeic2any();
    const supportsWebP = await checkWebPSupport();
    const targetFormat = supportsWebP && isSafari() ? 'image/webp' : 'image/jpeg';
    const fileExtension = targetFormat === 'image/webp' ? 'webp' : 'jpg';

    const conversionOptions: any = {
      blob: file,
      toType: targetFormat,
      quality: quality,
    };

    if (width) {
      conversionOptions.width = width;
    }

    const convertedBlob = await heic2any(conversionOptions) as Blob;
    const convertedFileName = file.name.replace(/\.(heic|heif)$/i, `.${fileExtension}`);
    
    return new File(
      [convertedBlob], 
      convertedFileName,
      { 
        type: targetFormat,
        lastModified: Date.now()
      }
    );
  };

  // HEIC 파일을 최적화된 포맷으로 변환하는 함수
  const convertHeicToJpeg = async (file: File): Promise<File> => {
    try {
      const heic2any = await loadHeic2any();
      
      // Safari 환경별 품질 최적화 - 파일 크기 초과 방지
      const quality = isMobileSafari() ? 0.15 : isSafari() ? 0.25 : isMobile() ? 0.35 : 0.7; // 더 적극적인 압축
      
      // 파일 크기에 따른 해상도 결정 (더 스마트한 변환)
      const fileSizeMB = file.size / 1024 / 1024;
      let targetWidth: number | undefined;
      
      if (fileSizeMB > 10) {
        targetWidth = 1080; // 10MB 이상 → FHD로 축소
      } else if (fileSizeMB > 5) {
        targetWidth = 1440; // 5MB 이상 → 1440p로 축소
      }
      // 5MB 이하는 해상도 유지

      // 변환 후 예상 크기 미리 계산
      const hasResizing = !!targetWidth;
      const estimatedSizeMB = estimateConvertedSize(fileSizeMB, quality, hasResizing);
      
      console.log(`HEIC 변환 설정: 품질=${quality}, 목표폭=${targetWidth || '원본'}, 원본크기=${fileSizeMB.toFixed(1)}MB, 예상크기=${estimatedSizeMB.toFixed(1)}MB`);

      // 예상 크기가 너무 클 경우 미리 경고하고 더 강한 설정 적용
      if (estimatedSizeMB > 15) {
        console.warn(`예상 변환 크기가 15MB 초과 (${estimatedSizeMB.toFixed(1)}MB), 더 강한 압축 적용`);
        
        // 더 강한 설정으로 변경
        const aggressiveQuality = Math.max(0.1, quality * 0.4);
        const aggressiveWidth = targetWidth ? Math.max(720, targetWidth * 0.6) : 720;
        
        console.log(`강화된 변환 설정: 품질=${aggressiveQuality}, 폭=${aggressiveWidth}`);
        
        return await convertHeicWithSettings(file, aggressiveQuality, aggressiveWidth);
      }

      // WebP 지원 여부 확인
      const supportsWebP = await checkWebPSupport();
      const targetFormat = supportsWebP && isSafari() ? 'image/webp' : 'image/jpeg';
      const fileExtension = targetFormat === 'image/webp' ? 'webp' : 'jpg';

      // 향상된 변환 설정
      const conversionOptions: any = {
        blob: file,
        toType: targetFormat,
        quality: quality,
      };

      // 해상도 축소가 필요한 경우
      if (targetWidth) {
        conversionOptions.width = targetWidth;
      }

      const convertedBlob = await heic2any(conversionOptions) as Blob;

      // 변환된 blob을 File 객체로 변환
      const convertedFileName = file.name.replace(/\.(heic|heif)$/i, `.${fileExtension}`);
      const convertedFile = new File(
        [convertedBlob], 
        convertedFileName,
        { 
          type: targetFormat,
          lastModified: Date.now()
        }
      );

      // 변환 후 크기 재검증
      if (convertedFile.size > 20 * 1024 * 1024) {
        console.warn(`변환된 파일이 20MB 초과: ${(convertedFile.size / 1024 / 1024).toFixed(2)}MB`);
        
        // Safari에서 더 낮은 품질로 재변환 시도
        if (isSafari() && quality > 0.1) {
          const lowerQuality = Math.max(0.1, quality - 0.1);
          console.log(`더 낮은 품질(${lowerQuality})로 재변환 시도...`);
          
          const retryOptions: any = {
            blob: file,
            toType: targetFormat,
            quality: lowerQuality,
          };
          
          // 해상도도 더 줄이기
          if (targetWidth) {
            retryOptions.width = Math.max(720, targetWidth - 360); // 더 작게
          } else {
            retryOptions.width = 720; // 강제로 720p
          }
          
          const retryBlob = await heic2any(retryOptions) as Blob;
          const retryFile = new File(
            [retryBlob], 
            convertedFileName,
            { 
              type: targetFormat,
              lastModified: Date.now()
            }
          );
          
          if (retryFile.size <= 20 * 1024 * 1024) {
            console.log(`재변환 성공: ${(retryFile.size / 1024 / 1024).toFixed(2)}MB`);
            return retryFile;
          }
        }
        
        throw new Error('변환된 파일이 크기 제한을 초과합니다.');
      }

      return convertedFile;
    } catch (error) {
      console.error('HEIC 변환 실패:', error);
      
      // Safari 전용 에러 처리
      if (isSafari()) {
        console.warn(`🍎 Safari HEIC 변환 실패 - 파일 크기나 형식 문제일 수 있음`);
        console.error('Safari HEIC Error Details:', {
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          fileName: file.name,
          fileSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
          fileType: file.type
        });
      }
      
      throw error;
    }
  };


  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;


    if (values.length + files.length > maxImages) {
      toast.error(`최대 ${maxImages}장까지 업로드 가능합니다.`);
      return;
    }

    const newFiles = Array.from(files);
    
    
    // HEIC 파일 병렬 변환 및 미리보기 URL 생성 최적화
    const heicFiles = newFiles.filter(isHeicFile);
    const nonHeicFiles = newFiles.filter(file => !isHeicFile(file));
    
    // HEIC 파일 ID들을 변환 상태로 설정
    const heicFileIds = heicFiles.map(createFileId);
    setConvertingIds(prev => new Set([...prev, ...heicFileIds]));
    
    console.log(`HEIC 병렬 변환 시작: ${heicFiles.length}개 파일`);
    
    // HEIC 파일들을 병렬로 변환
    const heicConversionPromises = heicFiles.map(async (file) => {
      const fileId = createFileId(file);
      
      try {
        const convertedFile = await convertHeicToJpeg(file);
        
        // Safari에서 변환된 파일 크기 검증
        if (isSafari() && convertedFile.size > 20 * 1024 * 1024) {
          console.warn('Safari: 변환된 파일이 여전히 20MB 초과');
          toast.error('변환된 이미지가 너무 큽니다. 다른 이미지를 선택해주세요.');
          return null; // 이 파일은 건너뛰기
        }
        
        const previewUrl = URL.createObjectURL(convertedFile);
        return {
          fileId,
          file: convertedFile, // 변환된 파일을 저장
          url: previewUrl,
          needsServerConversion: false,
        };
      } catch (error) {
        console.error('HEIC 변환 실패:', error);
        
        // Safari에서 변환 실패 시 서버 변환 fallback 시도
        if (isSafari()) {
          console.log('클라이언트 변환 실패, 서버 변환 fallback 시도...');
          
          // 서버에서 변환하도록 원본 HEIC 파일을 그대로 전송
          const previewUrl = URL.createObjectURL(file);
          return {
            fileId,
            file, // 원본 HEIC 파일을 서버로 전송
            url: previewUrl,
            needsServerConversion: true, // 서버 변환 필요 플래그
          };
        }
        
        console.warn('원본 HEIC 파일 사용 - 업로드 시 크기 문제 발생 가능');
        const previewUrl = URL.createObjectURL(file);
        return {
          fileId,
          file, // 변환 실패 시 원본 파일 사용
          url: previewUrl,
          needsServerConversion: false,
        };
      }
    });
    
    // 일반 이미지 파일들 즉시 처리
    const nonHeicImages = nonHeicFiles.map((file) => {
      const fileId = createFileId(file);
      const previewUrl = URL.createObjectURL(file);
      return {
        fileId,
        file,
        url: previewUrl,
        needsServerConversion: false,
      };
    });
    
    // HEIC 변환 완료 대기
    const heicImages = await Promise.all(heicConversionPromises);
    
    // 변환 상태 해제
    setConvertingIds(prev => {
      const newSet = new Set(prev);
      heicFileIds.forEach(id => newSet.delete(id));
      return newSet;
    });
    
    console.log(`HEIC 병렬 변환 완료: ${heicImages.filter(img => img !== null).length}/${heicFiles.length}개 성공`);
    
    // 모든 이미지 합치기
    const tempImages = [...nonHeicImages, ...heicImages];
    
    // null 값 필터링 (Safari에서 변환 실패한 파일들 제거)
    const validTempImages = tempImages.filter((img): img is NonNullable<typeof img> => img !== null) as FormImage[];
    
    if (validTempImages.length === 0) {
      toast.error('처리할 수 있는 이미지가 없습니다.');
      return;
    }
    
    if (validTempImages.length < tempImages.length) {
      const skippedCount = tempImages.length - validTempImages.length;
      toast.warning(`${skippedCount}개 파일을 건너뛰었습니다.`);
    }

    // 직접 업로드 전용 - 모든 파일을 Supabase에 직접 업로드
    const fileCount = validTempImages.length;
    console.log(`직접 업로드: ${fileCount}개 파일 처리`);
    
    // 전체 파일 크기 제한 검증 (직접 업로드에서는 더 관대하게)
    const totalSize = validTempImages.reduce((sum, img) => sum + (img.file?.size || 0), 0);
    const totalSizeMB = totalSize / 1024 / 1024;
    const maxTotalSizeMB = 500; // 500MB 총 제한
    
    if (totalSizeMB > maxTotalSizeMB) {
      toast.error(`총 파일 크기가 ${maxTotalSizeMB}MB를 초과합니다. (현재: ${totalSizeMB.toFixed(1)}MB)`);
      
      // 크기가 큰 파일들을 제거하여 제한 내로 맞추기
      const sortedImages = [...validTempImages].sort((a, b) => (a.file?.size || 0) - (b.file?.size || 0));
      const filteredImages: FormImage[] = [];
      let currentSize = 0;
      
      for (const img of sortedImages) {
        const imgSizeMB = (img.file?.size || 0) / 1024 / 1024;
        if (currentSize + imgSizeMB <= maxTotalSizeMB) {
          filteredImages.push(img);
          currentSize += imgSizeMB;
        }
      }
      
      if (filteredImages.length > 0) {
        const removedCount = validTempImages.length - filteredImages.length;
        toast.warning(`크기 제한으로 인해 ${removedCount}개 파일을 제외하고 ${filteredImages.length}개 파일만 업로드합니다.`);
        
        // 제외된 파일들의 blob URL 해제
        validTempImages.forEach(img => {
          if (!filteredImages.includes(img) && img.url) {
            URL.revokeObjectURL(img.url);
          }
        });
        
        // 유효한 파일들로 교체
        validTempImages.splice(0, validTempImages.length, ...filteredImages);
      } else {
        toast.error('업로드 가능한 파일이 없습니다. 파일 크기를 줄여주세요.');
        validTempImages.forEach(img => {
          if (img.url) URL.revokeObjectURL(img.url);
        });
        return;
      }
    }
    
    // 강사 타입이면 크롭을 위해 pending 상태로 저장, 아니면 바로 추가
    if (type === 'instructor') {
      // 강사 이미지는 크롭을 위해 pending 상태로 저장
      setPendingCropImages(validTempImages);
    } else {
      // 다른 타입은 바로 추가
      const updatedImages = [...values, ...validTempImages];
      onChange(updatedImages);
    }
    
    // 사용자에게 즉시 피드백
    if (validTempImages.length > 0) {
      toast.success(`${validTempImages.length}개 이미지가 추가되었습니다. 백그라운드에서 업로드를 시작합니다.`);
    }
    
    // 모든 파일을 업로딩 상태로 설정
    const newUploadingIds = new Set(validTempImages.map(img => img.fileId));
    setUploadingIds(prev => new Set([...prev, ...newUploadingIds]));

    try {
      // 업로드 시작
      setIsUploading(true);
      setUploadCancelled(false);
      setUploadProgress({ current: 0, total: validTempImages.length });
      
      console.log(`직접 업로드 시작: ${validTempImages.length}개 파일`);

      const successfulUploads: FormImage[] = [];
      const failedUploads: { fileName: string; error: string }[] = [];
      const finalImages = [...values];

      // 모든 파일을 직접 업로드로 순차 처리
      for (let i = 0; i < validTempImages.length; i++) {
        const img = validTempImages[i];
        
        if (uploadCancelled) {
          console.log('직접 업로드 취소됨');
          break;
        }

        if (!img.file) {
          failedUploads.push({ fileName: img.fileId, error: '파일이 없습니다' });
          continue;
        }

        setUploadProgress({ current: i + 1, total: validTempImages.length });
        setCurrentFileName(img.file.name);

        try {
          console.log(`직접 업로드 (${i + 1}/${validTempImages.length}): ${img.file.name} (${(img.file.size / 1024 / 1024).toFixed(1)}MB)`);

          // 1단계: signed URL 요청
          const tokenResponse = await fetch('/api/partner/upload/token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              fileName: img.file.name,
              fileSize: img.file.size,
              contentType: img.file.type,
              type: type,
              prefix: i === 0 ? 'featured' : 'gallery'
            }),
          });

          if (!tokenResponse.ok) {
            const errorText = await tokenResponse.text();
            throw new Error(`Token 생성 실패: ${tokenResponse.status} ${errorText}`);
          }

          const tokenData = await tokenResponse.json();
          if (!tokenData.success) {
            throw new Error(tokenData.error || 'Token 생성 실패');
          }

          console.log(`Signed URL 생성 완료: ${tokenData.filePath}`);

          // 2단계: 직접 Supabase에 업로드
          const uploadResponse = await fetch(tokenData.uploadUrl, {
            method: 'PUT',
            body: img.file,
            headers: {
              'Content-Type': img.file.type,
              'Cache-Control': 'max-age=3600',
            },
          });

          if (!uploadResponse.ok) {
            throw new Error(`직접 업로드 실패: ${uploadResponse.status} ${uploadResponse.statusText}`);
          }

          console.log(`직접 업로드 완료: ${img.file.name}`);

          // 3단계: 서버에 완료 알림
          const completeResponse = await fetch('/api/partner/upload/complete', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filePath: tokenData.filePath,
              publicUrl: tokenData.publicUrl,
              token: tokenData.token,
              metadata: tokenData.metadata,
              uploadSuccess: true
            }),
          });

          if (!completeResponse.ok) {
            const errorText = await completeResponse.text();
            throw new Error(`업로드 완료 처리 실패: ${completeResponse.status} ${errorText}`);
          }

          const completeData = await completeResponse.json();
          if (!completeData.success) {
            throw new Error(completeData.error || '업로드 완료 처리 실패');
          }

          // 성공 처리
          if (img.url) URL.revokeObjectURL(img.url);
          
          const successfulImage: FormImage = {
            fileId: img.fileId,
            url: tokenData.publicUrl,
            path: tokenData.filePath,
            file: undefined,
            needsServerConversion: false,
          };
          
          successfulUploads.push(successfulImage);
          finalImages.push(successfulImage);
          
          console.log(`직접 업로드 성공 (${i + 1}/${validTempImages.length}): ${img.file.name}`);

          // 강사 타입이면 pending 이미지 업데이트하고 첫 번째 이미지일 때 크롭 모달 표시
          if (type === 'instructor') {
            // pending 이미지를 업로드된 이미지로 업데이트
            setPendingCropImages(prev => 
              prev.map(pendingImg => 
                pendingImg.fileId === img.fileId 
                  ? { ...successfulImage, file: undefined }
                  : pendingImg
              )
            );

            // 첫 번째 이미지 업로드 완료시 크롭 모달 표시
            if (i === 0 && !cropModalOpen) {
              const uploadedImage = {
                ...successfulImage,
                file: undefined,
              };
              setSelectedImageForCrop(uploadedImage);
              setCropModalOpen(true);
            }
          }

        } catch (error) {
          console.error(`직접 업로드 실패 (${i + 1}/${validTempImages.length}):`, error);
          
          if (img.url) URL.revokeObjectURL(img.url);
          
          const errorMessage = error instanceof Error ? error.message : String(error);
          failedUploads.push({ fileName: img.file.name, error: errorMessage });
        }
      }

      // 최종 상태 업데이트 (강사 타입이 아닌 경우에만)
      if (type !== 'instructor') {
        onChange(finalImages);
      }

      // 업로드 결과 분석 및 사용자 피드백
      const successCount = successfulUploads.length;
      const failCount = failedUploads.length;
      
      if (uploadCancelled) {
        toast.warning(`업로드가 취소되었습니다. ${successCount}개 파일이 업로드되었습니다.`);
      } else if (successCount > 0 && failCount === 0) {
        toast.success(`${successCount}개 이미지가 성공적으로 업로드되었습니다.`);
      } else if (successCount > 0 && failCount > 0) {
        toast.warning(`${successCount}개 이미지 업로드 성공, ${failCount}개 실패했습니다.`);
        console.log('업로드 실패한 파일들:', failedUploads);
      } else {
        toast.error('모든 이미지 업로드에 실패했습니다.');
      }
      
    } catch (error) {
      console.error('이미지 업로드 실패:', error);
      
      // Safari별 에러 메시지 개선
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (errorMessage.includes('파일 크기 초과') || errorMessage.includes('413')) {
        if (isSafari()) {
          toast.error('Safari에서 파일 크기 제한을 초과했습니다. HEIC 파일은 더 작은 크기로 다시 시도해주세요.');
        } else {
          toast.error('파일 크기가 너무 큽니다. 더 작은 파일로 다시 시도해주세요.');
        }
      } else if (errorMessage.includes('HEIC') || errorMessage.includes('변환')) {
        toast.error('HEIC 파일 변환 중 오류가 발생했습니다. JPG나 PNG 파일을 사용해주세요.');
      } else if (errorMessage.includes('네트워크') || errorMessage.includes('Network')) {
        toast.error('네트워크 연결을 확인하고 다시 시도해주세요.');
      } else {
        toast.error('이미지 업로드에 실패했습니다. 잠시 후 다시 시도해주세요.');
      }
      
      // 실패 시 모든 미리보기 제거
      validTempImages.forEach(img => {
        if (img.url) URL.revokeObjectURL(img.url);
      });
      onChange(values); // 원래 상태로 복원
      
    } finally {
      // 업로딩 상태 모두 해제
      setIsUploading(false);
      setUploadProgress({ current: 0, total: 0 });
      setCurrentFileName('');
      setUploadingIds(prev => {
        const newSet = new Set(prev);
        validTempImages.forEach(img => {
          newSet.delete(img.fileId);
        });
        return newSet;
      });
      
      // 변환 상태도 모두 해제 (혹시 남아있을 경우를 위해)
      setConvertingIds(prev => {
        const newSet = new Set(prev);
        validTempImages.forEach(img => {
          newSet.delete(img.fileId);
        });
        return newSet;
      });
    }

    // 파일 입력 리셋
    e.target.value = '';
  };

  useEffect(() => {
    return () => {
      values
        .filter(image => image.url?.startsWith('blob'))
        .forEach(image => {
          URL.revokeObjectURL(image.url!);
        });
    };
  }, [values]);

  const removeImage = (fileId: string) => {
    const newValues = values.filter(image => image.fileId !== fileId);
    onChange(newValues);
  };

  const handleCropClick = (image: FormImage) => {
    setSelectedImageForCrop(image);
    setCropModalOpen(true);
  };

  const handleCropComplete = async (cropData: {
    x: number;
    y: number;
    width: number;
    height: number;
    unit: 'px' | '%';
  }) => {
    if (!selectedImageForCrop) return;

    let croppedImage = { ...selectedImageForCrop, cropArea: cropData };
    
    // 강사 타입인 경우에만 썸네일 생성 및 업로드
    if (type === 'instructor' && selectedImageForCrop.url && selectedImageForCrop.path) {
      try {
        toast.info('썸네일을 생성하고 있습니다...');
        
        // 1. 썸네일 생성
        const thumbnailBlob = await generateThumbnail(selectedImageForCrop.url, cropData, {
          size: 200,
          quality: 0.8,
          format: 'image/jpeg'
        });
        
        // 2. 썸네일 파일명 및 경로 생성
        const originalFileName = selectedImageForCrop.path.split('/').pop() || '';
        const thumbnailFileName = generateThumbnailFileName(originalFileName);
        const thumbnailPath = generateThumbnailPath(selectedImageForCrop.path);
        
        // 3. 썸네일 업로드를 위한 signed URL 요청
        const tokenResponse = await fetch('/api/partner/upload/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileName: thumbnailFileName,
            fileSize: thumbnailBlob.size,
            contentType: 'image/jpeg',
            type: type,
            prefix: 'thumbnail'
          }),
        });

        if (!tokenResponse.ok) {
          throw new Error('썸네일 업로드 토큰 생성 실패');
        }

        const tokenData = await tokenResponse.json();
        if (!tokenData.success) {
          throw new Error(tokenData.error || '썸네일 업로드 토큰 생성 실패');
        }

        // 4. 썸네일을 Supabase에 직접 업로드
        const uploadResponse = await fetch(tokenData.uploadUrl, {
          method: 'PUT',
          body: thumbnailBlob,
          headers: {
            'Content-Type': 'image/jpeg',
            'Cache-Control': 'max-age=3600',
          },
        });

        if (!uploadResponse.ok) {
          throw new Error('썸네일 업로드 실패');
        }

        // 5. 서버에 업로드 완료 알림
        const completeResponse = await fetch('/api/partner/upload/complete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            filePath: tokenData.filePath,
            publicUrl: tokenData.publicUrl,
            token: tokenData.token,
            metadata: tokenData.metadata,
            uploadSuccess: true
          }),
        });

        if (!completeResponse.ok) {
          throw new Error('썸네일 업로드 완료 처리 실패');
        }

        // 6. 썸네일 정보를 이미지에 추가
        croppedImage = {
          ...croppedImage,
          thumbnail: {
            url: tokenData.publicUrl,
            path: tokenData.filePath,
          }
        };
        
        toast.success('썸네일이 생성되었습니다!');
        
      } catch (error) {
        console.error('썸네일 생성/업로드 실패:', error);
        toast.error('썸네일 생성에 실패했습니다. 크롭 정보만 저장됩니다.');
      }
    }
    
    // 현재 이미지가 pending 상태인지 확인
    const isPendingImage = pendingCropImages.some(img => img.fileId === selectedImageForCrop.fileId);
    
    if (isPendingImage) {
      // pending 이미지라면 폼에 추가하고 pending에서 제거
      const updatedValues = [...values, croppedImage];
      const remainingPending = pendingCropImages.filter(img => img.fileId !== selectedImageForCrop.fileId);
      
      onChange(updatedValues);
      setPendingCropImages(remainingPending);
      
      // 다음 pending 이미지가 있다면 바로 크롭 모달 표시
      if (remainingPending.length > 0) {
        setSelectedImageForCrop(remainingPending[0]);
        // 모달은 계속 열린 상태 유지
      } else {
        setCropModalOpen(false);
        setSelectedImageForCrop(null);
        toast.success('썸네일 영역이 설정되었습니다.');
      }
    } else {
      // 기존 이미지의 크롭 수정
      const updatedValues = values.map(img => 
        img.fileId === selectedImageForCrop.fileId 
          ? croppedImage
          : img
      );
      
      onChange(updatedValues);
      setCropModalOpen(false);
      setSelectedImageForCrop(null);
      toast.success('썸네일 영역이 수정되었습니다.');
    }
  };

  const handleCropSkip = () => {
    if (!selectedImageForCrop) return;

    // 크롭 없이 그대로 추가
    const isPendingImage = pendingCropImages.some(img => img.fileId === selectedImageForCrop.fileId);
    
    if (isPendingImage) {
      const updatedValues = [...values, selectedImageForCrop];
      const remainingPending = pendingCropImages.filter(img => img.fileId !== selectedImageForCrop.fileId);
      
      onChange(updatedValues);
      setPendingCropImages(remainingPending);
      
      // 다음 pending 이미지가 있다면 바로 크롭 모달 표시
      if (remainingPending.length > 0) {
        setSelectedImageForCrop(remainingPending[0]);
      } else {
        setCropModalOpen(false);
        setSelectedImageForCrop(null);
      }
    } else {
      setCropModalOpen(false);
      setSelectedImageForCrop(null);
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'studio':
        return '센터 대표 사진';
      case 'instructor':
        return '강사 대표 사진';
      case 'class':
        return '운동 대표 사진';
      default:
        return '대표 사진';
    }
  };

  const getDescription = () => {
    switch (type) {
      case 'studio':
        return `센터 내부 공간을 보여주는 다양한 사진을 업로드하세요. (최대 ${maxImages}장, 50MB 이하, JPG/PNG/WEBP/HEIC)`;
      case 'instructor':
        return `강사님의 프로필 사진을 업로드해주세요. (최대 ${maxImages}장, 50MB 이하, JPG/PNG/WEBP/HEIC)`;
      case 'class':
        return '수업의 분위기나 운동 모습을 보여주는 사진을 업로드하세요. (50MB 이하, JPG/PNG/WEBP/HEIC)';
      default:
        return `사진을 업로드해주세요. (최대 ${maxImages}장, 50MB 이하, JPG/PNG/WEBP/HEIC)`;
    }
  };

  // 취소 함수
  const handleCancelUpload = () => {
    setUploadCancelled(true);
  };

  return (
    <div>
      <label className='text-sm font-medium'>
        {getTitle()}
        <span className='text-primary font-bold'>*</span>
      </label>
      <p className='text-muted-foreground mb-2 text-xs'>{getDescription()}</p>
      
      {/* 업로드 진행률 표시 - 개선된 UX */}
      {isUploading && (
        <div className='mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200'>
          <div className='flex items-center justify-between mb-3'>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-blue-500 rounded-full animate-pulse'></div>
              <span className='text-sm font-semibold text-blue-900'>
                {uploadProgress.current === uploadProgress.total 
                  ? '업로드 완료 중...' 
                  : `업로드 진행 중 ${uploadProgress.current}/${uploadProgress.total}`}
              </span>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCancelUpload}
              disabled={uploadCancelled}
              className="text-xs"
            >
              {uploadCancelled ? '취소 중...' : '취소'}
            </Button>
          </div>
          <Progress 
            value={(uploadProgress.current / uploadProgress.total) * 100} 
            className="mb-3 h-2" 
          />
          <div className='flex items-center justify-between text-xs'>
            <span className='text-blue-700'>
              {currentFileName || '업로드 준비 중...'}
            </span>
            <span className='text-blue-600 font-medium'>
              {Math.round((uploadProgress.current / uploadProgress.total) * 100)}%
            </span>
          </div>
        </div>
      )}
      
      <div className='grid grid-cols-3 gap-3'>
        {values.filter(img => img.url).map(img => {
          const isUploading = uploadingIds.has(img.fileId);
          const isConverting = convertingIds.has(img.fileId);
          const isProcessing = isUploading || isConverting;
          
          return (
            <div key={img.fileId} className='relative aspect-square'>
              <img
                src={img.url}
                alt={`Upload ${img.fileId}`}
                className={cn(
                  'h-full w-full rounded-lg object-cover',
                  isProcessing && 'opacity-50'
                )}
              />
              {isProcessing && (
                <div className='absolute inset-0 flex flex-col items-center justify-center bg-black/30 rounded-lg backdrop-blur-sm'>
                  <div className='bg-white/90 rounded-full p-2 mb-2'>
                    <Loader2 size={20} className='animate-spin text-blue-600' />
                  </div>
                  <span className='text-xs text-white font-medium bg-black/50 px-2 py-1 rounded'>
                    {isConverting ? '변환 중...' : '업로드 중...'}
                  </span>
                </div>
              )}
              
              {/* 강사 타입일 때만 크롭 버튼 표시 */}
              {type === 'instructor' && !isProcessing && img.path && (
                <button
                  type='button'
                  onClick={() => handleCropClick(img)}
                  className={cn(
                    'absolute bottom-2 left-2 flex h-7 w-7 items-center justify-center rounded-full bg-blue-500 text-xs text-white hover:bg-blue-600 transition-colors',
                    img.cropArea && 'bg-green-500 hover:bg-green-600'
                  )}
                  title='썸네일 영역 설정'
                >
                  <Crop size={12} />
                </button>
              )}
              
              <button
                type='button'
                onClick={() => removeImage(img.fileId)}
                disabled={isProcessing}
                className={cn(
                  'absolute -top-2 -right-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-xs text-white',
                  isProcessing && 'opacity-50 cursor-not-allowed'
                )}
              >
                <X size={12} />
              </button>
            </div>
          );
        })}
        {values.length < maxImages && !isUploading && (
          <label
            className={cn(
              // Base styles
              'group flex aspect-square cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed transition-all duration-200 ease-in-out',
              // Default state
              'border-gray-300 bg-gray-50/50',
              // Hover state
              'hover:border-primary hover:bg-primary/5 hover:scale-[1.02] hover:shadow-md',
              // Focus state
              'focus-within:border-primary focus-within:bg-primary/5 focus-within:scale-[1.02] focus-within:shadow-md'
            )}
          >
            <Camera
              size={24}
              className='group-hover:text-primary group-focus-within:text-primary mb-1 text-gray-400 transition-colors duration-200'
            />
            <span className='group-hover:text-primary group-focus-within:text-primary text-xs text-gray-500 transition-colors duration-200'>
              사진 추가
            </span>
            <input
              type='file'
              accept='image/jpeg,image/jpg,image/png,image/webp,image/heic'
              multiple
              onChange={handleImageUpload}
              className='hidden'
              aria-label='사진 파일을 선택하여 업로드하세요'
            />
          </label>
        )}
      </div>

      {/* 크롭 모달 */}
      {selectedImageForCrop && (
        <ImageCropModal
          isOpen={cropModalOpen}
          onClose={() => {
            setCropModalOpen(false);
            setSelectedImageForCrop(null);
            // pending 이미지가 있다면 모두 제거 (취소시)
            if (pendingCropImages.length > 0) {
              setPendingCropImages([]);
            }
          }}
          imageUrl={selectedImageForCrop.url!}
          onCropComplete={handleCropComplete}
          onSkip={handleCropSkip}
          initialCrop={selectedImageForCrop.cropArea}
        />
      )}
    </div>
  );
}
