'use client';
import StudioForm from '@/app/partner/_components/StudioForm';
import { partnerStudio<PERSON>pi } from '@/lib/api/partner/studio.api';
import { GetStudiosResponse } from '@/lib/api/partner/studio.schema';
import { uuid } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { toEditStudioFormDataToUpdateRequest } from '../_utils/transform';
import { EditStudioFormData } from '../_schemas/form.schema';

interface EditStudioClientProps {
  studioData: GetStudiosResponse[number];
}

export default function EditStudioClient({
  studioData,
}: EditStudioClientProps) {
  const router = useRouter();

  const handleSubmit = async (data: EditStudioFormData) => {
    const requestData = await toEditStudioFormDataToUpdateRequest(data, {
      prevAddress: studioData.address,
    });
    await partnerStudioApi.updateStudioById(studioData.id, requestData);
    router.refresh();
  };

  return (
    <StudioForm
      mode='edit'
      defaultValues={{
        ...studioData,
        images:
          studioData.images?.map(image => ({
            fileId: uuid(),
            url: image.url,
            path: image.path,
            file: undefined,
          })) || [],
        links: {
          website: studioData.links?.website || '',
          sns: studioData.links?.sns || '',
        },
      }}
      onSubmit={handleSubmit}
      submitButtonText='센터수정'
    />
  );
}
