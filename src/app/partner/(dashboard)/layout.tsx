import PartnerStoreHydrator from '@/app/partner/_components/PartnerStoreHydrator';
import { PartnerBottomNav } from '@/app/partner/_components/PartnerBottomNav';
import { serverGet } from '@/lib/api/server-fetch.server';
import type { GetStudiosResponse } from '@/lib/api/partner/studio.schema';
import { cn } from '@/lib/utils';

export default async function PartnerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const studios = await serverGet<GetStudiosResponse>('/api/partner/studios');

  const firstStudio =
    Array.isArray(studios) && studios.length > 0 ? studios[0] : null;

  const studioId = firstStudio?.id ?? null;
  const studioName = firstStudio?.name ?? null;

  return (
    <>
      <PartnerStoreHydrator initialData={{ studioId, studioName }} />
      <main className={cn('bg-white pb-20')}>{children}</main>
      <PartnerBottomNav />
    </>
  );
}
