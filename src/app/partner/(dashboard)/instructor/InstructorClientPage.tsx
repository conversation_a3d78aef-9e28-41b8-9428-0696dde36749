'use client';

import InstructorEmptyState from '@/components/partner/instructor/InstructorEmptyState';
import InstructorListItemComponent from '@/components/partner/instructor/InstructorListItem';
import { Button } from '@/components/ui/button';
import type { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { InstructorListItem } from '../../_types/client.type';

type Props = {
  initialInstructors: GetInstructorsResponse;
};

export default function InstructorClientPage({ initialInstructors }: Props) {
  const router = useRouter();
  const [instructors] = useState(initialInstructors);

  const buildEditHref = useMemo(
    () => (instructor: InstructorListItem) =>
      `/partner/instructor/edit/${instructor.id}`,
    []
  );

  const handleAddInstructor = () => {
    router.push('/partner/instructor/new');
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* 헤더 */}
      <div className='mb-8 flex items-center justify-between'>
        <div>
          <h1 className='mb-2 text-2xl font-bold text-gray-900'>소속 강사</h1>
          <p className='text-sm text-gray-600'>
            {instructors.length > 0
              ? `총 ${instructors.length}명의 강사가 등록되어 있습니다.`
              : '등록된 강사가 없습니다.'}
          </p>
        </div>

        <div className='flex items-center gap-3'>
          <Button
            onClick={handleAddInstructor}
            className='flex items-center gap-2'
          >
            <Plus className='h-4 w-4' />
            강사 등록
          </Button>
        </div>
      </div>

      {/* 콘텐츠 */}
      {instructors.length === 0 ? (
        <InstructorEmptyState onAddInstructor={handleAddInstructor} />
      ) : (
        <div className='space-y-4'>
          {instructors.map(instructor => (
            <InstructorListItemComponent
              key={instructor.id}
              instructor={{
                id: instructor.id,
                name: instructor.name,
                profileImageUrl: instructor.profileImages?.[0]?.url ?? '', // fallback for compatibility
                profileImages: instructor.profileImages || undefined,
              }}
              editHref={buildEditHref({
                id: instructor.id,
                name: instructor.name,
                profileImageUrl: instructor.profileImages?.[0]?.url ?? '',
              })}
            />
          ))}
        </div>
      )}
    </div>
  );
}
