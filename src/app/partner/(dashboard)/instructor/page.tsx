import InstructorClientPage from './InstructorClientPage';
import { serverGet } from '@/lib/api/server-fetch.server';
import {
  instructorApiSchema,
  INSTRUCTOR_API_ENDPOINTS,
} from '@/lib/api/partner/instructor.schema';
import InstructorPageSkeleton from '@/components/partner/instructor/InstructorPageSkeleton';
import { Suspense } from 'react';

export default async function InstructorPage() {
  const instructors = await serverGet(
    '/api/partner/instructors',
    {
      next: {
        tags: ['instructors'],
      },
    },
    instructorApiSchema[INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTORS].response
  );

  return (
    <Suspense fallback={<InstructorPageSkeleton />}>
      <InstructorClientPage initialInstructors={instructors} />
    </Suspense>
  );
}
