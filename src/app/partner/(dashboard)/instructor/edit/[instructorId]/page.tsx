import { Suspense } from 'react';
import EditInstructorPageContent, {
  EditInstructorPageSkeleton,
} from './EditInstructorPageContent';
import { serverGet } from '@/lib/api/server-fetch.server';
import {
  INSTRUCTOR_API_ENDPOINTS,
  instructorApiSchema,
} from '@/lib/api/partner/instructor.schema';

export default async function EditInstructorPage({
  params,
}: {
  params: Promise<{ instructorId: string }>;
}) {
  const { instructorId } = await params;
  const instructor = await serverGet(
    `/api/partner/instructors/${instructorId}`,
    {
      next: {
        tags: ['instructors', instructorId],
      },
    },
    instructorApiSchema[INSTRUCTOR_API_ENDPOINTS.GET_INSTRUCTOR_BY_ID].response
  );

  return (
    <Suspense fallback={<EditInstructorPageSkeleton />}>
      <EditInstructorPageContent instructor={instructor} />
    </Suspense>
  );
}
