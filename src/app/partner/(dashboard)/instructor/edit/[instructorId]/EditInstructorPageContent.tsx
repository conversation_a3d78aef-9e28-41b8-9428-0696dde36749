'use client';
import { InstructorFormData } from '@/app/partner/_schemas/form.schema';
import { toInstructorFormDataToRequestData } from '@/app/partner/_utils/transform';
import InstructorForm from '@/components/partner/instructor/InstructorForm';
import { Skeleton } from '@/components/ui/skeleton';
import { instructorApi } from '@/lib/api/partner/instructor.api';
import { InstructorResponse } from '@/lib/api/partner/instructor.schema';
import { useRouter } from 'next/navigation';
import { Suspense, useState } from 'react';
import { toast } from 'sonner';
interface EditInstructorPageContentProps {
  instructor: InstructorResponse;
}

export default function EditInstructorPageContent({
  instructor,
}: EditInstructorPageContentProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (data: InstructorFormData) => {
    try {
      setIsLoading(true);

      const requestData = await toInstructorFormDataToRequestData(data);
      await instructorApi.updateInstructor(instructor.id, requestData);

      toast.success('강사 정보가 성공적으로 수정되었습니다.');
      router.push('/partner/instructor');
    } catch (error) {
      console.error('강사 수정 실패:', error);
      toast.error('강사 수정에 실패했습니다.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Suspense fallback={<EditInstructorPageSkeleton />}>
      <InstructorForm
        mode='edit'
        initialData={instructor}
        onSubmit={handleSubmit}
        isLoading={isLoading}
      />
    </Suspense>
  );
}

export function EditInstructorPageSkeleton() {
  return (
    <div className='p-3'>
      <div className='mb-4'>
        <Skeleton className='h-7 w-32' />
      </div>

      <div className='flex flex-col gap-6'>
        {/* 기본 정보 스켈레톤 */}
        <div>
          <Skeleton className='mb-4 h-6 w-20' />
          <div className='flex flex-col gap-4'>
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className='space-y-2'>
                <Skeleton className='h-4 w-24' />
                <Skeleton className='h-10 w-full' />
              </div>
            ))}
          </div>
        </div>

        {/* 경력 사항 스켈레톤 */}
        <div>
          <Skeleton className='mb-4 h-6 w-20' />
          <div className='flex flex-col gap-4'>
            <div className='space-y-2'>
              <Skeleton className='h-4 w-16' />
              <Skeleton className='h-10 w-full' />
            </div>
            <Skeleton className='h-32 w-full' />
          </div>
        </div>

        {/* 자격 사항 스켈레톤 */}
        <div>
          <Skeleton className='mb-4 h-6 w-20' />
          <Skeleton className='h-24 w-full' />
        </div>

        {/* 버튼 스켈레톤 */}
        <div className='pt-6'>
          <Skeleton className='h-12 w-full' />
        </div>
      </div>
    </div>
  );
}
