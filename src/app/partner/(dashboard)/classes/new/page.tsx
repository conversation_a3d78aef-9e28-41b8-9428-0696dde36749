import PartnerClassForm from '@/components/partner/class/PartnerClassForm';
import { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';
import { getMyStudiosServer } from '@/lib/api/partner/studio.api.server';
import { serverGet } from '@/lib/api/server-fetch.server';
import NoStudioDialog from './_components/NoStudioDialog';

export default async function NewClassPage() {
  const studios = await getMyStudiosServer();
  const hasStudio = Array.isArray(studios) && studios.length > 0;
  const instructors = await serverGet<GetInstructorsResponse>(
    '/api/partner/instructors'
  );

  return (
    <>
      <NoStudioDialog hasStudio={hasStudio} />
      {hasStudio ? (
        <PartnerClassForm
          mode='create'
          instructors={instructors}
          studioId={studios[0].id}
          studioName={studios[0].name}
        />
      ) : null}
    </>
  );
}
