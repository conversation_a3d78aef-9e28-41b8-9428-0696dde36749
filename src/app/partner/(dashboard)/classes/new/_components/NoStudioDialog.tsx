'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface NoStudioDialogProps {
  hasStudio: boolean;
  isPending?: boolean;
}

export default function NoStudioDialog({ hasStudio }: NoStudioDialogProps) {
  const router = useRouter();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    setOpen(!hasStudio);
  }, [hasStudio]);

  return (
    <AlertDialog open={open} onOpenChange={setOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>센터 등록이 필요해요</AlertDialogTitle>
          <AlertDialogDescription>
            클래스를 생성하려면 먼저 센터를 등록해야 합니다.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <>
            <AlertDialogCancel onClick={() => router.back()}>
              돌아가기
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => router.push('/partner/studios/new')}
            >
              센터 등록하러가기
            </AlertDialogAction>
          </>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
