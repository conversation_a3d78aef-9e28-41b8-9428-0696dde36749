'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import PartnerClassCard from '../../_components/PartnerClassCard';
import type { ClassListItem } from '../../_types/client.type';
import { classApi } from '@/lib/api/partner/class.api';

interface ClassesClientProps {
  items: ClassListItem[];
}

export default function ClassesClient({ items }: ClassesClientProps) {
  const router = useRouter();
  const [classList, setClassList] = useState<ClassListItem[]>(items);

  if (!classList || classList.length === 0) return null;

  const handleViewDetails = (classId: string) => {
    router.push(`/partner/classes/${classId}`);
  };

  const handleEdit = (classId: string) => {
    router.push(`/partner/classes/${classId}/edit`);
  };

  const handleActiveChange = async (classId: string, visible: boolean) => {
    // 낙관적 업데이트: 즉시 UI 상태 변경
    const previousItem = classList.find(item => item.id === classId);
    const previousState = previousItem?.visible ?? null;
    
    setClassList(prev => 
      prev.map(item => 
        item.id === classId ? { ...item, visible } : item
      )
    );

    try {
      // API 호출
      await classApi.patchClass(classId, { visible });
    } catch (error) {
      console.error('클래스 상태 변경 실패:', error);
      
      // 에러 발생 시 이전 상태로 롤백
      setClassList(prev => 
        prev.map(item => 
          item.id === classId ? { ...item, visible: previousState } : item
        )
      );
      
      // 사용자에게 에러 알림 (실제 프로젝트에서는 toast 등 사용)
      alert('상태 변경에 실패했습니다. 다시 시도해주세요.');
    }
  };

  return (
    <div className='space-y-4'>
      {classList.map(item => (
        <PartnerClassCard
          key={item.id}
          item={item}
          onViewDetails={() => handleViewDetails(item.id)}
          onEdit={() => handleEdit(item.id)}
          onActiveChange={active => handleActiveChange(item.id, active)}
        />
      ))}
    </div>
  );
}
