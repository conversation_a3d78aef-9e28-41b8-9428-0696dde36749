import ClassesClient from '@/app/partner/(dashboard)/classes/ClassesClient';
import { But<PERSON> } from '@/components/ui/button';
import { getClassesServer } from '@/lib/api/partner/class.api.server';
import { type ClassListItemResponse } from '@/lib/api/partner/class.schema';
import { CLASS_CATEGORIES } from '@/lib/db/schema';
import Link from 'next/link';
import { ClassListItem } from '../../_types/client.type';

// 서버 렌더 경로에서는 스켈레톤을 사용하지 않음

// 데이터가 없을 때 표시할 컴포넌트
function EmptyClassList() {
  return (
    <div className='flex flex-col items-center justify-center rounded-lg border-2 border-dashed px-4 py-12 text-center'>
      <h2 className='text-xl font-semibold text-gray-800'>
        아직 등록된 수업이 없어요
      </h2>
      <p className='mt-2 mb-6 text-gray-500'>
        새로운 수업을 개설하고 수강생을 모집해보세요!
      </p>
      <Button asChild>
        <Link href='/partner/classes/new'>수업 개설하기</Link>
      </Button>
    </div>
  );
}

const toClassListItem = (item: ClassListItemResponse): ClassListItem => {
  const categoryInfo = CLASS_CATEGORIES.find(cat => cat.code === item.category);
  const categoryTitle = categoryInfo?.title || item.category;

  return {
    id: item.id,
    title: item.title,
    level: item.level,
    scheduleGroupCount: item.scheduleGroups.length,
    sessionsPerWeek: item.sessionsPerWeek,
    durationWeeks: item.durationWeeks,
    pricePerSession: item.pricePerSession,
    maxParticipants: item.maxParticipants,
    category: categoryTitle,
    visible: item.visible,
    status: item.status,
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
    studioName: item.studioName,
    instructorName: item.instructorName,
    enrollmentCount: item.enrollmentCount,
  };
};

export default async function PartnerClassesPage() {
  const response = await getClassesServer({
    page: 1,
    limit: 10,
  });
  const classItems = response.data.map(toClassListItem);
  return (
    <div className='p-4'>
      <div className='mb-6 flex justify-between'>
        <h1 className='text-foreground text-xl font-bold'>수업 관리</h1>
        <Button size='sm' asChild>
          <Link href='/partner/classes/new'>수업 개설</Link>
        </Button>
      </div>

      {classItems.length === 0 ? (
        <EmptyClassList />
      ) : (
        <ClassesClient items={classItems} />
      )}
    </div>
  );
}
