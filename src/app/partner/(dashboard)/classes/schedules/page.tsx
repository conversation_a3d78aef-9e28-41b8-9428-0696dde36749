import { GetScheduleGroupsResponse } from '@/lib/api/partner/partner.schema';
import { serverGet } from '@/lib/api/server-fetch.server';
import ScheduleGroupsClient from './components/ScheduleGroupsClient';

export default async function PartnerClassesStatusPage() {
  const initialFilters = {
    activeStatusTab: 'recruiting' as const,
    page: 1,
    limit: 10,
  };

  const initialScheduleGroups = await serverGet<GetScheduleGroupsResponse>(
    '/api/partner/schedule-groups',
    {
      searchParams: {
        status: 'recruiting',
        page: initialFilters.page,
        limit: initialFilters.limit,
      },
      next: {
        tags: ['schedule-groups', 'recruiting'],
      },
    }
  );

  return (
    <>
      <ScheduleGroupsClient
        initialFilters={initialFilters}
        initialScheduleGroups={initialScheduleGroups}
      />
    </>
  );
}
