import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CalendarDays, Calendar as CalendarIcon } from 'lucide-react';

interface StartDateConfirmProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (startDate: string) => void;
  isLoading?: boolean;
}

export default function StartDateConfirm({
  open,
  onOpenChange,
  onConfirm,
  isLoading = false,
}: StartDateConfirmProps) {
  const [startDate, setStartDate] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [error, setError] = useState('');
  const [calendarOpen, setCalendarOpen] = useState(false);

  const validateDateString = (dateString: string) => {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) {
      return 'YYYY-MM-DD 형식으로 입력해주세요';
    }

    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isNaN(date.getTime())) {
      return '올바른 날짜를 입력해주세요';
    }

    const selectedDateObj = new Date(date);
    selectedDateObj.setHours(0, 0, 0, 0);

    if (selectedDateObj < today) {
      return '오늘 이후의 날짜를 입력해주세요';
    }

    return '';
  };

  const handleInputChange = (value: string) => {
    setStartDate(value);
    const errorMessage = validateDateString(value);
    setError(errorMessage);
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      const formattedDate = date.toISOString().split('T')[0];
      setStartDate(formattedDate);
      setSelectedDate(date);
      setError('');
      setCalendarOpen(false);
    }
  };

  const handleInputClick = () => {
    setCalendarOpen(true);
  };

  const handleConfirm = () => {
    const errorMessage = validateDateString(startDate);
    if (errorMessage) {
      setError(errorMessage);
      return;
    }

    onConfirm(startDate);
  };

  const handleClose = () => {
    setStartDate('');
    setSelectedDate(undefined);
    setError('');
    setCalendarOpen(false);
    onOpenChange(false);
  };

  // 과거 날짜 비활성화를 위한 함수
  const isDateDisabled = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const checkDate = new Date(date);
    checkDate.setHours(0, 0, 0, 0);

    return checkDate < today;
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <CalendarDays className='h-5 w-5' />
            수업 확정
          </DialogTitle>
          <DialogDescription>
            수업을 시작할 날짜를 선택해주세요. 해당 날짜부터 수업이 진행됩니다.
          </DialogDescription>
        </DialogHeader>

        <div className='grid gap-4 py-4'>
          <div className='grid gap-2'>
            <Label htmlFor='startDate' className='text-sm font-medium'>
              수업 시작 날짜
            </Label>
            <div className='relative'>
              <CalendarIcon className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400' />
              <Input
                id='startDate'
                type='text'
                placeholder='예: 2025-08-10'
                value={startDate}
                onChange={e => handleInputChange(e.target.value)}
                onClick={handleInputClick}
                className={`cursor-pointer pl-10 ${error ? 'border-red-500' : ''}`}
                disabled={isLoading}
                readOnly
              />
            </div>
            {error && <p className='text-sm text-red-500'>{error}</p>}
            <p className='text-xs text-gray-500'>
              날짜를 선택하려면 입력 필드를 클릭하세요
            </p>
          </div>
        </div>

        <DialogFooter className='flex justify-between'>
          <Button
            type='button'
            className='flex-1'
            onClick={handleConfirm}
            disabled={!startDate || !!error || isLoading}
          >
            {isLoading ? '확정 중...' : '수업 확정'}
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* 캘린더 전용 다이얼로그 */}
      <Dialog open={calendarOpen} onOpenChange={setCalendarOpen}>
        <DialogContent className='sm:max-w-[400px]'>
          <DialogHeader>
            <DialogTitle className='flex items-center gap-2'>
              <CalendarIcon className='h-5 w-5' />
              날짜 선택
            </DialogTitle>
            <DialogDescription>
              수업을 시작할 날짜를 선택해주세요.
            </DialogDescription>
          </DialogHeader>

          <div className='flex justify-center py-4'>
            <Calendar
              mode='single'
              selected={selectedDate}
              onSelect={handleDateSelect}
              disabled={isDateDisabled}
              className='rounded-md border'
              initialFocus
            />
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
