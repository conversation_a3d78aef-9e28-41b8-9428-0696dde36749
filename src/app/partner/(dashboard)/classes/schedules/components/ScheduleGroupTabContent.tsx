import { classApi } from '@/lib/api/partner/class.api';
import type { ScheduleGroupListItem } from '@/lib/types/schedule-groups.types';
import { PersonStanding, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import StartDateConfirm from './StartDateConfirm';
import ScheduleGroupItem from './ScheduleGroupItem';
import { ClassStatusSkeletonList } from './ClassStatusCardSkeleton';

export default function ScheduleGroupTabContent({
  scheduleGroups,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  refetch,
}: {
  scheduleGroups: ScheduleGroupListItem[];
  hasNextPage?: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => void;
  refetch: () => void;
}) {
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    classId?: string;
    scheduleGroupId?: number;
  }>({ open: false });

  const [isConfirming, setIsConfirming] = useState(false);

  const handleConfirmClick = (classId: string, scheduleGroupId: number) => {
    setConfirmDialog({ open: true, classId, scheduleGroupId });
  };

  const handleConfirmSchedule = async (startDate: string) => {
    if (!confirmDialog.classId || !confirmDialog.scheduleGroupId) return;
    setIsConfirming(true);
    try {
      await classApi.confirmSchedule(
        confirmDialog.classId,
        confirmDialog.scheduleGroupId,
        { startDate }
      );
      toast.success('수업이 성공적으로 확정되었습니다');
      setConfirmDialog({ open: false });
      refetch();
    } catch (error) {
      console.error('Schedule confirmation error:', error);
      toast.error('수업 확정에 실패했습니다', {
        description:
          error instanceof Error
            ? error.message
            : '알 수 없는 오류가 발생했습니다',
      });
    } finally {
      setIsConfirming(false);
    }
  };

  const handleLoadMore = () => {
    if (!isFetchingNextPage && hasNextPage) {
      fetchNextPage();
    }
  };

  return (
    <div className='flex-1'>
      {scheduleGroups.length > 0 ? (
        <div className='space-y-4 bg-white'>
          {scheduleGroups.map(group => (
            <ScheduleGroupItem
              key={group.id}
              group={group}
              onClickConfirm={() => handleConfirmClick(group.classId, group.id)}
            />
          ))}

          {/* 더보기 버튼 또는 로딩 스켈레톤 */}
          {isFetchingNextPage ? (
            <div className='py-4'>
              <ClassStatusSkeletonList count={3} />
            </div>
          ) : hasNextPage ? (
            <div className='flex justify-center py-6'>
              <Button
                onClick={handleLoadMore}
                variant='outline'
                className='min-w-32'
                disabled={isFetchingNextPage}
              >
                {isFetchingNextPage ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    로딩 중...
                  </>
                ) : (
                  '더보기'
                )}
              </Button>
            </div>
          ) : null}
        </div>
      ) : (
        <div className='py-12 text-center'>
          <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-200'>
            <PersonStanding className='h-8 w-8 text-gray-400' />
          </div>
          <h3 className='mb-2 text-lg font-medium text-gray-900'>
            클래스가 없습니다
          </h3>
        </div>
      )}

      <StartDateConfirm
        open={confirmDialog.open}
        onOpenChange={open => setConfirmDialog({ open })}
        onConfirm={handleConfirmSchedule}
        isLoading={isConfirming}
      />
    </div>
  );
}
