import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface CancelClassDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (cancelReason: string) => void;
  isLoading?: boolean;
  className: string;
}

export default function CancelClassDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  className,
}: CancelClassDialogProps) {
  const [cancelReason, setCancelReason] = useState('');

  const handleSubmit = () => {
    if (!cancelReason.trim()) {
      return;
    }
    onSubmit(cancelReason.trim());
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isLoading) {
      onOpenChange(newOpen);
      if (!newOpen) {
        setCancelReason('');
      }
    }
  };

  const isValid = cancelReason.trim().length > 0 && cancelReason.length <= 200;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>수업 취소</DialogTitle>
          <DialogDescription>
            <strong>{className}</strong> 수업을 취소하시겠습니까?
            <br />
            취소 사유를 입력해 주세요.
          </DialogDescription>
        </DialogHeader>
        <div className='grid gap-4 py-4'>
          <div className='grid gap-2'>
            <Label htmlFor='cancelReason'>취소 사유</Label>
            <Textarea
              id='cancelReason'
              placeholder='취소 사유를 입력해 주세요 (최대 200자)'
              value={cancelReason}
              onChange={e => setCancelReason(e.target.value)}
              disabled={isLoading}
              className='min-h-[80px]'
              maxLength={200}
            />
            <div className='text-right text-sm text-gray-500'>
              {cancelReason.length}/200
            </div>
          </div>
        </div>
        <div className='flex justify-end gap-2'>
          <Button
            className='flex-1'
            type='button'
            variant='outline'
            onClick={() => handleOpenChange(false)}
            disabled={isLoading}
          >
            취소
          </Button>
          <Button
            type='button'
            onClick={handleSubmit}
            disabled={!isValid || isLoading}
            className='bg-destructive hover:bg-destructive/90 flex-1'
          >
            {isLoading ? '취소 중...' : '수업 취소'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
