import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useScheduleGroupEnrollments } from '@/lib/hooks/useScheduleGroupEnrollments';
import { mapGender } from '@/lib/utils/format';

type ApplicantsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scheduleGroupId: number;
};

export default function ApplicantsDialog({
  open,
  onOpenChange,
  scheduleGroupId,
}: ApplicantsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <ApplicantDialogContent
        scheduleGroupId={scheduleGroupId}
        enabled={open}
      />
    </Dialog>
  );
}

function ApplicantDialogContent({
  scheduleGroupId,
  enabled,
}: {
  scheduleGroupId: number;
  enabled: boolean;
}) {
  const { data } = useScheduleGroupEnrollments(scheduleGroupId, enabled, {
    sortBy: 'enrollmentOrder',
    sortOrder: 'asc',
  });

  if (!data) {
    return null;
  }

  if (!data.success) {
    return (
      <DialogContent>
        <DialogHeader>
          <DialogTitle>신청자 정보</DialogTitle>
        </DialogHeader>
        <div className='text-red-600'>
          신청자 정보를 불러오지 못했습니다: {data.message}
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button>닫기</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    );
  }

  const { enrollments } = data.data;

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>신청자 정보 상세</DialogTitle>
      </DialogHeader>
      <div className='flex flex-col gap-4'>
        {enrollments.length === 0 && (
          <div className='text-sm text-gray-600'>신청자가 없습니다.</div>
        )}
        {enrollments.map(item => (
          <div key={item.id} className='rounded-md border bg-gray-100 p-2'>
            <div className='mb-2 text-base font-semibold'>{item.name}</div>
            <div className='flex items-center justify-between'>
              <div className='font-semibold text-black'>{item.nickname}</div>
              <div className='text-primary text-xs'>
                {item.gender && mapGender(item.gender)}
              </div>
            </div>
          </div>
        ))}
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button>닫기</Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  );
}
