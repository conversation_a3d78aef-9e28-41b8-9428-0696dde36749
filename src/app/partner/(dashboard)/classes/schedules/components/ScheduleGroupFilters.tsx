import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { X } from 'lucide-react';
import { ScheduleFiltersState } from '@/lib/hooks/useScheduleFilters';
import { useMyInstructors } from '@/lib/hooks/useMyInstructors';
import { useCategories } from '@/lib/hooks/useCategories';
import { CLASS_CATEGORIES } from '@/lib/db/schema';

interface ScheduleGroupFiltersProps {
  filters: ScheduleFiltersState;
  setInstructorId: (instructorId: string | undefined) => void;
  setCategory: (category: string | undefined) => void;
  clearAllFilters: () => void;
}

export default function ScheduleGroupFilters({
  filters,
  setInstructorId,
  setCategory,
  clearAllFilters,
}: ScheduleGroupFiltersProps) {
  const { data: instructorsData } = useMyInstructors();
  const { data: categoriesData } = useCategories();

  const instructorId = filters.instructorId;
  const category = filters.category;
  const instructors = instructorsData || [];
  const categories = categoriesData || CLASS_CATEGORIES;

  const handleInstructorSelect = (instructorIdValue: string) => {
    if (instructorIdValue === 'all') {
      setInstructorId(undefined);
    } else {
      setInstructorId(instructorIdValue);
    }
  };

  const handleCategorySelect = (categoryValue: string) => {
    if (categoryValue === 'all') {
      setCategory(undefined);
    } else {
      setCategory(categoryValue);
    }
  };

  const hasActiveFilters = !!(
    (instructorId && instructorId.length > 0) ||
    (category && category.length > 0)
  );

  return (
    <div className='flex flex-col gap-3 pb-3'>
      <div className='flex items-center gap-3'>
        <div className='space-y-2'>
          <Select
            value={
              instructorId && instructorId.length > 0 ? instructorId[0] : 'all'
            }
            onValueChange={handleInstructorSelect}
          >
            <SelectTrigger>
              <SelectValue>
                {instructorId && instructorId.length > 0
                  ? instructors.find(i => i.id === instructorId[0])?.name ||
                    '선택된 강사'
                  : '강사'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>전체 강사</SelectItem>
              {instructors.map(instructor => (
                <SelectItem key={instructor.id} value={instructor.id}>
                  {instructor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className='space-y-2'>
          <Select
            value={category && category.length > 0 ? category[0] : 'all'}
            onValueChange={handleCategorySelect}
          >
            <SelectTrigger>
              <SelectValue>
                {category && category.length > 0
                  ? (() => {
                      const cat = categories.find(c => c.code === category[0]);
                      return cat ? (
                        <span className='flex items-center gap-2'>
                          {cat.emoji && <span>{cat.emoji}</span>}
                          {cat.title}
                        </span>
                      ) : (
                        '선택된 카테고리'
                      );
                    })()
                  : '수업 유형'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>전체 카테고리</SelectItem>
              {categories.map(cat => (
                <SelectItem key={cat.code} value={cat.code}>
                  <span className='flex items-center gap-2'>
                    {cat.emoji && <span>{cat.emoji}</span>}
                    {cat.title}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className='flex items-center justify-between'>
        {hasActiveFilters && (
          <div className='flex flex-wrap gap-2'>
            {instructorId?.map(id => {
              const instructor = instructors.find(i => i.id === id);
              return instructor ? (
                <Badge key={id} variant='outline' className='text-xs'>
                  강사: {instructor.name}
                </Badge>
              ) : null;
            })}
            {category?.map(c => {
              const cat = categories.find(cat => cat.code === c);
              return cat ? (
                <Badge key={c} variant='outline' className='text-xs'>
                  카테고리: {cat.title}
                </Badge>
              ) : null;
            })}
          </div>
        )}

        {hasActiveFilters && (
          <div className='flex justify-end'>
            <Button
              variant='ghost'
              size='sm'
              onClick={clearAllFilters}
              className='text-gray-500 hover:text-gray-700'
            >
              <X className='mr-1 h-3 w-3' />
              필터 초기화
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export function ScheduleGroupFiltersSkeleton() {
  return (
    <div className='space-y-4 border-b bg-white p-4'>
      <div className='space-y-2'>
        <div className='h-4 w-8 animate-pulse rounded bg-gray-200' />
        <div className='flex flex-wrap gap-2'>
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              className='h-6 w-12 animate-pulse rounded bg-gray-200'
            />
          ))}
        </div>
      </div>
      <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
        <div className='space-y-2'>
          <div className='h-4 w-8 animate-pulse rounded bg-gray-200' />
          <div className='h-10 w-full animate-pulse rounded bg-gray-200' />
        </div>
        <div className='space-y-2'>
          <div className='h-4 w-16 animate-pulse rounded bg-gray-200' />
          <div className='h-10 w-full animate-pulse rounded bg-gray-200' />
        </div>
      </div>
    </div>
  );
}
