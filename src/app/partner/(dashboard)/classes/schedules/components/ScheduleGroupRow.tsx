import { Button } from '@/components/ui/button';
import { mapDayOfWeek } from '@/lib/utils/format';

type ScheduleGroupRowProps = {
  group: {
    id: number;
    classId: string;
    className: string;
    instructor: { name: string };
    schedules: { dayOfWeek: string; startTime: string; endTime: string }[];
    maxParticipants: number;
    confirmedCount: number;
    status: 'recruiting' | 'ongoing' | 'ended';
  };
  onClickConfirm: () => void;
  onClickApplicants: () => void;
  onClickDetail: () => void;
  onClickCancel: () => void;
  onClickOpenChat?: () => void;
};

const mapGroupStatus = (
  status: 'recruiting' | 'ongoing' | 'ended',
  confirmedCount: number,
  maxParticipants: number
) => {
  if (status === 'recruiting')
    return `${confirmedCount}명 / ${maxParticipants}명`;
  if (status === 'ongoing') return '진행중';
  return '종료';
};

export default function ScheduleGroupRow({
  group,
  onClickConfirm,
  onClickApplicants,
  onClickDetail,
  onClickCancel,
  onClickOpenChat,
}: ScheduleGroupRowProps) {
  const isRecruiting = group.status === 'recruiting';
  const isOngoing = group.status === 'ongoing';
  const canConfirm = isRecruiting && group.confirmedCount > 0;

  return (
    <div className='flex flex-col gap-3 rounded-md border p-3'>
      <div className='flex flex-col gap-1'>
        <div className='flex items-center justify-between'>
          <div className='text-sm text-gray-700'>
            {group.instructor.name} 코치님
          </div>
          <div className='bg-primary rounded px-2 py-1 text-xs text-white'>
            {mapGroupStatus(
              group.status,
              group.confirmedCount,
              group.maxParticipants
            )}
          </div>
        </div>
        <div className='text-lg font-semibold text-black'>
          {group.className}
        </div>
      </div>
      <div className='flex flex-col gap-2 rounded-md border bg-neutral-50 px-4 py-3'>
        {group.schedules.map(s => (
          <div
            key={`${s.dayOfWeek}-${s.startTime}`}
            className='flex items-center justify-between'
          >
            <div className='text-sm text-gray-700'>
              {mapDayOfWeek(s.dayOfWeek)}요일
            </div>
            <div className='text-sm text-gray-900'>
              {s.startTime} ~ {s.endTime}
            </div>
          </div>
        ))}
      </div>

      <div className='flex items-center gap-2'>
        {isRecruiting && (
          <>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickCancel}
            >
              수업 취소
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickApplicants}
            >
              신청자 정보 보기
            </Button>
            <Button
              variant='primaryDark'
              className='flex-1'
              onClick={onClickConfirm}
              disabled={!canConfirm}
            >
              수업 확정
            </Button>
          </>
        )}
        
        {isOngoing && (
          <>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickCancel}
            >
              수업 취소
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickApplicants}
            >
              신청자 정보 보기
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickOpenChat}
            >
              오픈 채팅 링크 공유
            </Button>
          </>
        )}
        
        {!isRecruiting && !isOngoing && (
          <Button
            variant='outline'
            className='flex-1'
            onClick={onClickDetail}
          >
            수업 상세 보기
          </Button>
        )}
      </div>
    </div>
  );
}
