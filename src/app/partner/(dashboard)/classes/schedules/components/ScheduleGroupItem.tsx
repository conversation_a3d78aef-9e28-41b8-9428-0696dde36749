import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { useMutation } from '@tanstack/react-query';
import ApplicantsDialog from './ApplicantsDialog';
import OpenChatDialog from './OpenChatDialog';
import CancelClassDialog from './CancelClassDialog';
import { Button } from '@/components/ui/button';
import { mapDayOfWeek } from '@/lib/utils/format';
import { classApi } from '@/lib/api/partner/class.api';
import type { CancelClassRequest } from '@/lib/validations/class-cancellation.validation';

export default function ScheduleGroupItem({
  group,
  onClickConfirm,
}: {
  group: {
    id: number;
    classId: string;
    className: string;
    instructor: { name: string };
    schedules: { dayOfWeek: string; startTime: string; endTime: string }[];
    maxParticipants: number;
    confirmedCount: number;
    applicantCount: number;
    status: 'recruiting' | 'ongoing' | 'ended';
  };
  onClickConfirm: () => void;
}) {
  const router = useRouter();
  const [applicationsDialogOpen, setApplicationsDialogOpen] = useState(false);
  const [openChatDialogOpen, setOpenChatDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);

  const { mutate: cancelClass, isPending: isCancelling } = useMutation({
    mutationFn: (request: CancelClassRequest) =>
      classApi.cancelClass(group.classId, group.id, request),
    onSuccess: response => {
      toast.success(
        `수업이 취소되었습니다. ${response.data.refundedCount}명에게 환불 처리됩니다.`
      );
      setCancelDialogOpen(false);
      router.refresh();
    },
    onError: (error: unknown) => {
      let message = '수업 취소 처리 중 오류가 발생했습니다.';
      if (error && typeof error === 'object') {
        const e = error as {
          data?: { error?: string; message?: string };
          message?: string;
        };
        message = e?.data?.error || e?.data?.message || e?.message || message;
      }
      toast.error(message);
    },
  });

  const handleOpenChatSubmit = async (link: string) => {
    try {
      console.log('오픈채팅 링크 전송:', { scheduleGroupId: group.id, link });
      toast.success('오픈채팅 링크가 회원들에게 전송되었습니다');
    } catch (error) {
      console.error('오픈채팅 링크 전송 실패:', error);
      toast.error('오픈채팅 링크 전송에 실패했습니다');
      throw error;
    }
  };

  const handleCancelSubmit = (cancelReason: string) => {
    cancelClass({ cancelReason });
  };

  return (
    <>
      <ScheduleGroupRow
        group={group}
        onClickConfirm={onClickConfirm}
        onClickApplicants={() => setApplicationsDialogOpen(true)}
        onClickDetail={() => router.push(`/partner/classes/${group.classId}`)}
        onClickCancel={() => setCancelDialogOpen(true)}
        isCancelling={isCancelling}
        onClickOpenChat={() => setOpenChatDialogOpen(true)}
      />
      <ApplicantsDialog
        open={applicationsDialogOpen}
        onOpenChange={setApplicationsDialogOpen}
        scheduleGroupId={group.id}
      />
      <OpenChatDialog
        open={openChatDialogOpen}
        onOpenChange={setOpenChatDialogOpen}
        onSubmit={handleOpenChatSubmit}
      />
      <CancelClassDialog
        open={cancelDialogOpen}
        onOpenChange={setCancelDialogOpen}
        onSubmit={handleCancelSubmit}
        isLoading={isCancelling}
        className={group.className}
      />
    </>
  );
}

type ScheduleGroupRowProps = {
  group: {
    id: number;
    classId: string;
    className: string;
    instructor: { name: string };
    schedules: { dayOfWeek: string; startTime: string; endTime: string }[];
    maxParticipants: number;
    confirmedCount: number;
    applicantCount: number;
    status: 'recruiting' | 'ongoing' | 'ended';
  };
  onClickConfirm: () => void;
  onClickApplicants: () => void;
  onClickDetail: () => void;
  onClickCancel: () => void;
  onClickOpenChat?: () => void;
  isCancelling?: boolean;
};

const mapGroupStatus = (
  status: 'recruiting' | 'ongoing' | 'ended',
  confirmedCount: number,
  maxParticipants: number
) => {
  if (status === 'recruiting')
    return `${confirmedCount}명 / ${maxParticipants}명`;
  if (status === 'ongoing') return '진행중';
  return '종료';
};

function ScheduleGroupRow({
  group,
  onClickConfirm,
  onClickApplicants,
  onClickDetail,
  onClickCancel,
  onClickOpenChat,
  isCancelling,
}: ScheduleGroupRowProps) {
  const isRecruiting = group.status === 'recruiting';
  const isOngoing = group.status === 'ongoing';
  const canConfirm = isRecruiting && group.applicantCount > 0;

  return (
    <div className='flex flex-col gap-3 rounded-md border p-3'>
      <div className='flex flex-col gap-1'>
        <div className='flex items-center justify-between'>
          <div className='text-sm text-gray-700'>
            {group.instructor.name} 코치님
          </div>
          <div className='bg-primary rounded px-2 py-1 text-xs text-white'>
            {mapGroupStatus(
              group.status,
              group.applicantCount,
              group.maxParticipants
            )}
          </div>
        </div>
        <div className='text-lg font-semibold text-black'>
          {group.className}
        </div>
      </div>
      <div className='flex flex-col gap-2 rounded-md border bg-neutral-50 px-4 py-3'>
        {group.schedules.map(s => (
          <div
            key={`${s.dayOfWeek}-${s.startTime}`}
            className='flex items-center justify-between'
          >
            <div className='text-sm text-gray-700'>
              {mapDayOfWeek(s.dayOfWeek)}요일
            </div>
            <div className='text-sm text-gray-900'>
              {s.startTime} ~ {s.endTime}
            </div>
          </div>
        ))}
      </div>

      <div className='flex flex-wrap items-center gap-2'>
        {isRecruiting && (
          <>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickCancel}
              disabled={isCancelling}
            >
              {isCancelling ? '취소 중...' : '수업 취소'}
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickApplicants}
            >
              신청자 정보 보기
            </Button>
            <Button
              variant='primaryDark'
              className='flex-1'
              onClick={onClickConfirm}
              disabled={!canConfirm}
            >
              수업 확정
            </Button>
          </>
        )}

        {isOngoing && (
          <>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickCancel}
              disabled={isCancelling}
            >
              {isCancelling ? '취소 중...' : '수업 취소'}
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickApplicants}
            >
              신청자 정보 보기
            </Button>
            <Button
              variant='outline'
              className='flex-1'
              onClick={onClickOpenChat}
            >
              오픈 채팅 링크 공유
            </Button>
          </>
        )}

        {!isRecruiting && !isOngoing && (
          <Button variant='outline' className='flex-1' onClick={onClickDetail}>
            수업 상세 보기
          </Button>
        )}
      </div>
    </div>
  );
}
