import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

type OpenChatDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (link: string) => Promise<void> | void;
  isLoading?: boolean;
};

export default function OpenChatDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: OpenChatDialogProps) {
  const [link, setLink] = useState('');

  const handleSubmit = async () => {
    if (!link.trim()) return;

    try {
      await onSubmit(link.trim());
      setLink('');
      onOpenChange(false);
    } catch (error) {
      // 에러 처리는 부모 컴포넌트에서 담당
      console.error('OpenChatDialog submit error:', error);
    }
  };

  const handleClose = () => {
    setLink('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>오픈채팅 링크 공유하기</DialogTitle>
          <DialogDescription>
            그룹수업 회원님들과 소통할 오픈채팅 링크를 올려주세요. 업로드 시
            회원분들께 참여 알림이 발송됩니다.
          </DialogDescription>
        </DialogHeader>

        <div className='flex flex-col gap-4'>
          <Input
            type='url'
            placeholder='오픈채팅 링크를 입력해주세요'
            value={link}
            onChange={e => setLink(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleClose} disabled={isLoading}>
            취소
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!link.trim() || isLoading}
            // loading={isLoading}
          >
            오픈 채팅방 보내기
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
