'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import type { GetScheduleGroupsResponse } from '@/lib/api/partner/partner.schema';
import {
  useScheduleFilters,
  type ScheduleFiltersState,
} from '@/lib/hooks/useScheduleFilters';
import { useScheduleGroups } from '@/lib/hooks/useScheduleGroups';
import type { ScheduleGroupStatus } from '@/lib/types/schedule-groups.types';
import { Suspense } from 'react';
import { ClassStatusSkeletonList } from './ClassStatusCardSkeleton';
import ScheduleGroupFilters, {
  ScheduleGroupFiltersSkeleton,
} from './ScheduleGroupFilters';
import ScheduleGroupTabContent from './ScheduleGroupTabContent';

type ScheduleGroupsClientProps = {
  initialFilters: ScheduleFiltersState;
  initialScheduleGroups: GetScheduleGroupsResponse;
};

export default function ScheduleGroupsClient({
  initialFilters,
  initialScheduleGroups,
}: ScheduleGroupsClientProps) {
  const {
    filters,
    setInstructorId,
    setCategory,
    clearAllFilters,
    setActiveStatusTab,
  } = useScheduleFilters(initialFilters);

  return (
    <div className='flex min-h-screen flex-col bg-white pt-4'>
      <div className='flex flex-col gap-2 px-3'>
        <h1 className='font-semibold text-black'>클래스 진행 현황</h1>

        <div className=''>
          <Suspense fallback={<ScheduleGroupFiltersSkeleton />}>
            <ScheduleGroupFilters
              filters={filters}
              setInstructorId={setInstructorId}
              setCategory={setCategory}
              clearAllFilters={clearAllFilters}
            />
          </Suspense>
        </div>
      </div>

      <div className='px-3'>
        <StatusTabs
          activeStatusTab={filters.activeStatusTab}
          onChange={value => setActiveStatusTab(value)}
          filters={filters}
          initialData={initialScheduleGroups}
        />
      </div>
    </div>
  );
}

function StatusTabs({
  activeStatusTab,
  onChange,
  filters,
  initialData,
}: {
  activeStatusTab: 'all' | ScheduleGroupStatus;
  onChange: (status: ScheduleGroupStatus) => void;
  filters: ScheduleFiltersState;
  initialData?: GetScheduleGroupsResponse;
}) {
  const activeTab: ScheduleGroupStatus =
    activeStatusTab === 'all' ? 'recruiting' : activeStatusTab;

  return (
    <Tabs
      value={activeTab}
      onValueChange={val => onChange(val as ScheduleGroupStatus)}
    >
      <TabsList className='h-10 w-full'>
        <TabsTrigger value='recruiting'>모집중</TabsTrigger>
        <TabsTrigger value='ongoing'>진행중</TabsTrigger>
        <TabsTrigger value='ended'>종료</TabsTrigger>
      </TabsList>

      <TabsContent value='recruiting' className='mt-2'>
        <Suspense fallback={<ContentSkeleton />}>
          <StatusTabContent
            filters={filters}
            activeTab='recruiting'
            initialData={activeTab === 'recruiting' ? initialData : undefined}
          />
        </Suspense>
      </TabsContent>

      <TabsContent value='ongoing' className='mt-2'>
        <Suspense fallback={<ContentSkeleton />}>
          <StatusTabContent
            filters={filters}
            activeTab='ongoing'
            initialData={undefined}
          />
        </Suspense>
      </TabsContent>

      <TabsContent value='ended' className='mt-2'>
        <Suspense fallback={<ContentSkeleton />}>
          <StatusTabContent
            filters={filters}
            activeTab='ended'
            initialData={undefined}
          />
        </Suspense>
      </TabsContent>
    </Tabs>
  );
}

function StatusTabContent({
  filters,
  activeTab,
  initialData,
}: {
  filters: ScheduleFiltersState;
  activeTab: ScheduleGroupStatus;
  initialData?: GetScheduleGroupsResponse;
}) {
  const {
    scheduleGroups,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    refetch,
  } = useScheduleGroups(
    { ...filters, activeStatusTab: activeTab },
    { initialData }
  );

  return (
    <ScheduleGroupTabContent
      scheduleGroups={scheduleGroups}
      hasNextPage={hasNextPage}
      isFetchingNextPage={isFetchingNextPage}
      fetchNextPage={fetchNextPage}
      refetch={refetch}
    />
  );
}

function ContentSkeleton() {
  return (
    <div className='px-3 pt-2'>
      <ClassStatusSkeletonList count={6} />
    </div>
  );
}
