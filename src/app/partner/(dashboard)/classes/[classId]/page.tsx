import PartnerClassForm from '@/components/partner/class/PartnerClassForm';
import { getClassByIdServer } from '@/lib/api/partner/class.api.server';

export default async function ClassDetailPage({
  params,
}: {
  params: Promise<{ classId: string }>;
}) {
  const { classId } = await params;
  const classData = await getClassByIdServer(classId);

  return (
    <PartnerClassForm
      mode='view'
      initialData={classData}
      classId={classId}
      studioId={classData.studio.id}
      studioName={classData.studio.name}
      instructorName={classData.instructor.name}
    />
  );
}
