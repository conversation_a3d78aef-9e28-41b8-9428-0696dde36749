import PartnerClassForm from '@/components/partner/class/PartnerClassForm';
import { serverGet } from '@/lib/api/server-fetch.server';
import type { GetClassResponse } from '@/lib/api/partner/class.schema';
import type { GetInstructorsResponse } from '@/lib/api/partner/instructor.schema';

export default async function ClassEditPage({
  params,
}: {
  params: Promise<{ classId: string }>;
}) {
  const { classId } = await params;
  const classData = await serverGet<GetClassResponse>(
    `/api/partner/classes/${classId}`
  );
  const instructors = await serverGet<GetInstructorsResponse>(
    '/api/partner/instructors'
  );

  const studioId = classData.studioId;
  const studioName = classData.studio.name;

  return (
    <PartnerClassForm
      mode='edit'
      initialData={classData}
      classId={classId}
      instructors={instructors}
      studioId={studioId}
      studioName={studioName}
    />
  );
}
