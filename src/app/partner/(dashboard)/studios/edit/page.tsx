import EditStudioClient from '@/app/partner/_components/EditStudioClient';
import type { GetStudiosResponse } from '@/lib/api/partner/studio.schema';
import { serverGet } from '@/lib/api/server-fetch.server';
import { redirect } from 'next/navigation';

export default async function EditStudioPage() {
  const studios = await serverGet<GetStudiosResponse>('/api/partner/studios');

  if (!Array.isArray(studios) || studios.length === 0) {
    return redirect('/partner/studios/new');
  }
  const studioData = studios[0];
  return (
    <>
      <EditStudioClient studioData={studioData} />
    </>
  );
}
