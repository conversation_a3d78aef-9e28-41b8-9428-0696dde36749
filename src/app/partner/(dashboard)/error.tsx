'use client';

import { useEffect } from 'react';
import ErrorPage from '@/components/ErrorPage';

export default function PartnerDashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('Partner dashboard error boundary caught:', error);
  }, [error]);

  return (
    <ErrorPage
      title='대시보드 오류'
      message='대시보드를 불러오는 중 오류가 발생했습니다. 페이지를 새로고침하거나 잠시 후 다시 시도해주세요.'
      theme='partner'
      actions={[
        {
          kind: 'button',
          label: '다시 시도',
          variant: 'outline',
          onClick: reset,
        },
        {
          kind: 'link',
          label: '파트너 홈',
          href: '/partner/dashboard',
        },
      ]}
    />
  );
}