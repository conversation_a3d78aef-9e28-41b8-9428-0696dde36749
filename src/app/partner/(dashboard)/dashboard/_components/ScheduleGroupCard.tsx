'use client';

import { useRouter } from 'next/navigation';
import { mapDayOfWeek } from '@/lib/utils/format';
import { ScheduleGroup } from '@/app/api/partner/dashboard/schema';

interface ScheduleGroupCardProps {
  group: ScheduleGroup;
}

export default function ScheduleGroupCard({ group }: ScheduleGroupCardProps) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/partner/classes/schedules`);
  };

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'recruiting':
        return { text: '모집중', className: 'bg-blue-100 text-blue-800' };
      case 'ongoing':
        return { text: '진행중', className: 'bg-green-100 text-green-800' };
      case 'ended':
        return { text: '종료', className: 'bg-gray-100 text-gray-800' };
      default:
        return { text: status, className: 'bg-gray-100 text-gray-800' };
    }
  };

  const statusDisplay = getStatusDisplay(group.status);

  return (
    <div
      onClick={handleClick}
      className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 hover:border-gray-300 hover:shadow-sm transition-all"
    >
      <div className="flex items-start justify-between mb-2">
        <h4 className="font-medium text-gray-900 text-sm line-clamp-1">
          {group.className}
        </h4>
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${statusDisplay.className}`}
        >
          {statusDisplay.text}
        </span>
      </div>

      <div className="space-y-1 text-xs text-gray-600">
        <p>강사: {group.instructor.name}</p>
        
        {group.schedules.length > 0 && (
          <div>
            {group.schedules.map((schedule, index) => (
              <p key={index}>
                {mapDayOfWeek(schedule.dayOfWeek)} {schedule.startTime.slice(0, 5)}-{schedule.endTime.slice(0, 5)}
              </p>
            ))}
          </div>
        )}

        {group.startDate && group.endDate && (
          <p>
            기간: {group.startDate} ~ {group.endDate}
          </p>
        )}

        <div className="flex items-center justify-between pt-1">
          <p>
            {group.status === 'recruiting' 
              ? `신청 ${group.applicantCount}/${group.maxParticipants}명`
              : `수강생 ${group.confirmedCount}명`
            }
          </p>
        </div>
      </div>
    </div>
  );
}