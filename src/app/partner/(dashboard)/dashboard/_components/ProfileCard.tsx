import { ClassStatusStats } from '@/lib/api/partner/dashboard.schema';
interface ProfileCardProps {
  partnerName: string;
  partnerEmail: string;
  // totalClassesCount: number;
  // monthlyClassesCount: number;
  classStats: ClassStatusStats;
}

export default function ProfileCard({
  partnerName,
  partnerEmail,
  classStats,
}: ProfileCardProps) {
  const displayName = partnerName || '파트너님';

  return (
    <div className='p-3'>
      {/* 프로필 헤더 */}
      <div className='mb-6 flex items-center'>
        {/* <div className='mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-indigo-400 to-purple-500'>
          <span className='text-xl font-bold text-white'>
            {displayName.charAt(0)}
          </span>
        </div> */}
        <div>
          <h2 className='text-xl font-bold text-black'>
            안녕하세요, {displayName}님!
          </h2>
          <p className='mt-1 text-sm text-gray-500'>
            {partnerEmail}
          </p>
          {/* <div className='flex items-center gap-2'>
            <p>
              총 수업{' '}
              <span className='text-primary font-bold'>
                {totalClassesCount}
              </span>
              개
            </p>
            <p>
              이번 주 수업{' '}
              <span className='text-primary font-bold'>
                {monthlyClassesCount}
              </span>
              회
            </p>
          </div> */}
        </div>
      </div>

      <div className='mb-4 grid grid-cols-3 gap-2'>
        <div className='rounded-md bg-blue-50 p-3 text-center'>
          <p className='text-xl font-bold text-blue-700'>
            {classStats.recruiting}
          </p>
          <p className='text-sm text-blue-600'>모집중</p>
        </div>

        <div className='rounded-md bg-green-50 p-3 text-center'>
          <p className='text-xl font-bold text-green-700'>
            {classStats.ongoing}
          </p>
          <p className='text-sm text-green-600'>진행중</p>
        </div>

        <div className='rounded-md bg-yellow-50 p-3 text-center'>
          <p className='text-xl font-bold text-yellow-700'>
            {classStats.upcoming}
          </p>
          <p className='text-sm text-yellow-600'>확정 대기</p>
        </div>
      </div>
    </div>
  );
}
