'use client';

import { usePartnerStore } from '@/app/partner/_store/partner.store';
import { type ClassStats } from '@/app/api/partner/dashboard/schema';
import { Clock, Building2, PlusIcon, Users } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

function getActionColorClasses(index: number) {
  const colorSets = [
    { bg: 'bg-green-500', icon: 'text-white' },
    { bg: 'bg-indigo-500', icon: 'text-white' },
    { bg: 'bg-blue-500', icon: 'text-white' },
    { bg: 'bg-purple-500', icon: 'text-white' },
  ];

  return colorSets[index % colorSets.length];
}
interface QuickAction {
  id: string;
  title: string;
  // description: string;
  icon: React.ReactNode;
  href: string;
  showRedDot?: boolean;
}

interface QuickActionGridProps {
  classStats: ClassStats;
}

export function createDefaultQuickActions(ctx: {
  studioId: string | null | undefined;
  hasUpcomingClasses: boolean;
}): QuickAction[] {
  return [
    {
      id: 'new-class',
      title: '수업 등록',
      // description: '새 수업 만들기',
      icon: <PlusIcon className='h-5 w-5 text-white' />,
      href: '/partner/classes',
    },
    {
      id: 'classes',
      title: '수업 일정 관리',
      // description: '수업 현황 조회',
      icon: <Clock className='h-5 w-5 text-white' />,
      href: '/partner/classes/schedules',
      showRedDot: ctx.hasUpcomingClasses,
    },

    {
      id: 'studios',
      title: '센터 관리',
      // description: '스튜디오 조회 및 수업 등록',
      icon: <Building2 className='h-5 w-5 text-white' />,
      href: ctx.studioId ? '/partner/studios/edit' : '/partner/studios/new',
    },
    {
      id: 'instructors',
      title: '강사 관리',
      // description: '강사 관리',
      icon: <Users className='h-5 w-5 text-white' />,
      href: '/partner/instructor',
    },
  ];
}

export default function QuickActionGrid({ classStats }: QuickActionGridProps) {
  const studioId = usePartnerStore(state => state.studioId);
  const hasUpcomingClasses = classStats.upcoming > 0;
  const actions = createDefaultQuickActions({ studioId, hasUpcomingClasses });

  return (
    <div className='mb-6 px-3'>
      <div className='grid grid-cols-2 gap-3'>
        {actions.map((action, index) => {
          const colorClasses = getActionColorClasses(index);

          return (
            <Link
              key={action.id}
              href={action.href}
              className='hover:border-primary rounded-md border bg-white p-3 text-left transition-colors'
            >
              <div className='flex items-center'>
                <div
                  className={`h-8 w-8 ${colorClasses.bg} relative mr-3 flex items-center justify-center rounded-lg`}
                >
                  <div className={`h-5 w-5 ${colorClasses.icon}`}>
                    {action.icon}
                  </div>
                  {action.showRedDot && (
                    <div className='absolute -top-1 -right-1 h-3 w-3 rounded-full border-2 border-white bg-red-500'></div>
                  )}
                </div>
                <span className='font-medium text-black'>{action.title}</span>
              </div>
              {/* <p className='text-sm text-gray-500'>{action.description}</p> */}
            </Link>
          );
        })}
      </div>
    </div>
  );
}
