'use client';

import { useRouter } from 'next/navigation';
import { ScheduleGroup } from '@/app/api/partner/dashboard/schema';
import ScheduleGroupCard from './ScheduleGroupCard';

interface DashboardSchedulePreviewProps {
  recruitingClasses: ScheduleGroup[];
  ongoingClasses: ScheduleGroup[];
}

export default function DashboardSchedulePreview({
  recruitingClasses,
  ongoingClasses,
}: DashboardSchedulePreviewProps) {
  const router = useRouter();

  const handleViewAll = () => {
    router.push('/partner/classes/schedules');
  };

  const handleCreateClass = () => {
    router.push('/partner/classes/new');
  };

  const hasClasses = recruitingClasses.length > 0 || ongoingClasses.length > 0;

  return (
    <section className="mb-6 px-4">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">수업 현황</h3>
        <button
          onClick={handleViewAll}
          className="text-sm text-indigo-600 hover:text-indigo-700"
        >
          전체보기
        </button>
      </div>

      {hasClasses ? (
        <div className="space-y-4">
          {/* 모집중 클래스 */}
          {recruitingClasses.length > 0 && (
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700">
                모집중 ({recruitingClasses.length}개)
              </h4>
              <div className="space-y-2">
                {recruitingClasses.slice(0, 3).map(group => (
                  <ScheduleGroupCard key={group.id} group={group} />
                ))}
                {recruitingClasses.length > 3 && (
                  <p className="text-xs text-gray-500 text-center pt-1">
                    +{recruitingClasses.length - 3}개 더
                  </p>
                )}
              </div>
            </div>
          )}

          {/* 진행중 클래스 */}
          {ongoingClasses.length > 0 && (
            <div>
              <h4 className="mb-2 text-sm font-medium text-gray-700">
                진행중 ({ongoingClasses.length}개)
              </h4>
              <div className="space-y-2">
                {ongoingClasses.slice(0, 3).map(group => (
                  <ScheduleGroupCard key={group.id} group={group} />
                ))}
                {ongoingClasses.length > 3 && (
                  <p className="text-xs text-gray-500 text-center pt-1">
                    +{ongoingClasses.length - 3}개 더
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="py-8 text-center">
          <svg
            className="mx-auto mb-4 h-12 w-12 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z"
            />
          </svg>
          <p className="mb-2 text-sm text-gray-500">
            현재 진행중인 수업이 없습니다
          </p>
          <button
            onClick={handleCreateClass}
            className="mt-2 rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 transition-colors"
          >
            수업 개설하기
          </button>
        </div>
      )}
    </section>
  );
}