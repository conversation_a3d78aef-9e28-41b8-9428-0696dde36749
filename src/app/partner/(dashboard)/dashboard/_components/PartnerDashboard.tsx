'use client';

import { type DashboardResponse } from '@/app/api/partner/dashboard/schema';
import { PartnerMeResponse } from '@/app/api/partner/me/me.schema';
import { logoutPartner } from '@/lib/api/partner/auth';
import { useRouter } from 'next/navigation';
import ProfileCard from './ProfileCard';
import QuickActionGrid from './QuickActionGrid';
import DashboardSchedulePreview from './DashboardSchedulePreview';
import { toast } from 'sonner';
import KakaoInquiry from '@/components/shared/KakaoInquiry';

interface PartnerDashboardProps {
  dashboard: DashboardResponse;
  partner: PartnerMeResponse;
}

export default function PartnerDashboard({
  dashboard,
  partner,
}: PartnerDashboardProps) {
  const router = useRouter();

  // const handleClassClick = (classId: string) => {
  //   router.push(`/partner/classes/${classId}`);
  // };

  // const handleViewAllClasses = () => {
  //   router.push('/partner/classes');
  // };

  const handleLogout = async () => {
    try {
      const result = await logoutPartner();
      if (result.success) {
        router.push('/partner/login');
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('로그아웃 오류:', error);
      toast.error('로그아웃 중 오류가 발생했습니다.');
    }
  };

  // const displayName = dashboard.partner.name;
  return (
    <>
      <div className=''>
        {/* 프로필 카드 */}
        <ProfileCard
          partnerName={partner.name}
          partnerEmail={partner.email}
          // totalClassesCount={dashboard.todayClasses.length}
          // monthlyClassesCount={dashboard.thisWeekClasses.length}
          classStats={dashboard.classStats}
        />

        <QuickActionGrid classStats={dashboard.classStats} />

        <DashboardSchedulePreview
          recruitingClasses={dashboard.recruitingClasses}
          ongoingClasses={dashboard.ongoingClasses}
        />

        {/* 로그아웃 버튼 */}
        <div className='mb-6 px-4'>
          <button
            onClick={handleLogout}
            className='w-full rounded-md border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50'
          >
            로그아웃
          </button>
        </div>
      </div>
      <KakaoInquiry />
    </>
  );
}
