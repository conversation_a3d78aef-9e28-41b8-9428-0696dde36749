import PartnerDashboard from './_components/PartnerDashboard';
import { serverGet } from '@/lib/api/server-fetch.server';
import {
  DashboardResponseSchema,
  type DashboardResponse,
} from '@/app/api/partner/dashboard/schema';
import { PartnerMeResponseSchema } from '@/app/api/partner/me/me.schema';

export default async function PartnerDashboardPage() {
  console.log('[PARTNER_DASHBOARD] 페이지 로드 시작');
  
  try {
    console.log('[PARTNER_DASHBOARD] 대시보드 데이터 요청 시작');
    const dashboard = await serverGet<DashboardResponse>(
      '/api/partner/dashboard',
      {},
      DashboardResponseSchema
    );
    console.log('[PARTNER_DASHBOARD] 대시보드 데이터 요청 완료');

    console.log('[PARTNER_DASHBOARD] 파트너 정보 요청 시작');
    const partner = await serverGet(
      '/api/partner/me',
      {},
      PartnerMeResponseSchema
    );
    console.log('[PARTNER_DASHBOARD] 파트너 정보 요청 완료');

    console.log('[PARTNER_DASHBOARD] 페이지 렌더링 시작');
    return <PartnerDashboard dashboard={dashboard} partner={partner} />;
  } catch (error) {
    console.error('[PARTNER_DASHBOARD] 페이지 로드 실패:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
