export interface InstructorListItem {
  id: string;
  name: string;
  profileImageUrl?: string; // deprecated - use profileImages instead
  profileImages?: Array<{
    url: string;
    path: string;
    thumbnail?: {
      url: string;
      path: string;
    };
  }>;
}

export interface ClassListItem {
  id: string;
  title: string;
  level: string;
  scheduleGroupCount: number;
  sessionsPerWeek: number;
  durationWeeks: number;
  pricePerSession: number;
  maxParticipants: number;
  category: string;
  visible: boolean | null;
  status: string;
  createdAt: string | null;
  updatedAt: string | null;
  studioName: string | null;
  instructorName: string | null;
  enrollmentCount: number;
}
