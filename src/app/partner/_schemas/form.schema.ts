import { CreateInstructorSchema } from '@/lib/schemas/instructor';
import { CreateStudioSchema } from '@/lib/schemas/studio';
import z from 'zod';

export const formImageSchema = z.object({
  fileId: z.string(),
  file: z.instanceof(File).optional(),
  url: z.string().optional(),
  path: z.string().optional(),
  needsServerConversion: z.boolean().optional(),
  cropArea: z.object({
    x: z.number(),
    y: z.number(),
    width: z.number(),
    height: z.number(),
    unit: z.enum(['px', '%']),
  }).optional(),
  thumbnail: z.object({
    url: z.string(),
    path: z.string(),
  }).optional(),
});

export type FormImage = z.infer<typeof formImageSchema>;

export const studioFormSchema = CreateStudioSchema.omit({
  partnerId: true,
  images: true,
  stationDistance: true,
  nearestStation: true,
  latitude: true,
  longitude: true,
  operatingHours: true,
}).extend({
  images: z
    .array(formImageSchema)
    .max(10, '이미지는 최대 10개까지 업로드 가능합니다')
    .optional(),
  phone: z.string().min(1, '전화번호를 입력해주세요'),
  links: z.object({
    website: z.union([z.url(), z.literal('')]),
    sns: z.union([z.url(), z.literal('')]),
  }),
  // operatingHours: z
  //   .object({
  //     monday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     tuesday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     wednesday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     thursday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     friday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     saturday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //     sunday: z.object({
  //       open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
  //       closed: z.boolean(),
  //     }),
  //   })
  //   .optional(),
});

export type StudioFormData = z.infer<typeof studioFormSchema>;

// Edit용 스키마 - 모든 필드를 optional로 만듦
export const editStudioFormSchema = studioFormSchema.partial();
export type EditStudioFormData = z.infer<typeof editStudioFormSchema>;

export const instructorFormSchema = CreateInstructorSchema.omit({
  profileImages: true,
  links: true,
}).extend({
  links: z.object({
    website: z.union([z.url(), z.literal('')]).optional(),
    sns: z.union([z.url(), z.literal('')]).optional(),
  }),
  profileImages: z
    .array(formImageSchema)
    .min(1, '대표 사진은 1장 등록해야 합니다')
    .max(1, '이미지는 최대 1개까지 업로드 가능합니다'),
});

export type InstructorFormData = z.infer<typeof instructorFormSchema>;
