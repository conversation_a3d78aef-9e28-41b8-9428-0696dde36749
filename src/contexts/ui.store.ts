'use client';

import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';

export type UIOverlayKey = 'InquireDialog' | 'LogoutDialog';

interface UIState {
  openMap: Record<UIOverlayKey, boolean>;
}

const initialState: UIState = {
  openMap: {
    InquireDialog: false,
    LogoutDialog: false,
  },
};

export const useUIStore = create(
  devtools(
    combine(initialState, set => {
      const actions = {
        setOpen: (key: UIOverlayKey, open: boolean) => {
          set(
            state => ({
              openMap: { ...state.openMap, [key]: open },
            }),
            false,
            `setOpen:${key}:${open ? 'open' : 'close'}`
          );
        },
        toggleOpen: (key: UIOverlayKey) => {
          set(
            state => ({
              openMap: { ...state.openMap, [key]: !state.openMap[key] },
            }),
            false,
            `toggleOpen:${key}`
          );
        },
        reset: () => {
          set(initialState, false, 'reset');
        },
      };

      return actions;
    }),
    {
      name: 'ui-store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

export const uiStore = useUIStore.getState();
export const uiStoreActions = {
  setOpen: (key: UIOverlayKey, open: boolean) =>
    useUIStore.getState().setOpen(key, open),
  toggleOpen: (key: UIOverlayKey) => useUIStore.getState().toggleOpen(key),
  reset: () => useUIStore.getState().reset(),
};
