'use client';

import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';

export interface UserState {
  id: string | null;
  email: string | null;
}

const initialState: UserState = {
  id: null,
  email: null,
};

export const useUserStore = create(
  devtools(
    combine(initialState, set => {
      const actions = {
        setUser: (user: Partial<UserState>) => {
          set(user, false, 'setUser');
        },

        setUserField: <K extends keyof UserState>(
          field: K,
          value: UserState[K]
        ) => {
          set({ [field]: value }, false, `setUserField:${field}`);
        },

        reset: () => {
          set(initialState, false, 'reset');
        },
      };

      return actions;
    }),
    {
      name: 'user-store', // DevTools에서 표시될 이름
      enabled: process.env.NODE_ENV === 'development', // 개발 환경에서만 활성화
    }
  )
);

export const userStoreActions = {
  setUser: useUserStore.getState().setUser,
  setUserField: useUserStore.getState().setUserField,
  reset: useUserStore.getState().reset,
};
