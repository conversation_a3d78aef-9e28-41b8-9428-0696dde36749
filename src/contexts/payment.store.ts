'use client';

import { create } from 'zustand';
import {
  combine,
  devtools,
  persist,
  createJSONStorage,
} from 'zustand/middleware';
import { ClassSchedulesResponse } from '@/app/api/classes/schema';

export interface PaymentInfo {
  classData: ClassSchedulesResponse;
  selectedScheduleGroupId: string;
  enrollment: {
    id: string;
    status: 'pending' | 'paid' | 'confirmed' | 'cancelled' | 'refunded';
    totalAmount: number;
    depositAmount: number;
  };
  paymentWidget: {
    clientKey: string;
    amount: number;
    orderId: string;
    orderName: string;
    customerName: string;
    successUrl: string;
    failUrl: string;
  };
}

interface PaymentState {
  info: PaymentInfo | null;
}

const initialState: PaymentState = {
  info: null,
};

export const usePaymentStore = create(
  persist(
    devtools(
      combine(initialState, set => {
        const actions = {
          setPaymentInfo: (info: PaymentInfo) => {
            set({ info }, false, 'setPaymentInfo');
          },
          reset: () => {
            set(initialState, false, 'reset');
          },
        };

        return actions;
      }),
      {
        name: 'payment-store-devtools',
        enabled: process.env.NODE_ENV === 'development',
      }
    ),
    {
      name: 'payment-store',
      storage: createJSONStorage(() => sessionStorage),
      version: 1,
      partialize: state => ({ info: state.info }),
    }
  )
);

export const paymentStoreActions = {
  setPaymentInfo: (info: PaymentInfo) =>
    usePaymentStore.getState().setPaymentInfo(info),
  reset: () => usePaymentStore.getState().reset(),
};
