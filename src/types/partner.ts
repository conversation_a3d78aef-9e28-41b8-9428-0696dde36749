/**
 * 파트너 관련 TypeScript 타입 정의
 * 
 * @description
 * - 파트너 회원가입, 로그인, 관리와 관련된 모든 타입
 * - API 요청/응답 타입
 * - 데이터베이스 모델 타입
 */

import type { Partner, PartnerStatus } from '@/lib/db/schema';

// ===== 데이터베이스 모델 타입 =====

/**
 * 파트너 엔티티
 * database schema에서 추출한 타입을 재export
 */
export type PartnerEntity = Partner;

/**
 * 파트너 상태 타입
 */
export type PartnerStatusType = PartnerStatus;

/**
 * 파트너 생성 데이터 (INSERT용)
 */
export interface CreatePartnerData {
  user_id: string;
  contact_name: string;
  contact_phone: string;
  status?: PartnerStatusType;
}

/**
 * 파트너 업데이트 데이터 (UPDATE용)
 */
export interface UpdatePartnerData {
  contact_name?: string;
  contact_phone?: string;
  status?: PartnerStatusType;
  updated_at?: Date;
}

// ===== API 요청/응답 타입 =====

/**
 * 파트너 회원가입 요청
 */
export interface PartnerRegisterRequest {
  email: string;
  password: string;
  contactInfo: {
    name: string;
    phone: string;
  };
}

/**
 * 파트너 회원가입 성공 응답
 */
export interface PartnerRegisterSuccessResponse {
  success: true;
  message: string;
  data: {
    partnerId: string;
    status: PartnerStatusType;
  };
}

/**
 * 파트너 회원가입 실패 응답
 */
export interface PartnerRegisterErrorResponse {
  success: false;
  message: string;
  error?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

/**
 * 파트너 회원가입 응답 (유니온 타입)
 */
export type PartnerRegisterResponse = 
  | PartnerRegisterSuccessResponse 
  | PartnerRegisterErrorResponse;

/**
 * 파트너 로그인 요청
 */
export interface PartnerLoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * 파트너 로그인 성공 응답
 */
export interface PartnerLoginSuccessResponse {
  success: true;
  message: string;
  data: {
    user: {
      id: string;
      email: string;
    };
    partner: {
      id: string;
      contact_name: string;
      contact_phone: string;
      status: PartnerStatusType;
    };
    redirectUrl: string;
  };
}

/**
 * 파트너 로그인 실패 응답
 */
export interface PartnerLoginErrorResponse {
  success: false;
  message: string;
  error?: string;
}

/**
 * 파트너 로그인 응답 (유니온 타입)
 */
export type PartnerLoginResponse = 
  | PartnerLoginSuccessResponse 
  | PartnerLoginErrorResponse;

/**
 * 파트너 상태 확인 요청
 */
export type PartnerCheckStatusRequest = Record<string, never>;

/**
 * 파트너 상태 확인 성공 응답
 */
export interface PartnerCheckStatusSuccessResponse {
  success: true;
  data: {
    partnerId: string;
    status: PartnerStatusType;
    message?: string;
  };
}

/**
 * 파트너 상태 확인 실패 응답
 */
export interface PartnerCheckStatusErrorResponse {
  success: false;
  message: string;
}

/**
 * 파트너 상태 확인 응답 (유니온 타입)
 */
export type PartnerCheckStatusResponse = 
  | PartnerCheckStatusSuccessResponse 
  | PartnerCheckStatusErrorResponse;

/**
 * 파트너 프로필 조회 응답
 */
export interface PartnerProfileResponse {
  success: boolean;
  data?: {
    id: string;
    user_id: string;
    contact_name: string;
    contact_phone: string;
    status: PartnerStatusType;
    created_at: string;
    updated_at: string;
  };
  error?: string;
}

/**
 * 파트너 프로필 업데이트 요청
 */
export interface PartnerProfileUpdateRequest {
  contact_name?: string;
  contact_phone?: string;
}

// ===== 폼 데이터 타입 =====

/**
 * 파트너 회원가입 폼 데이터
 */
export interface PartnerSignupFormData {
  // 계정 정보
  email: string;
  password: string;
  passwordConfirm: string;
  
  // 담당자 정보
  contactInfo: {
    name: string;
    phone: string;
  };
}

/**
 * 파트너 회원가입 폼 에러
 */
export interface PartnerSignupFormErrors {
  email?: string;
  password?: string;
  passwordConfirm?: string;
  'contactInfo.name'?: string;
  'contactInfo.phone'?: string;
  general?: string;
}

/**
 * 파트너 로그인 폼 데이터
 */
export interface PartnerLoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * 파트너 로그인 폼 에러
 */
export interface PartnerLoginFormErrors {
  email?: string;
  password?: string;
  general?: string;
}

// ===== 유틸리티 타입 =====

/**
 * API 에러 객체
 */
export interface ApiError {
  field: string;
  message: string;
}

/**
 * 파트너 상태별 표시 텍스트
 */
export const PartnerStatusDisplayText: Record<PartnerStatusType, string> = {
  PENDING: '승인 대기',
  ACTIVE: '활성',
  SUSPENDED: '정지',
  REJECTED: '거부',
} as const;

/**
 * 파트너 상태별 색상 클래스 (Tailwind CSS)
 */
export const PartnerStatusColorClass: Record<PartnerStatusType, string> = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  ACTIVE: 'bg-green-100 text-green-800',
  SUSPENDED: 'bg-red-100 text-red-800',
  REJECTED: 'bg-gray-100 text-gray-800',
} as const;

// ===== 타입 가드 함수 =====

/**
 * 파트너 회원가입 성공 응답인지 확인
 */
export function isPartnerRegisterSuccess(
  response: PartnerRegisterResponse
): response is PartnerRegisterSuccessResponse {
  return response.success === true;
}

/**
 * 파트너 로그인 성공 응답인지 확인
 */
export function isPartnerLoginSuccess(
  response: PartnerLoginResponse
): response is PartnerLoginSuccessResponse {
  return response.success === true;
}

/**
 * 활성 상태 파트너인지 확인
 */
export function isActivePartner(partner: PartnerEntity): boolean {
  return partner.status === 'ACTIVE';
}

/**
 * 승인 대기 상태 파트너인지 확인
 */
export function isPendingPartner(partner: PartnerEntity): boolean {
  return partner.status === 'PENDING';
}

// ===== 기본값 상수 =====

/**
 * 기본 파트너 상태
 */
export const DEFAULT_PARTNER_STATUS: PartnerStatusType = 'PENDING';

/**
 * 파트너 연락처 정규식 (서버용 - 숫자만 11자리)
 */
export const PARTNER_PHONE_REGEX = /^010\d{8}$/;

/**
 * 파트너 연락처 정규식 (프론트엔드용 - 하이픈 포함)
 */
export const PARTNER_PHONE_DISPLAY_REGEX = /^010-\d{4}-\d{4}$/;

/**
 * 파트너 담당자명 정규식 (한글, 영문, 공백만)
 */
export const PARTNER_NAME_REGEX = /^[가-힣a-zA-Z\s]+$/;

// ===== 대시보드 관련 타입 =====

/**
 * 대시보드 접근을 위한 파트너 정보
 */
export interface PartnerDashboardUser {
  id: string;
  user_id: string;
  contact_name: string;
  status: PartnerStatusType;
}

/**
 * 파트너 대시보드 권한 확인
 */
export interface PartnerDashboardPermission {
  canAccessDashboard: boolean;
  canManageClasses: boolean;
  canManageInstructors: boolean;
  canViewReports: boolean;
  restrictions?: string[];
}

/**
 * 파트너 대시보드 설정
 */
export interface PartnerDashboardSettings {
  timezone: string;
  refreshInterval: number;
  notificationPreferences: {
    newBookings: boolean;
    reviews: boolean;
    payments: boolean;
    cancellations: boolean;
  };
}