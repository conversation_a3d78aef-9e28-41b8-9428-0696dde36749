# 카카오 OAuth Dev/Prod 환경 분리 계획서

## 📋 현재 구현 상황 분석

### 현재 로그인 시스템 구조

#### 1. 일반 사용자 (Members) 로그인
- **경로**: `/login` → `KakaoButton` 컴포넌트
- **방식**: 카카오 OAuth 전용
- **플로우**: 
  ```
  /login → KakaoButton.onClick → signInWithKakao() → 카카오 OAuth → /auth/callback → 사용자 타입 판단 후 리다이렉트
  ```

#### 2. 파트너 (Partners) 로그인  
- **경로**: `/partner/login` → `PartnerLoginForm` 컴포넌트
- **방식**: 이메일/비밀번호 전용
- **플로우**:
  ```
  /partner/login → PartnerLoginForm → Server Action → 직접 인증 → /partner/dashboard
  ```

### 현재 OAuth 구현 코드

#### 핵심 파일 및 코드 위치

1. **OAuth 로직**: `src/lib/supabase/auth.ts:41-60`
   ```typescript
   export async function signInWithKakao(redirectTo: string = '/') {
     const { data, error } = await supabase.auth.signInWithOAuth({
       provider: 'kakao',
       options: {
         redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`,
       },
     });
   }
   ```

2. **콜백 처리**: `src/app/auth/callback/route.ts:5-88`
   ```typescript
   export async function GET(request: NextRequest) {
     const redirectTo = requestUrl.searchParams.get('redirect_to') || '/';
     // 인증 코드 교환 → 사용자 타입 판단 → 최종 리다이렉트
   }
   ```

3. **카카오 버튼**: `src/components/auth/KakaoButton.tsx:22-33`
   ```typescript
   const handleKakaoLogin = async () => {
     await signInWithKakao(redirectTo);
   };
   ```

4. **환경 설정**: `src/lib/api/api.config.ts:1`
   ```typescript
   export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
   ```

## 🚨 현재 문제점 분석

### 1. 카카오 정책 위반 문제

**문제**: `src/lib/supabase/auth.ts:46`의 redirect_to 파라미터
```typescript
redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`
```

**카카오 개발자 문서 규칙**:
- "리다이렉트 URI는 경로(path)에 임의의 파라미터를 포함할 수 없습니다"
- 출처: https://developers.kakao.com/docs/latest/ko/kakaologin/prerequisite#kakao-login-redirect-uri

**영향**: 프로덕션 환경에서 카카오 로그인 실패 가능성

### 2. Dev/Prod 환경 미분리 문제

**현재 상황**:
- 단일 Supabase 프로젝트 사용
- 단일 카카오 앱 키 사용  
- `window.location.origin` 의존으로 런타임 URL 결정

**문제점**:
- 개발/운영 환경 격리 불가
- 테스트 중 프로덕션 데이터 영향 가능
- 환경별 다른 도메인/포트 처리 어려움

### 3. 파트너 로그인 제한

**현재**: 파트너는 이메일/비밀번호만 사용 가능
**잠재 요구사항**: 파트너도 카카오 로그인 원할 수 있음

## 🎯 해결 방안 및 구현 계획

### 1단계: 카카오 정책 준수 (최우선)

#### 목표
- `redirect_to` 파라미터를 `state` 파라미터로 이동
- 카카오 Redirect URI에서 모든 파라미터 제거

#### 수정 파일
1. **`src/lib/supabase/auth.ts`**
   ```typescript
   // Before
   redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`
   
   // After  
   redirectTo: `${window.location.origin}/auth/callback`,
   queryParams: {
     state: encodeURIComponent(JSON.stringify({
       redirectTo,
       userType: 'member', // 또는 'partner'
       timestamp: Date.now()
     }))
   }
   ```

2. **`src/app/auth/callback/route.ts`**
   ```typescript
   // Before
   const redirectTo = requestUrl.searchParams.get('redirect_to') || '/';
   
   // After
   const state = requestUrl.searchParams.get('state');
   const { redirectTo, userType } = state ? JSON.parse(decodeURIComponent(state)) : { redirectTo: '/', userType: 'member' };
   ```

### 2단계: 환경 설정 분리

#### 새 파일 생성

1. **`src/lib/config/environment.ts`**
   ```typescript
   export const ENV = {
     isDev: process.env.NODE_ENV === 'development',
     isProd: process.env.NODE_ENV === 'production',
     baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
     supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
     kakaoApiKey: process.env.NEXT_PUBLIC_KAKAO_REST_API_KEY,
   };
   
   export const getAuthCallbackUrl = () => {
     return `${ENV.baseUrl}/auth/callback`;
   };
   ```

2. **`.env.development`**
   ```
   # 개발 환경 전용
   NEXT_PUBLIC_BASE_URL=http://localhost:3000
   NEXT_PUBLIC_SUPABASE_URL=https://dev-project.supabase.co
   NEXT_PUBLIC_KAKAO_REST_API_KEY=dev_kakao_key
   ```

3. **`.env.production`**
   ```
   # 프로덕션 환경 전용  
   NEXT_PUBLIC_BASE_URL=https://yourdomain.com
   NEXT_PUBLIC_SUPABASE_URL=https://prod-project.supabase.co
   NEXT_PUBLIC_KAKAO_REST_API_KEY=prod_kakao_key
   ```

#### 수정 파일

1. **`src/lib/supabase/auth.ts`**
   ```typescript
   import { getAuthCallbackUrl } from '@/lib/config/environment';
   
   export async function signInWithKakao(redirectTo: string = '/', userType: 'member' | 'partner' = 'member') {
     const state = JSON.stringify({ redirectTo, userType, timestamp: Date.now() });
     
     const { data, error } = await supabase.auth.signInWithOAuth({
       provider: 'kakao',
       options: {
         redirectTo: getAuthCallbackUrl(),
         queryParams: {
           state: encodeURIComponent(state)
         }
       },
     });
   }
   ```

### 3단계: 카카오 개발자 콘솔 설정

#### 개발용 앱 등록
- **앱 이름**: ShallWe-Dev
- **Redirect URI**: `http://localhost:3000/auth/callback`
- **도메인**: `localhost:3000`

#### 운영용 앱 등록  
- **앱 이름**: ShallWe-Prod
- **Redirect URI**: `https://yourdomain.com/auth/callback`
- **도메인**: `yourdomain.com`

### 4단계: Supabase 프로젝트 분리

#### 개발용 프로젝트
- 새 프로젝트 생성: `shallwe-dev`
- 개발용 데이터베이스 스키마 복사
- Auth 설정에 개발용 카카오 앱 연결

#### 운영용 프로젝트
- 기존 프로젝트 유지: `shallwe-prod`  
- Auth 설정에 운영용 카카오 앱 연결

### 5단계: 파트너 카카오 로그인 추가 (선택사항)

#### 새 컴포넌트 생성
**`src/components/auth/PartnerKakaoButton.tsx`**
```typescript
export function PartnerKakaoButton({ redirectTo = '/partner/dashboard' }: PartnerKakaoButtonProps) {
  const handleKakaoLogin = async () => {
    await signInWithKakao(redirectTo, 'partner');
  };
  // ... 기존 KakaoButton과 유사한 구조
}
```

#### 콜백 처리 확장
**`src/app/auth/callback/route.ts`**
```typescript
// state에서 userType 추출 후 적절한 경로로 리다이렉트
if (userType === 'partner') {
  // 파트너 테이블에서 확인 후 파트너 대시보드로
  return NextResponse.redirect(new URL('/partner/dashboard', requestUrl.origin));
} else {
  // 기존 멤버 로직
}
```

## 🧪 테스트 계획

### 환경별 테스트 시나리오

#### 개발 환경 테스트
1. `npm run dev` 실행
2. `http://localhost:3000/login` 접속
3. 카카오 로그인 클릭
4. 개발용 카카오 앱으로 인증
5. 콜백 처리 후 적절한 페이지 이동 확인

#### 프로덕션 환경 테스트  
1. 배포 후 `https://yourdomain.com/login` 접속
2. 카카오 로그인 클릭
3. 운영용 카카오 앱으로 인증
4. 콜백 처리 후 적절한 페이지 이동 확인

### 리그레션 체크리스트

- [ ] 기존 회원 로그인 정상 동작
- [ ] 파트너 이메일 로그인 정상 동작  
- [ ] 신규 회원가입 플로우 정상 동작
- [ ] 필수 정보 미입력 시 `/signup/required` 리다이렉트
- [ ] 파트너 상태별 적절한 페이지 이동
- [ ] 개발/운영 환경별 올바른 설정 적용

## 📝 구현 우선순위

### High Priority (즉시 필요)
1. **1단계**: 카카오 정책 준수 - state 파라미터 방식
2. **2단계**: 환경별 설정 분리

### Medium Priority (dev/prod 분리 완료 후)  
3. **3-4단계**: 카카오 앱 및 Supabase 프로젝트 분리

### Low Priority (기능 확장)
4. **5단계**: 파트너 카카오 로그인 추가

## 🔄 마이그레이션 전략

### 점진적 배포 방안
1. **1단계 먼저 배포**: state 파라미터 방식으로 변경
2. **호환성 유지**: 기존 `redirect_to` 파라미터도 임시 지원
3. **검증 후 완전 전환**: 신규 방식 안정화 후 기존 방식 제거

### 롤백 계획
- 각 단계별 브랜치 생성으로 빠른 롤백 가능
- 환경 변수만 변경으로 이전 설정 복원 가능

---

**작성일**: 2025-08-17  
**작성자**: Claude Code Assistant  
**상태**: 계획 단계