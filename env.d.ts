declare namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_SUPABASE_URL: string;
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
      SUPABASE_SERVICE_ROLE_KEY: string;
      NEXT_PUBLIC_NAVER_MAP_API_KEY: string;
      NEXT_PUBLIC_KAKAO_REST_API_KEY: string;
      KAKAO_CLIENT_SECRET: string;
      NEXT_PUBLIC_PORTONE_USER_CODE: string;
      PORTONE_API_KEY: string;
      PORTONE_API_SECRET: string;
      NEXT_PUBLIC_BASE_URL: string;
      NODE_ENV: string;
      ADMIN_EMAILS: string;
      NEXT_PUBLIC_NAVER_MAP_CLIENT_ID: string;
      KAKAO_MAP_API_KEY: string;
      // 토스페이먼츠 결제위젯용 키 (v2 위젯에 사용)
      NEXT_PUBLIC_TOSS_WIDGET_CLIENT_KEY: string;
      TOSS_WIDGET_SECRET_KEY: string;
      // 토스페이먼츠 API 개별 연동 키 (레거시/API용)
      NEXT_PUBLIC_TOSS_CLIENT_KEY: string;
      TOSS_SECRET_KEY: string;
      NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY: string;
      // Google Analytics
      NEXT_PUBLIC_GA_ID: string;
      NEXT_PUBLIC_KAKAO_MAP_API_KEY: string;
      DATABASE_URL: string;
    }
  }

