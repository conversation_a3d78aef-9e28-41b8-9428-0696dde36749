{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:inspect": "NODE_OPTIONS='--inspect' next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "check-types": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:introspect": "drizzle-kit introspect", "db:drop": "drizzle-kit drop"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@next/third-parties": "^15.4.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tosspayments/tosspayments-sdk": "^2.3.5", "@types/react-image-crop": "^8.1.6", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dot-path-value": "^0.0.11", "drizzle-orm": "^0.44.2", "embla-carousel-react": "^8.6.0", "heic2any": "^0.0.4", "immer": "^10.1.1", "input-otp": "^1.4.2", "isbot": "^5.1.28", "lodash.groupby": "^4.6.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "postgres": "^3.4.7", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-number-format": "^5.4.4", "react-resizable-panels": "^3.0.3", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@hookform/devtools": "^4.4.0", "@storybook/nextjs": "^9.0.16", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.83.0", "@types/lodash.groupby": "^4.6.9", "@types/navermaps": "^3.9.1", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "dotenv": "^17.2.0", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^9.0.16", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "storybook": "^9.0.16", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "5.9.2", "vite": "6.3.5", "vite-plugin-devtools-json": "^0.2.1", "vite-tsconfig-paths": "^5.1.4"}}